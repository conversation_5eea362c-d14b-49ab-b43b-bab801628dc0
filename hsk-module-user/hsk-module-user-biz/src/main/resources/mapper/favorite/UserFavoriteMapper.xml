<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.user.dal.mysql.favorite.UserFavoriteMapper">

    <!-- 分页查询收藏的小游戏题目列表 -->
    <select id="selectFavoriteGameQuestionsPage"
            resultType="com.xt.hsk.module.user.controller.app.favorite.vo.FavoriteGameQuestionsAppPageRespVO">
      SELECT
      gq.id AS questionId,
      gq.question_content AS questionContent,
      gq.hsk_level AS hskLevel,
      gq.audio_url AS audioUrl,
      uf.favorite_time AS favoriteTime,
      uf.is_wrong_question AS isWrongQuestion
      FROM user_favorite uf
      left JOIN game_question gq ON uf.target_id = gq.id and uf.favorite_type = 3
      left JOIN game_special_exercise gse ON uf.game_id = gse.id
      WHERE uf.deleted = 0
      AND gq.deleted = 0
      <if test="req.userId != null">
        AND uf.user_id = #{req.userId}
      </if>
      <if test="req.type != null">
        AND gq.type = #{req.type}
      </if>
      <if test="req.hskLevel != null">
        AND gq.hsk_level = #{req.hskLevel}
      </if>
      <if test="req.questionContent != null and req.questionContent != ''">
        AND gq.question_content LIKE CONCAT('%', #{req.questionContent}, '%')
      </if>
      <if test="req.difficultyLevels != null and req.difficultyLevels.size() > 0">
        AND gse.difficulty_level IN
        <foreach collection="req.difficultyLevels" item="level" open="(" close=")" separator=",">
          #{level}
        </foreach>
      </if>
      <if test="req.isWrongQuestion != null">
        AND uf.is_wrong_question = #{req.isWrongQuestion}
      </if>
      <if test="req.collectionTimeType != null">
        <choose>
          <when test="req.collectionTimeType == 1">
            AND uf.favorite_time >= DATE_SUB(NOW(), INTERVAL 3 DAY)
          </when>
          <when test="req.collectionTimeType == 2">
            AND uf.favorite_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
          </when>
          <when test="req.collectionTimeType == 3">
            AND uf.favorite_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
          </when>
        </choose>
      </if>
      ORDER BY uf.favorite_time DESC
    </select>

    <select id="favoriteQuestionList"
            resultType="com.xt.hsk.module.user.controller.app.favorite.vo.FavoriteQuestionPageRespVO">
        SELECT uf.id               AS favorite_id,
               uf.favorite_time,
               uf.is_wrong_question,
               uf.target_id        AS question_id,
               uf.target_detail_id AS question_detail_id,
               q.type_id           AS question_type_id,
               q.textbook_id,
               q.chapter_id,
               u.sort
        FROM user_favorite uf
                 LEFT JOIN edu_question q ON uf.target_id = q.id
                 LEFT JOIN edu_unit u ON q.unit_id = u.id
        WHERE uf.user_id = #{req.userId}
          AND uf.favorite_type = 2
          AND q.hsk_level = #{req.hskLevel}
          AND q.`subject` = #{req.subject}
        <if test="req.isWrongQuestion != null">
            AND uf.is_wrong_question = #{req.isWrongQuestion}
        </if>
        <if test="req.collectionTimeType != null">
            <choose>
                <when test="req.collectionTimeType == 1">
                    AND uf.favorite_time >= DATE_SUB(NOW(), INTERVAL 3 DAY)
                </when>
                <when test="req.collectionTimeType == 2">
                    AND uf.favorite_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                </when>
                <when test="req.collectionTimeType == 3">
                    AND uf.favorite_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                </when>
            </choose>
        </if>
        ORDER BY u.sort, uf.id DESC
    </select>

    <select id="wordBookList" resultType="com.xt.hsk.module.user.controller.app.favorite.vo.FavoriteWordAppPageRespVO">
        SELECT uf.id        AS favorite_id,
               uf.favorite_time,
               uf.is_wrong_question,
               uf.target_id AS word_id,
               w.word,
               w.pinyin,
               w.hsk_level,
               w.is_special,
               w.audio_url
        FROM user_favorite uf
                 LEFT JOIN edu_word w ON uf.target_id = w.id
        WHERE uf.deleted = 0
          AND w.deleted = 0
          AND uf.user_id = #{req.userId}
          AND uf.favorite_type = 1
        <if test="req.hskLevelList != null and req.hskLevelList.size() > 0">
            AND (
            <foreach collection="req.hskLevelList" item="level" separator=" OR ">
                (w.hsk_level &amp; #{level} != 0)
            </foreach>
            )
        </if>
        <if test="req.collectionTimeType != null">
            <choose>
                <when test="req.collectionTimeType == 1">
                    AND uf.favorite_time >= DATE_SUB(NOW(), INTERVAL 3 DAY)
                </when>
                <when test="req.collectionTimeType == 2">
                    AND uf.favorite_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                </when>
                <when test="req.collectionTimeType == 3">
                    AND uf.favorite_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                </when>
            </choose>
        </if>
    </select>
</mapper>
