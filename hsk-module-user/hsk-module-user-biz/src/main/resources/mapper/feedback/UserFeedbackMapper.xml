<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.user.dal.mysql.feedback.UserFeedbackMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap"
    type="com.xt.hsk.module.user.dal.dataobject.feedback.UserFeedbackDO">
    <id column="id" property="id"/>
    <result column="user_id" property="userId"/>
    <result column="content" property="content"/>
    <result column="country_code" property="countryCode"/>
    <result column="operate_ip" property="operateIp"/>
    <result column="contact_info_type" property="contactInfoType"/>
    <result column="contact_info" property="contactInfo"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="deleted" property="deleted"/>
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id, user_id, content, country_code, operate_ip, contact_info_type, contact_info,
        create_time, update_time, deleted
  </sql>

</mapper> 