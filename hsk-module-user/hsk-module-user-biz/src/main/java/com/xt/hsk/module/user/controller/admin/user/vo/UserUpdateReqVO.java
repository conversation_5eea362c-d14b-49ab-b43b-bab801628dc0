package com.xt.hsk.module.user.controller.admin.user.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 管理后台 - 用户更新 Request VO
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Data
public class UserUpdateReqVO {

    /**
     * 用户ID 必填，示例值：1
     */
    @NotNull(message = "用户ID不能为空")
    private Long id;

    /**
     * 用户昵称
     * 必填，示例值：张三
     * 最长支持30个中文字符或45个英文/数字字符
     */
    @NotBlank(message = "用户昵称不能为空")
    @Size(max = 45, message = "用户昵称长度不能超过45个字符(最多30个中文字符)")
    private String nickname;

    /**
     * 手机号码
     * 必填，示例值：13888888888
     */
    @NotBlank(message = "手机号码不能为空")
    @Size(max = 20, message = "手机号码长度不能超过20个字符")
    private String mobile;

    /**
     * 手机区号
     * 必填，示例值：86
     */
    @NotNull(message = "手机区号不能为空")
    private String countryCode;

} 