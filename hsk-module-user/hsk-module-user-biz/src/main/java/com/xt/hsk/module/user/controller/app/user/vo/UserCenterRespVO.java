package com.xt.hsk.module.user.controller.app.user.vo;

import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户中心信息 resp vo
 *
 * <AUTHOR>
 * @since 2025/01/03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserCenterRespVO implements java.io.Serializable {

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 当前学习等级
     */
    private Integer currentLevel;

    /**
     * 计划考试时间
     */
    private LocalDate plannedExamDate;

    /**
     * 已练习题目数量
     */
    private Integer studiedQuestions;

    /**
     * 已模考次数
     */
    private Integer mockExamCount;

    /**
     * 我的课程数量
     */
    private Integer myCourseCount;

    /**
     * 已学习秒数
     */
    private Long studiedSeconds;
}