package com.xt.hsk.module.user.controller.app.sms.vo;

import com.xt.hsk.framework.common.constants.RegexpConstant;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * app auth login req vo
 *
 * <AUTHOR>
 * @since 2025/05/30
 */
@Data
public class AppAuthLoginReqVO extends CaptchaVerificationReqVO {

    /**
     * 电话号码
     */

    @Pattern(regexp = RegexpConstant.MOBILE, message = "{validation.mobile.invalid}")
    private String mobile;

    /**
     * 区号
     */
    @Pattern(regexp = RegexpConstant.COUNTRY_CODE, message = "{validation.invalid.country.code}")
    private String countryCode;

}
