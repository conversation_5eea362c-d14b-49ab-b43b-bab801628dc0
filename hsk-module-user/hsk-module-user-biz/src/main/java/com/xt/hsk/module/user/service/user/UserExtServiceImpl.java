package com.xt.hsk.module.user.service.user;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.user.controller.app.user.vo.UserExtPageReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.xt.hsk.module.user.dal.dataobject.ext.UserExtDO;

import com.xt.hsk.module.user.dal.mysql.ext.UserExtMapper;


/**
 * 用户扩展 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserExtServiceImpl extends ServiceImpl<UserExtMapper, UserExtDO> implements UserExtService {

    @Resource
    private UserExtMapper extMapper;

    @Override
    public PageResult<UserExtDO> selectPage(UserExtPageReqVO pageReqVO) {

        return extMapper.selectPage(pageReqVO);
    }

}