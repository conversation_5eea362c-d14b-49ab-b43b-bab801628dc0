package com.xt.hsk.module.user.controller.admin.user.vo;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.user.enums.UserSourceEnum;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 管理后台 - 用户分页请求参数对象。 用于封装用户分页查询的条件。
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserPageReqVO extends PageParam {

    /**
     * 用户ID。 示例值：1。
     */
    private Long id;

    /**
     * 用户昵称。 示例值：张三。
     */
    private String nickname;

    /**
     * 手机号码。
     * 示例值：13888888888。
     */
    private String mobile;

    /**
     * 手机区号。
     * 示例值：86。
     */
    private Integer countryCode;

    /**
     * 用户来源 0-手动注册/管理员注册 1-APP注册 2-PC注册 3-H5注册）
     */
    @InEnum(value = UserSourceEnum.class, message = "用户来源必须是{value}")
    private Integer userSource;
    /**
     * 用户来源说明
     */
    private String userSourceStr;

    /**
     * 用户状态 禁用/启用）
     */
    private Integer status;

    /**
     * 创建时间范围。
     * 用于筛选用户的创建时间区间。
     * 示例值：["2023-01-01 00:00:00", "2023-12-31 23:59:59"]。
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * IDS
     */
    private List<Long> ids;

}
