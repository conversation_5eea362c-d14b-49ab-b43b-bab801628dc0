package com.xt.hsk.module.user.listener;

import cn.hutool.core.collection.CollUtil;
import cn.idev.excel.context.AnalysisContext;
import com.xt.hsk.framework.common.constants.RegexpConstant;
import com.xt.hsk.framework.common.constants.UserConstant;
import com.xt.hsk.module.infra.listener.BaseAnalysisEventListener;
import com.xt.hsk.module.user.controller.admin.user.vo.UserImportExcelVO;
import com.xt.hsk.module.user.service.user.UserService;
import com.xt.hsk.module.user.util.CountryCodeUtils;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户导入监听器
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Slf4j
public class UserImportListener extends BaseAnalysisEventListener<UserImportExcelVO> {

    private final List<UserImportExcelVO> userList = new ArrayList<>();

    /**
     * 校验是否重复的手机号（区号+手机号）
     */
    private final Set<String> phoneSet = new HashSet<>();

    /**
     * 批量检查 - 用于存储所有导入数据的手机号
     */
    private final List<String> importPhones = new ArrayList<>();

    /**
     * 批量检查 - 用于存储所有导入数据的区号
     */
    private final List<String> importCountryCodes = new ArrayList<>();

    /**
     * 存储每个手机号对应的Excel行号
     */
    private final List<Integer> rowIndexList = new ArrayList<>();

    /**
     * 是否有格式错误（非重复错误）
     */
    private boolean hasFormatError = false;

    /**
     * 是否有重复数据错误
     */
    private boolean hasDuplicateError = false;

    /**
     * 昵称最大字节长度（约为15个中文字符或45个英文字符）
     */
    private static final int MAX_NICKNAME_BYTES = UserConstant.MAX_NICKNAME_BYTES;

    private final UserService userService;

    public UserImportListener(UserService userService) {
        this.userService = userService;
    }

    // 手机号和区号只能是数字
    private static final String MOBILE_REGEX = RegexpConstant.MOBILE;
    private static final String COUNTRY_CODE_REGEX = RegexpConstant.COUNTRY_CODE;

    @Override
    public void invoke(UserImportExcelVO data, AnalysisContext context) {
        // 行号 + 1是因为索引从0开始
        int rowIndex = context.readRowHolder().getRowIndex();

        try {
            // 校验并处理数据
            if (validateUserData(data, rowIndex)) {
                userList.add(data);
                phoneSet.add(data.getCountryCode() + "-" + data.getMobile());
                VALID_COUNT++;
            } else {
                INVALID_COUNT++;
                SUCCESS = false;
                hasFormatError = true;
            }
        } catch (Exception e) {
            // 记录未预期的异常
            log.error("处理第{}行数据时发生异常", rowIndex, e);
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, e.getMessage()));
            INVALID_COUNT++;
            SUCCESS = false;
            hasFormatError = true;
        }
    }

    /**
     * 获取字符串的字节长度（UTF-8编码）
     *
     * @param str 字符串
     * @return 字节长度
     */
    private int getByteLength(String str) {
        if (StringUtils.isBlank(str)) {
            return 0;
        }
        return str.getBytes(StandardCharsets.UTF_8).length;
    }

    /**
     * 校验用户数据
     *
     * @param data     用户数据
     * @param rowIndex 行号
     * @return 是否有效
     */
    private boolean validateUserData(UserImportExcelVO data, int rowIndex) {

        boolean valid = true;

        // 校验区号和手机号必填
        if (StringUtils.isBlank(data.getCountryCode())) {
            msg.add(String.format("第%d行：区号为空", rowIndex));
            valid = false;
        }

        if (StringUtils.isNotBlank(data.getCountryCode()) && !CountryCodeUtils.checkCountryCode(
            data.getCountryCode())) {
            msg.add(String.format("第%d行：区号不存在", rowIndex));
            valid = false;
        }
        if (StringUtils.isNotBlank(data.getCountryCode()) && !data.getCountryCode()
            .matches(COUNTRY_CODE_REGEX)) {
            msg.add(String.format("第%d行：区号格式不正确，区号只能填写数字", rowIndex));
            valid = false;
        }

        if (StringUtils.isBlank(data.getMobile())) {
            msg.add(String.format("第%d行：手机号为空", rowIndex));
            valid = false;
        }
        if (StringUtils.isNotBlank(data.getMobile()) && !data.getMobile().matches(MOBILE_REGEX)) {
            msg.add(String.format("第%d行：手机号格式不正确，手机号只能是6-11位数字", rowIndex));
            valid = false;
        }

        // 校验昵称字节长度
        if (StringUtils.isNotBlank(data.getNickname())) {
            int byteLength = getByteLength(data.getNickname());
            if (byteLength > MAX_NICKNAME_BYTES) {
                msg.add(String.format("第%d行：昵称长度超出限制，最多15个中文字符或45个英文字符",
                    rowIndex));
                valid = false;
            }
        }

        // 生成手机号唯一标识
        String phoneKey = data.getCountryCode() + "-" + data.getMobile();

        // 校验本次导入中是否有重复
        if (phoneSet.contains(phoneKey)) {
            msg.add(String.format("第%d行：与本次导入的其他数据重复", rowIndex));
            hasDuplicateError = true;
            valid = false;
        }

        // 添加到待检查列表，后续将进行批量查询
        importCountryCodes.add(data.getCountryCode());
        importPhones.add(data.getMobile());
        // 记录当前数据的行号
        rowIndexList.add(rowIndex);

        return valid;
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Excel解析完成，共处理{}条数据，有效{}条，无效{}条",
            VALID_COUNT + INVALID_COUNT, VALID_COUNT, INVALID_COUNT);

        // 数据校验通过，进行批量查重后保存
        if (VALID_COUNT > 0) {
            try {
                // 1. 执行批量查重检查
                Set<String> existingPhones = userService.batchCheckPhonesExist(importCountryCodes,
                    importPhones);

                // 2. 移除已存在的记录
                if (CollUtil.isNotEmpty(existingPhones)) {
                    List<UserImportExcelVO> validUserList = new ArrayList<>();
                    // 记录有效数据数量
                    int validCount = 0;

                    for (int i = 0; i < userList.size(); i++) {
                        UserImportExcelVO user = userList.get(i);
                        String phoneKey = user.getCountryCode() + "-" + user.getMobile();
                        
                        // 如果不存在数据库中，则添加到待新增列表中
                        if (!existingPhones.contains(phoneKey)) {
                            validUserList.add(user);
                            validCount++;
                        } else {
                            // 记录重复数据（包含行号信息）
                            msg.add(String.format("第%d行：区号%s和手机号%s在系统中已存在",
                                rowIndexList.get(i), user.getCountryCode(),
                                user.getMobile()));
                            INVALID_COUNT++;
                            hasDuplicateError = true;
                            SUCCESS = false;
                        }
                    }

                    // 更新有效用户列表和有效计数
                    userList.clear();
                    userList.addAll(validUserList);
                    VALID_COUNT = validCount;
                }

                // 3. 批量创建用户（一次性批量插入）
                if (SUCCESS && CollUtil.isNotEmpty(userList)) {
                    userService.batchCreateUsersByAdmin(userList);
                    log.info("成功导入{}条用户数据", VALID_COUNT);
                }
            } catch (Exception e) {
                log.error("批量导入用户失败", e);
                SUCCESS = false;
                msg.add("批量导入用户失败：" + e.getMessage());
            }
        } else if (hasFormatError) {
            msg.add("请检查数据格式是否正确");
        } else if (hasDuplicateError) {
            msg.add("请检查是否有重复数据");
        }
    }
} 