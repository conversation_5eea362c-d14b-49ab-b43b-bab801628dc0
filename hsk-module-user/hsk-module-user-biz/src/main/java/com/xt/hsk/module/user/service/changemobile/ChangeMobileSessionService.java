package com.xt.hsk.module.user.service.changemobile;

import cn.hutool.core.util.IdUtil;
import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import com.xt.hsk.framework.redis.utils.RedisUtil;
import com.xt.hsk.module.user.dal.dataobject.changemobile.ChangeMobileRecordDO;
import com.xt.hsk.module.user.dal.mysql.changemobile.ChangeMobileRecordMapper;
import com.xt.hsk.module.user.dto.ChangeMobileSession;
import com.xt.hsk.module.user.enums.ChangeMobileState;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 修改手机号会话管理服务
 *
 * <AUTHOR>
 * @since 2025/07/25
 */
@Service
@Slf4j
public class ChangeMobileSessionService {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ChangeMobileRecordMapper changeMobileRecordMapper;

    private static final String SESSION_KEY_PREFIX = "change_mobile_session:";
    private static final int SESSION_EXPIRE_MINUTES = 45;

    /**
     * 创建会话（Redis + 数据库双写）
     */
    @Transactional(rollbackFor = Exception.class)
    public ChangeMobileSession createSession(Long userId, String oldMobile, String oldCountryCode) {
        // 1. 处理用户之前未完成的会话
        cleanupUserPreviousSessions(userId);

        // 2. 清理Redis中的旧会话
        deleteSession(userId);

        String sessionId = generateSessionId();
        LocalDateTime now = LocalDateTime.now();

        // 创建会话对象
        ChangeMobileSession session = ChangeMobileSession.builder()
                .sessionId(sessionId)
                .userId(userId)
                .state(ChangeMobileState.OLD_CODE_SENT)
                .oldMobile(oldMobile)
                .oldCountryCode(oldCountryCode)
                .oldMobileVerified(false)
                .newMobileCodeSent(false)
                .createTime(now)
                .updateTime(now)
                .retryCount(0)
                .build();

        // 1. 保存到Redis
        String key = SESSION_KEY_PREFIX + userId;
        redisUtil.set(key, session, SESSION_EXPIRE_MINUTES, TimeUnit.MINUTES);

        // 2. 保存到数据库
        ChangeMobileRecordDO recordDO = buildRecordDO(session);
        changeMobileRecordMapper.insert(recordDO);

        log.info("创建修改手机号会话: userId={}, sessionId={}", userId, sessionId);
        return session;
    }

    /**
     * 获取会话（优先从Redis获取，Redis不存在时从数据库恢复）
     */
    public ChangeMobileSession getSession(Long userId) {
        String key = SESSION_KEY_PREFIX + userId;
        ChangeMobileSession session = (ChangeMobileSession) redisUtil.get(key);

        // 如果Redis中不存在，尝试从数据库恢复
        if (session == null) {
            session = recoverSessionFromDatabase(userId);
            if (session != null) {
                // 恢复到Redis中
                redisUtil.set(key, session, SESSION_EXPIRE_MINUTES, TimeUnit.MINUTES);
                log.info("从数据库恢复会话到Redis: userId={}, sessionId={}", userId, session.getSessionId());
            }
        }

        return session;
    }

    /**
     * 更新会话（Redis + 数据库双写）
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSession(ChangeMobileSession session) {
        session.setUpdateTime(LocalDateTime.now());
        String key = SESSION_KEY_PREFIX + session.getUserId();

        // 1. 更新Redis
        redisUtil.set(key, session, SESSION_EXPIRE_MINUTES, TimeUnit.MINUTES);

        // 2. 更新数据库 - 根据sessionId更新，而不是根据ID
        ChangeMobileRecordDO existingRecord = changeMobileRecordMapper.selectBySessionId(session.getSessionId());
        if (existingRecord != null) {
            // 构建更新的记录，保留原有的ID和创建时间等字段
            ChangeMobileRecordDO recordDO = buildRecordDO(session);
            recordDO.setId(existingRecord.getId());
            recordDO.setCreateTime(existingRecord.getCreateTime());

            changeMobileRecordMapper.updateById(recordDO);
            log.debug("更新修改手机号会话: userId={}, sessionId={}, state={}",
                session.getUserId(), session.getSessionId(), session.getState());
        } else {
            log.warn("未找到对应的数据库记录进行更新: userId={}, sessionId={}",
                session.getUserId(), session.getSessionId());
        }
    }

    /**
     * 删除会话（只删除Redis，数据库记录保留用于审计）
     */
    public void deleteSession(Long userId) {
        String key = SESSION_KEY_PREFIX + userId;
        redisUtil.delete(key);
        log.info("删除修改手机号会话: userId={}", userId);
    }

    /**
     * 标记会话完成（更新数据库状态为COMPLETED）
     */
    @Transactional(rollbackFor = Exception.class)
    public void markSessionCompleted(Long userId, String sessionId) {
        ChangeMobileRecordDO record = changeMobileRecordMapper.selectBySessionId(sessionId);
        if (record != null) {
            record.setState(ChangeMobileState.COMPLETED);
            record.setCompletedTime(LocalDateTime.now());
            // 清除失败信息，因为最终成功了
            record.setFailedReason(null);
            record.setFailedTime(null);
            changeMobileRecordMapper.updateById(record);
            log.info("标记会话完成: userId={}, sessionId={}", userId, sessionId);
        }
    }

    /**
     * 标记会话失败（更新数据库状态为FAILED）
     */
    @Transactional(rollbackFor = Exception.class)
    public void markSessionFailed(Long userId, String sessionId, String failedReason) {
        ChangeMobileRecordDO record = changeMobileRecordMapper.selectBySessionId(sessionId);
        if (record != null) {
            record.setState(ChangeMobileState.FAILED);
            record.setFailedReason(failedReason);
            record.setFailedTime(LocalDateTime.now());
            changeMobileRecordMapper.updateById(record);
            log.info("标记会话失败: userId={}, sessionId={}, reason={}", userId, sessionId, failedReason);
        }
    }

    /**
     * 验证会话状态
     */
    public boolean validateSession(Long userId, ChangeMobileState expectedState) {
        ChangeMobileSession session = getSession(userId);
        if (session == null) {
            log.warn("会话不存在: userId={}", userId);
            return false;
        }

        if (!expectedState.equals(session.getState())) {
            log.warn("会话状态不匹配: userId={}, expected={}, actual={}",
                    userId, expectedState, session.getState());
            return false;
        }

        return true;
    }

    /**
     * 检查会话是否已过期或已完成
     */
    public boolean isSessionExpiredOrCompleted(ChangeMobileSession session) {
        if (session == null) {
            return true;
        }

        // 检查是否已完成或失败
        if (ChangeMobileState.COMPLETED.equals(session.getState()) ||
            ChangeMobileState.FAILED.equals(session.getState())) {
            return true;
        }

        // 检查是否超时
        if (session.getCreateTime().isBefore(LocalDateTime.now().minusMinutes(SESSION_EXPIRE_MINUTES))) {
            log.warn("会话已超时: userId={}, sessionId={}", session.getUserId(), session.getSessionId());
            return true;
        }

        return false;
    }

    /**
     * 清理用户之前未完成的会话
     */
    private void cleanupUserPreviousSessions(Long userId) {
        // 查找用户最新的未完成会话
        ChangeMobileRecordDO latestRecord = changeMobileRecordMapper.selectLatestByUserId(userId);

        if (latestRecord != null &&
            !ChangeMobileState.COMPLETED.equals(latestRecord.getState()) &&
            !ChangeMobileState.FAILED.equals(latestRecord.getState())) {

            // 标记为被新会话替换
            latestRecord.setState(ChangeMobileState.FAILED);
            latestRecord.setFailedReason("用户发起新的更换手机号流程，旧会话被替换");
            latestRecord.setFailedTime(LocalDateTime.now());
            changeMobileRecordMapper.updateById(latestRecord);

            log.info("清理用户旧会话: userId={}, oldSessionId={}", userId, latestRecord.getSessionId());
        }
    }

    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return "cms_" + System.currentTimeMillis() + "_" + IdUtil.fastSimpleUUID().substring(0, 8);
    }

    /**
     * 从数据库恢复会话
     */
    private ChangeMobileSession recoverSessionFromDatabase(Long userId) {
        ChangeMobileRecordDO record = changeMobileRecordMapper.selectLatestByUserId(userId);
        if (record == null || ChangeMobileState.COMPLETED.equals(record.getState()) ||
            ChangeMobileState.FAILED.equals(record.getState())) {
            return null;
        }

        // 检查是否超时（超过45分钟）
        if (record.getCreateTime().isBefore(LocalDateTime.now().minusMinutes(SESSION_EXPIRE_MINUTES))) {
            log.warn("会话已超时，不进行恢复: userId={}, sessionId={}", userId, record.getSessionId());
            return null;
        }

        return buildSessionFromRecord(record);
    }

    /**
     * 从记录DO构建会话对象
     */
    private ChangeMobileSession buildSessionFromRecord(ChangeMobileRecordDO record) {
        return ChangeMobileSession.builder()
                .sessionId(record.getSessionId())
                .userId(record.getUserId())
                .state(record.getState())
                .oldMobile(record.getOldMobile())
                .oldCountryCode(record.getOldCountryCode())
                .oldMobileVerified(record.getOldMobileVerified())
                .oldMobileVerifiedTime(record.getOldMobileVerifiedTime())
                .newMobile(record.getNewMobile())
                .newCountryCode(record.getNewCountryCode())
                .newMobileCodeSent(record.getNewMobileCodeSent())
                .newMobileCodeSentTime(record.getNewMobileCodeSentTime())
                .createTime(record.getCreateTime())
                .updateTime(record.getUpdateTime())
                .retryCount(record.getRetryCount())
                .build();
    }

    /**
     * 从会话对象构建记录DO
     */
    private ChangeMobileRecordDO buildRecordDO(ChangeMobileSession session) {

        return ChangeMobileRecordDO.builder()
                .sessionId(session.getSessionId())
                .userId(session.getUserId())
                .state(session.getState())
                .oldCountryCode(session.getOldCountryCode())
                .oldMobile(session.getOldMobile())
                .oldMobileVerified(session.getOldMobileVerified())
                .oldMobileVerifiedTime(session.getOldMobileVerifiedTime())
                .newCountryCode(session.getNewCountryCode())
                .newMobile(session.getNewMobile())
                .newMobileCodeSent(session.getNewMobileCodeSent())
                .newMobileCodeSentTime(session.getNewMobileCodeSentTime())
                .retryCount(session.getRetryCount())
                .clientIp(ServletUtils.getClientIP())
                .userAgent(ServletUtils.getUserAgent())
                .build();
    }
}
