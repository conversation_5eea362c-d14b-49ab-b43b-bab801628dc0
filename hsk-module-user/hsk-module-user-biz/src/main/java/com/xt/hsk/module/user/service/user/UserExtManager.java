package com.xt.hsk.module.user.service.user;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.user.controller.app.user.vo.UserExtPageReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserExtSaveReqVO;
import com.xt.hsk.module.user.dal.dataobject.ext.UserExtDO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 用户扩展 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserExtManager {

    @Resource
    private UserExtService extService;


    public Long createUserExt(UserExtSaveReqVO createReqVO) {
        // 插入
        UserExtDO ext = BeanUtils.toBean(createReqVO, UserExtDO.class);
        extService.save(ext);

        // 返回
        return ext.getId();
    }


    public void updateUserExt(UserExtSaveReqVO updateReqVO) {
        // 校验存在
        validateUserExtExists(updateReqVO.getId());
        // 更新
        UserExtDO updateObj = BeanUtils.toBean(updateReqVO, UserExtDO.class);
        extService.updateById(updateObj);
    }


    public void deleteUserExt(Long id) {
        // 校验存在
        validateUserExtExists(id);
        // 删除
        extService.removeById(id);
    }

    private void validateUserExtExists(Long id) {
        if (extService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public UserExtDO getUserExt(Long id) {
        return extService.getById(id);
    }

    public PageResult<UserExtDO> getUserExtPage(@Valid UserExtPageReqVO pageReqVO) {
        return extService.selectPage(pageReqVO);
    }

}