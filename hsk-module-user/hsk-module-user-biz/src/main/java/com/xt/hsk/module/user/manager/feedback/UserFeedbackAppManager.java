package com.xt.hsk.module.user.manager.feedback;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_FEEDBACK_DAILY_LIMIT_REACHED;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_FEEDBACK_MOBILE_CODE_REQUIRED;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.xt.hsk.module.user.controller.app.feedback.vo.UserFeedbackCreateReqVO;
import com.xt.hsk.module.user.service.feedback.UserFeedbackService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 用户反馈应用管理器
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Component
public class UserFeedbackAppManager {

    @Resource
    private UserFeedbackService userFeedbackService;

    /**
     * 新增用户反馈
     */
    public void createUserFeedback(UserFeedbackCreateReqVO reqVO) {
        Long userId = StpUtil.getLoginIdAsLong();
        // 如果联系方式是手机号，需要设置区号
        if (reqVO.getContactInfoType() == 4 && CharSequenceUtil.isBlank(reqVO.getCountryCode())) {
            throw exception(USER_FEEDBACK_MOBILE_CODE_REQUIRED);
        }

        // 校验当天反馈次数是否达到上限
        if (userFeedbackService.checkUserFeedbackLimit(userId)) {
            throw exception(USER_FEEDBACK_DAILY_LIMIT_REACHED);
        }

        userFeedbackService.createUserFeedback(userId, reqVO);
    }
}
