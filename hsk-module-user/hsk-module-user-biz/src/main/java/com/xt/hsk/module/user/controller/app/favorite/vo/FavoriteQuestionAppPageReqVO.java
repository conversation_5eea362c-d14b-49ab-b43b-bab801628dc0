package com.xt.hsk.module.user.controller.app.favorite.vo;

import com.xt.hsk.framework.common.enums.SubjectEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户题目收藏分页查询 req vo
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
public class FavoriteQuestionAppPageReqVO {


    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 科目
     *
     * @see SubjectEnum
     */
    private Integer subject;

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 题干内容
     */
    private String questionContent;

    /**
     * 是否错题：1-是 0-否
     */
    private Integer isWrongQuestion;

    /**
     * 收藏时间
     */
    private LocalDateTime favoriteTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 收录时间 1-近三天 2-近7天 3-近30天
     */
    private Integer collectionTimeType;

    /**
     * 部分题型
     */
    private List<UnitQuestionType> questionTypeList;

    @Data
    public static class UnitQuestionType {
        private Integer unitSort;
        private Integer questionType;
    }

}
