package com.xt.hsk.module.user.controller.app.favorite.vo;

import lombok.Data;

import java.util.List;

/**
 * 用户收藏切换请求VO
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
public class UserFavoriteToggleReqVO {

    /**
     * 收藏类型：1-字词 2-真题题目 3-小游戏题目
     */
    private Integer favoriteType;

    /**
     * 收藏来源：1-互动课作业 2-真题练习 3-专项练习
     */
    private Integer favoriteSource;

    /**
     * 目标ID（字词ID | 题目ID | 小游戏题目ID）
     */
    private List<UserFavoriteTargetVo> targetList;

    /**
     * 游戏ID（收藏小游戏题目时需要）
     */
    private Long gameId;

    /**
     * 是否错题：1-是 0-否
     */
    private Boolean isWrongQuestion;

    /**
     * 操作类型：true-收藏 false-取消收藏
     */
    private Boolean isFavorite;
}