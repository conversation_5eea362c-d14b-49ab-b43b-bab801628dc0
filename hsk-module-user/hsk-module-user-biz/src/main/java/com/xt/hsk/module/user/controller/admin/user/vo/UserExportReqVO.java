package com.xt.hsk.module.user.controller.admin.user.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 管理后台 - 用户导出请求参数对象。 用于封装用户导出查询的条件。
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserExportReqVO extends UserPageReqVO {

    /**
     * 任务名称。用于在导出任务列表中显示。 示例值：用户数据导出。
     */
    private String taskName = "用户数据导出";
} 