package com.xt.hsk.module.user.service.user;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.module.user.dal.dataobject.user.UserOperateLogDO;
import com.xt.hsk.module.user.dal.mysql.user.UserOperateLogMapper;
import com.xt.hsk.module.user.enums.UserOperateTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 用户操作日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserOperateLogServiceImpl extends ServiceImpl<UserOperateLogMapper, UserOperateLogDO>
    implements UserOperateLogService {

    @Resource
    private UserOperateLogMapper userOperateLogMapper;

    @Override
    public Long createUserOperateLog(Long userId, UserOperateTypeEnum operateType,
        Integer beforeStatus, Integer afterStatus,
        Long operatorId, String remark, String operateIp,
        String extra) {
        UserOperateLogDO log = new UserOperateLogDO();
        log.setUserId(userId);
        log.setOperateType(operateType.getCode());
        log.setBeforeStatus(beforeStatus);
        log.setAfterStatus(afterStatus);
        log.setOperatorId(operatorId);
        log.setRemark(remark);
        log.setOperateIp(operateIp);
        log.setExtra(extra);

        userOperateLogMapper.insert(log);
        return log.getId();
    }

} 