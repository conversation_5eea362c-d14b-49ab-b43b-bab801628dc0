package com.xt.hsk.module.user.controller.admin.feedback.vo;

import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 用户反馈 Response VO
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class UserFeedbackRespVO extends UserFeedbackBaseVO {

    /**
     * 反馈ID
     */
    private Long id;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 