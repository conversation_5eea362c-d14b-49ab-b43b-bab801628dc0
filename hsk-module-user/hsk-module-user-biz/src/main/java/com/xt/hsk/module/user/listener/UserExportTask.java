package com.xt.hsk.module.user.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xt.hsk.module.infra.listener.BaseExportTask;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户导出任务
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Component("userExportTask")
@Slf4j
public class UserExportTask extends BaseExportTask<UserExportTaskExport> {

    @Resource
    private UserExportTaskExport userExportTaskExport;

    @Override
    protected UserExportTaskExport getExporter() {
        return userExportTaskExport;
    }

    @Override
    protected String getFileName() {
        return "用户数据";
    }

    @Override
    protected Map<String, Object> buildQueryParams(String params) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 直接将params转换为Map
            return objectMapper.readValue(params,
                new TypeReference<Map<String, Object>>() {
                });
        } catch (Exception e) {
            log.error("解析导出参数失败: {}", e.getMessage(), e);
            return Map.of();
        }
    }
} 