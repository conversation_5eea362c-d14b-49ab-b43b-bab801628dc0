package com.xt.hsk.module.user.controller.app.favorite.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户单词收藏分页列表响应
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
public class FavoriteWordAppPageRespVO {

    /**
     * 收藏ID
     */
    private Long favoriteId;

    /**
     * 是否错题：1-是 0-否
     */
    private Boolean isWrongQuestion;

    /**
     * 收藏时间
     */
    private LocalDateTime favoriteTime;

    /**
     * 单词ID
     */
    private Long wordId;

    /**
     * 汉字/词语（如爱）
     */
    private String word;

    /**
     * 拼音（如ài）
     */
    private String pinyin;

    /**
     * 是否汉越词 0 否 1 是
     */
    private Integer isSpecial;

    /**
     * 音频文件URL
     */
    private String audioUrl;

    /**
     * hsk 等级
     */
    private Integer hskLevel;

    /**
     * 越南语
     */
    private List<String> translationOts;
}
