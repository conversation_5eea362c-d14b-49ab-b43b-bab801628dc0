package com.xt.hsk.module.user.controller.app.sms;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.idempotent.core.annotation.Idempotent;
import com.xt.hsk.framework.idempotent.core.keyresolver.impl.ExpressionIdempotentKeyResolver;
import com.xt.hsk.framework.ratelimiter.core.annotation.RateLimiter;
import com.xt.hsk.framework.ratelimiter.core.keyresolver.impl.ClientIpRateLimiterKeyResolver;
import com.xt.hsk.module.user.controller.app.sms.vo.AppAuthLoginReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.SendChangeMobileCodeReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.SendNewMobileCodeReqVO;
import com.xt.hsk.module.user.manager.changemobile.ChangeMobileManager;
import com.xt.hsk.module.user.manager.sms.SmsManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * SMS 控制器
 *
 * <AUTHOR>
 * @since 2025/05/29
 */
@RestController
@RequestMapping("/user/sms")
@Slf4j
public class SmsController {

    @Resource
    private SmsManager smsManager;

    @Resource
    private ChangeMobileManager changeMobileManager;

    /**
     * 登录短信验证码发送 限制规则：1分钟内只能发送1条，每天最多10条
     */
    @Idempotent(timeout = 5, keyResolver = ExpressionIdempotentKeyResolver.class,
                keyArg = "#reqVO.countryCode + ':' + #reqVO.mobile",
                message = "{Business.sms_send_too_frequently}")
    // @RateLimiter(timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    @PostMapping("/sendLoginSmsCode")
    public CommonResult<Boolean> sendLoginSmsCode(@Valid @RequestBody AppAuthLoginReqVO reqVO) {
        return CommonResult.success(smsManager.sendLoginSmsCode(reqVO));
    }

    /**
     * 重置密码短信验证码发送
     */
    @Idempotent(timeout = 5, keyResolver = ExpressionIdempotentKeyResolver.class,
                keyArg = "#reqVO.countryCode + ':' + #reqVO.mobile",
                message = "{Business.sms_send_too_frequently}")
    @RateLimiter(timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    @PostMapping("/sendResetPasswordSmsCode")
    public CommonResult<Boolean> sendResetPasswordSmsCode(
        @Valid @RequestBody AppAuthLoginReqVO reqVO) {
        return CommonResult.success(smsManager.sendResetPasswordSmsCode(reqVO));
    }

    /**
     * 更换手机号 步骤 1 - >发送更换手机号验证码（发送到旧手机号）
     */
    @RateLimiter(timeUnit = TimeUnit.MINUTES, keyResolver = ClientIpRateLimiterKeyResolver.class)
    @PostMapping("/sendChangeMobileCode")
    public CommonResult<Boolean> sendChangeMobileCode(@Valid @RequestBody SendChangeMobileCodeReqVO reqVO) {
        changeMobileManager.sendOldMobileCode(reqVO);
        return CommonResult.success(true);
    }

    /**
     * 更换手机号 步骤3 发送新手机号验证码
     */
    @PostMapping("/sendNewMobileCode")
    public CommonResult<Boolean> sendNewMobileCode(@Valid @RequestBody SendNewMobileCodeReqVO reqVO) {
        return CommonResult.success(changeMobileManager.sendNewMobileCode(reqVO));
    }
}
