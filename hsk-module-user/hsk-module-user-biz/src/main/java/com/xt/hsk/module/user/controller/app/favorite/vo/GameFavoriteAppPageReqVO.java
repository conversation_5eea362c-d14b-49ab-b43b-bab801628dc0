package com.xt.hsk.module.user.controller.app.favorite.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import java.util.List;
import lombok.Data;

/**
 * 游戏题目收藏分页查询 req vo
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Data
public class GameFavoriteAppPageReqVO extends PageParam {

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 专项练习类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     */
    private Integer type;

    /**
     * 题目名称(题干内容)模糊搜索
     */
    private String questionContent;

    /**
     * 题目难度 1 简单 2中等 3 困难（支持多选）
     */
    private List<Integer> difficultyLevels;

    /**
     * 是否错题：1-是 0-否
     */
    private Integer isWrongQuestion;

    /**
     * 收录时间 1-近三天 2-近7天 3-近30天
     */
    private Integer collectionTimeType;

    /**
     * 用户id
     */
    private Long userId;

}
