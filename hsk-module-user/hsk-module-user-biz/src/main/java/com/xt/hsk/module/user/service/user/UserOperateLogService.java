package com.xt.hsk.module.user.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.user.dal.dataobject.user.UserOperateLogDO;
import com.xt.hsk.module.user.enums.UserOperateTypeEnum;

/**
 * 用户操作日志 Service 接口
 *
 * <AUTHOR>
 */
public interface UserOperateLogService extends IService<UserOperateLogDO> {

    /**
     * 创建用户操作日志
     *
     * @param userId       用户ID
     * @param operateType  操作类型
     * @param beforeStatus 操作前状态
     * @param afterStatus  操作后状态
     * @param operatorId   操作者ID
     * @param remark       备注
     * @param operateIp    操作IP
     * @param extra        扩展信息
     * @return 日志ID
     */
    Long createUserOperateLog(Long userId, UserOperateTypeEnum operateType,
        Integer beforeStatus, Integer afterStatus,
        Long operatorId, String remark, String operateIp,
        String extra);


} 