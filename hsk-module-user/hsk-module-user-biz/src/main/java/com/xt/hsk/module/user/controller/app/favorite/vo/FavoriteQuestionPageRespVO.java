package com.xt.hsk.module.user.controller.app.favorite.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户题目收藏分页列表响应
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
public class FavoriteQuestionPageRespVO {

    /**
     * 收藏ID
     */
    private Long favoriteId;

    /**
     * 是否错题：1-是 0-否
     */
    private Boolean isWrongQuestion;

    /**
     * 收藏时间
     */
    private LocalDateTime favoriteTime;

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 题目详情ID
     */
    private Long questionDetailId;

    /**
     * 题型ID
     */
    private Long questionTypeId;

    /**
     * 教材id
     */
    private Long textbookId;

    /**
     * 教材名
     */
    private String textbookName;

    /**
     * 章节ID
     */
    private Long chapterId;

    /**
     * 章节名称
     */
    private String chapterName;

    /**
     * 排序序号
     */
    private Integer sort;

}
