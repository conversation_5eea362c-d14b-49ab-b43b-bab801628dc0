package com.xt.hsk.module.user.controller.admin.user;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;
import static java.util.stream.Collectors.toMap;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.ImportResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.api.dto.EliteCourseRespDTO;
import com.xt.hsk.module.edu.api.elitecourse.EliteCourseApi;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import com.xt.hsk.module.trade.api.order.TradeOrderApi;
import com.xt.hsk.module.trade.api.order.dto.OrderAdminRespDTO;
import com.xt.hsk.module.trade.api.order.dto.UserOrderPageReqDTO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserAdminRespVO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserCreateReqVO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserExportReqVO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserPageReqVO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserUpdateReqVO;
import com.xt.hsk.module.user.manager.user.AppUserAdminManager;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.FileCopyUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 管理后台 - 用户
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
@Validated
public class AppUserAdminController {

    @Resource
    private AppUserAdminManager appUserAdminManager;

    @Resource
    private ExportTaskApi exportTaskApi;

    @Resource
    private TradeOrderApi tradeOrderApi;

    @Resource
    private EliteCourseApi eliteCourseApi;


    /**
     * 用户订单列表
     */
    @TransMethodResult
    @PostMapping("/userOrderItemList")
    public CommonResult<PageResult<OrderAdminRespDTO>> userOrderItemList(
        @Valid @RequestBody UserOrderPageReqDTO userOrderPageReqDTO) {

        PageResult<OrderAdminRespDTO> orderItemList = tradeOrderApi.getUserOrderItemList(
            userOrderPageReqDTO);

        if (CollUtil.isEmpty(orderItemList.getList())) {
            return success(PageResult.empty());
        }

        // 去课程表查询上下架状态
        List<EliteCourseRespDTO> eliteCourseList = eliteCourseApi.getListByIds(
            orderItemList.getList()
                .stream()
                .map(OrderAdminRespDTO::getProductId)
                .toList());
        if (CollUtil.isNotEmpty(eliteCourseList)) {
            Map<Long, Integer> listingStatusMap = eliteCourseList.stream()
                .collect(
                    toMap(EliteCourseRespDTO::getCourseId, EliteCourseRespDTO::getListingStatus));
            orderItemList.getList().forEach(orderItem -> orderItem.setListingStatus(
                listingStatusMap.get(orderItem.getProductId())));
        }

        return CommonResult.success(orderItemList);
    }



    /**
     * 创建用户
     *
     * @param createReqVO 创建信息
     * @return 用户ID
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('user:user:create')")
    @LogRecord(type = LogRecordType.APP_USER_TYPE, subType = "创建用户", bizNo = "{{#userId}}", success = "创建用户：【{{#user.nickname}}】，手机号：{{#user.mobile}}")
    public CommonResult<Long> createUser(@Valid @RequestBody UserCreateReqVO createReqVO) {
        return success(appUserAdminManager.createUser(createReqVO));
    }

    /**
     * 更新用户
     *
     * @param updateReqVO 更新信息
     * @return true/false
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('user:user:update')")
    @LogRecord(type = LogRecordType.APP_USER_TYPE, subType = "修改用户", bizNo = "{{#updateReqVO.id}}", success = "修改用户：【{{#user.nickname}}】，手机号：{{#user.mobile}}")
    public CommonResult<Boolean> updateUser(@Valid @RequestBody UserUpdateReqVO updateReqVO) {
        appUserAdminManager.updateUser(updateReqVO);
        return success(true);
    }

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return true/false
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('user:user:delete')")
    @LogRecord(type = LogRecordType.APP_USER_TYPE, subType = "删除用户", bizNo = "{{#id}}", success = "删除用户：【{{#user.nickname}}】，手机号：{{#user.mobile}}")
    public CommonResult<Boolean> deleteUser(@RequestParam("id") Long id) {
        appUserAdminManager.deleteUser(id);
        return success(true);
    }

    /**
     * 更新用户状态
     *
     * @param id 用户ID
     * @return true/false
     */
    @PutMapping("/update-status")
    @PreAuthorize("@ss.hasPermission('user:user:update')")
    @LogRecord(type = LogRecordType.APP_USER_TYPE, subType = "更新用户状态", bizNo = "{{#id}}", success = "{{#statusText}}用户手机号：【{{#user.mobile}}】")
    public CommonResult<Boolean> updateUserStatus(@RequestParam("id") Long id) {
        appUserAdminManager.updateUserStatus(id);
        return success(true);
    }

    /**
     * 重置密码
     *
     * @param id 用户ID
     * @return true/false
     */
    @PutMapping("/reset-password")
    @PreAuthorize("@ss.hasPermission('user:user:update')")
    @LogRecord(type = LogRecordType.APP_USER_TYPE, subType = "重置用户密码", bizNo = "{{#id}}", success = "重置用户：【{{#user.mobile}}】密码")
    public CommonResult<Boolean> resetPassword(@RequestParam("id") Long id) {
        appUserAdminManager.resetPassword(id);
        return success(true);
    }

    /**
     * 获得用户分页
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('user:user:query')")
    public CommonResult<PageResult<UserAdminRespVO>> getInteractiveCoursePage(
        @RequestBody @Valid UserPageReqVO pageReqVO) {
        return success(appUserAdminManager.getUserPage(pageReqVO));
    }

    /**
     * 获取用户
     *
     * @param id 用户id
     */
    @PostMapping("/get")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('user:user:query')")
    public CommonResult<UserAdminRespVO> getUser(@RequestParam("id") Long id) {
        return success(appUserAdminManager.getUser(id));
    }

    /**
     * 导出用户
     */
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermission('user:user:export')")
    public CommonResult<Long> exportUser(@Valid @RequestBody UserExportReqVO exportReqVO) {
        // 调用导出任务API创建任务
        Long task = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
            ExportTaskTypeEnum.USER,
            JSON.toJSONString(exportReqVO));
        return success(task);
    }

    /**
     * 下载导入模板
     *
     * @param response 响应
     */
    @PreAuthorize("@ss.hasPermission('user:user:import')")
    @PostMapping("/import/template")
    public void downloadImportTemplate(HttpServletResponse response)
        throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("批量导入用户模板", StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        // 获取模板文件路径
        ClassPathResource resource = new ClassPathResource("excelTemplate/批量导入用户模板.xlsx");
        InputStream inputStream = resource.getInputStream();

        // 写入响应流
        FileCopyUtils.copy(inputStream, response.getOutputStream());
    }

    /**
     * 导入用户
     *
     * @param file 导入文件
     * @return 导入结果
     */
    @PostMapping("/import")
    @PreAuthorize("@ss.hasPermission('user:user:import')")
    @LogRecord(type = LogRecordType.APP_USER_TYPE, subType = "导入用户", bizNo = "{{#fileName}}", success = "导入用户：成功导入{{#importResult.createCount}}个用户，失败{{#importResult.updateCount}}个")
    public CommonResult<ImportResult> importUser(@RequestParam("file") MultipartFile file) {
        return success(appUserAdminManager.importUser(file));
    }
} 