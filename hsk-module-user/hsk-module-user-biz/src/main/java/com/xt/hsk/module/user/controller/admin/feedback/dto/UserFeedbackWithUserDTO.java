package com.xt.hsk.module.user.controller.admin.feedback.dto;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 用户反馈信息DTO，包含用户基本信息
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Data
public class UserFeedbackWithUserDTO {

    /**
     * 反馈ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 区号
     */
    private String countryCode;

    /**
     * 联系方式类型 1 whchat 2 e-mail 3 zalo 4 手机号 5whatapp 6Facebook
     */
    private Integer contactInfoType;

    /**
     * 联系方式
     */
    private String contactInfo;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 操作IP地址
     */
    private String operateIp;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户手机区号
     */
    private String userCountryCode;
} 