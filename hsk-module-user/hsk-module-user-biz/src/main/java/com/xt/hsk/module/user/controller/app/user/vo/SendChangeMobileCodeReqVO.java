package com.xt.hsk.module.user.controller.app.user.vo;

import com.xt.hsk.module.user.controller.app.sms.vo.CaptchaVerificationReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发送更换手机号验证码 Request VO
 *
 * <AUTHOR>
 * @since 2025/07/21
 */
@Schema(description = "发送更换手机号验证码 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SendChangeMobileCodeReqVO extends CaptchaVerificationReqVO {

}
