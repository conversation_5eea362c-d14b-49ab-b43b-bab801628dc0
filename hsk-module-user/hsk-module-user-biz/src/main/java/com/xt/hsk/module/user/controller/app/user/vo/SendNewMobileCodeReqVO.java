package com.xt.hsk.module.user.controller.app.user.vo;

import com.xt.hsk.framework.common.constants.RegexpConstant;
import com.xt.hsk.module.user.controller.app.sms.vo.CaptchaVerificationReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 发送新手机号验证码 Request VO
 *
 * <AUTHOR>
 * @since 2025/07/25
 */
@Schema(description = "发送新手机号验证码 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class SendNewMobileCodeReqVO extends CaptchaVerificationReqVO {

    /**
     * 新手机号
     */
    @Schema(description = "新手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "13888888888")
    @NotBlank(message = "新手机号不能为空")
    @Pattern(regexp = RegexpConstant.MOBILE, message = "{validation.mobile.invalid}")
    private String newMobile;

    /**
     * 新手机区号
     */
    @Schema(description = "新手机区号", requiredMode = Schema.RequiredMode.REQUIRED, example = "+86")
    @NotBlank(message = "新手机区号不能为空")
    @Pattern(regexp = RegexpConstant.COUNTRY_CODE, message = "{validation.invalid.country.code}")
    private String newCountryCode;

}
