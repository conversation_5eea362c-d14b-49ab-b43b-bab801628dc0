package com.xt.hsk.module.user.controller.app.user;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.user.controller.app.user.vo.UserForgetPasswordReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserLoginReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserLoginRespVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserSmsLoginReqVO;
import com.xt.hsk.module.user.manager.user.UserAuthManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * APP端 - 用户认证
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user/auth")
@Validated
public class AppUserAuthController {

    @Resource
    private UserAuthManager userAuthManager;

    /**
     * 密码登录
     *
     * @param loginReqVO 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public CommonResult<UserLoginRespVO> login(@Valid @RequestBody UserLoginReqVO loginReqVO) {
        return success(userAuthManager.login(loginReqVO));
    }

    /**
     * 验证码登录/注册
     */
    @PostMapping("/app-sms-login")
    public CommonResult<UserLoginRespVO> smsLogin(@Valid @RequestBody UserSmsLoginReqVO reqVO) {
        return success(userAuthManager.smsLogin(reqVO));
    }

    /**
     * 忘记密码 重新设置密码
     */
    @PostMapping("/forget-password")
    public CommonResult<Boolean> forgetPassword(@Valid @RequestBody UserForgetPasswordReqVO reqVO) {
        return success(userAuthManager.forgetPassword(reqVO));
    }

} 