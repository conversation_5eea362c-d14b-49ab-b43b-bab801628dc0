package com.xt.hsk.module.user.controller.app.feedback;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.user.controller.app.feedback.vo.UserFeedbackCreateReqVO;
import com.xt.hsk.module.user.manager.feedback.UserFeedbackAppManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * APP - 用户反馈
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@RestController
@RequestMapping("/user/feedback")
@Validated
public class UserFeedbackAppController {

    @Resource
    private UserFeedbackAppManager userFeedbackAppManager;

    /**
     * 创建用户反馈
     *
     * @param createReqVO 创建反馈请求参数
     * @return 反馈ID
     */
    @PostMapping("/create")
    public CommonResult<Boolean> createUserFeedback(
        @Valid @RequestBody UserFeedbackCreateReqVO createReqVO) {
        userFeedbackAppManager.createUserFeedback(createReqVO);
        return CommonResult.success(true);
    }
} 