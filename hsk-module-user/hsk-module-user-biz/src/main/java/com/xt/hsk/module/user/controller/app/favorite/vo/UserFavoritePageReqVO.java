package com.xt.hsk.module.user.controller.app.favorite.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 用户收藏页面 req vo
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserFavoritePageReqVO extends PageParam {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 收藏类型：1-字词 2-真题题目 3-小游戏题目
     */
    private Integer favoriteType;

    /**
     * 收藏来源：1-互动课作业 2-真题练习 3-专项练习
     */
    private Integer favoriteSource;

    /**
     * 目标ID（字词ID | 题目ID | 小游戏题目ID）
     */
    private Long targetId;
    /**
     * 目标明细id 存在时说明收藏的是一部分内容
     */
    private Long targetDetailId;

    /**
     * 是否错题：1-是 0-否
     */
    private Integer isWrongQuestion;

}
