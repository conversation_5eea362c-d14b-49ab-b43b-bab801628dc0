package com.xt.hsk.module.user.service.user;

import static com.xt.hsk.framework.common.constants.ParamConfigKey.DEFAULT_AVATAR_CONFIG_KEY;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_PHONE_EXISTS;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.constants.UserConstant;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.infra.api.config.ConfigApi;
import com.xt.hsk.module.user.annotation.UserOperateLog;
import com.xt.hsk.module.user.controller.admin.user.vo.UserImportExcelVO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserPageReqVO;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import com.xt.hsk.module.user.dal.dataobject.user.UserOperateLogDO;
import com.xt.hsk.module.user.dal.mysql.user.UserMapper;
import com.xt.hsk.module.user.enums.UserOperateTypeEnum;
import com.xt.hsk.module.user.enums.UserSourceEnum;
import com.xt.hsk.module.user.util.CountryCodeUtils;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 用户 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class UserServiceImpl extends ServiceImpl<UserMapper, UserDO> implements UserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private UserOperateLogService userOperateLogService;

    @Resource
    private ConfigApi configApi;


    @Override
    public PageResult<UserDO> getUserPage(UserPageReqVO pageReqVO) {
        return userMapper.selectPage(pageReqVO);
    }

    @Override
    public UserDO getUserByPhone(String countryCode, String phone) {
        return userMapper.selectByPhone(countryCode, phone);
    }



    @Override
    public boolean isPasswordMatch(String password, String userPassword) {
        return passwordEncoder.matches(password, userPassword);
    }

    @Override
    public void updateUserLogin(Long userId, String clientIp) {
        UserDO userDO = this.getById(userId);
        userDO.setLastLoginTime(LocalDateTime.now());
        userDO.setLastLoginIp(clientIp);
        userMapper.updateById(userDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @UserOperateLog(
        operateType = UserOperateTypeEnum.CREATE,
        userIdSpEL = "#result.id",
        remark = "用户自行注册",
        afterStatusSpEL = "#result.status",
        extraSpEL = "{'countryCode': #countryCode, 'mobile': #mobile, 'userSource': #userSourceEnum.code, 'country': #result.country}"
    )
    public UserDO createUser(String countryCode, String mobile, UserSourceEnum userSourceEnum) {
        UserDO user = new UserDO();
        // 默认用户名 用户 + 手机后四位
        user.setNickname("用户" + mobile.substring(mobile.length() - 4));
        user.setMobile(mobile);
        user.setCountryCode(countryCode);
        user.setLastLoginTime(LocalDateTime.now());
        String clientIp = ServletUtils.getClientIP();
        user.setLastLoginIp(clientIp);

        // 如果IP无法获取国家，则通过手机区号获取
        String country = CountryCodeUtils.getCountryByCode(countryCode, "未知");
        user.setCountry(country);

        user.setPasswordChanged(IsEnum.NO.isIs());
        user.setUserSource(userSourceEnum.getCode());
        user.setStatus(CommonStatusEnum.ENABLE.getStatus());
        user.setDeleteTime(0L);

        // 设置默认头像
        String defaultAvatar = configApi.getConfigValueByKey(DEFAULT_AVATAR_CONFIG_KEY);
        if (StringUtils.isNotBlank(defaultAvatar)) {
            user.setAvatar(defaultAvatar);
        }

        userMapper.insert(user);

        return user;
    }


    @Override
    public void encodePassword(Long id, String password) {
        String encodePassword = encodePassword(password);
        UserDO userDO = new UserDO();
        userDO.setId(id);
        userDO.setPassword(encodePassword);
        userDO.setPasswordChanged(IsEnum.YES.isIs());
        userMapper.updateById(userDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserPassword(Long id, String newPassword) {
        UserDO userDO = validateUserExists(id);
        boolean passwordChanged = userDO.getPasswordChanged();
        // 加密并更新密码
        this.encodePassword(id, newPassword);

        // 记录操作日志
        Map<String, Object> extraMap = new HashMap<>(2);
        extraMap.put("action", passwordChanged ? "change" : "first_set");
        extraMap.put("hasOldPassword", passwordChanged);
        userOperateLogService.save(UserOperateLogDO.builder()
            .userId(id)
            .beforeStatus(passwordChanged ? 1 : 0)
            .afterStatus(1)
            .operateType(UserOperateTypeEnum.CHANGE_PASSWORD.getCode())
            .operatorId(WebFrameworkUtils.getLoginUserId())
            .remark(passwordChanged ? "修改密码" : "首次设置密码")
            .operateIp(ServletUtils.getClientIP())
            .extra(JSON.toJSONString(extraMap))
            .build());
    }

    @Override
    @UserOperateLog(
        operateType = UserOperateTypeEnum.CREATE,
        userIdSpEL = "#result.id",
        remark = "管理员创建",
        afterStatusSpEL = "#result.status",
        recordOperator = true,
        extraSpEL = "{'countryCode': #countryCode, 'mobile': #mobile, 'nickname': #nickname, 'userSource': 'MANUAL_REGISTER', 'defaultPassword': true}"
    )
    public UserDO createUserByAdmin(String countryCode, String mobile, String nickname) {

        UserDO userByPhone = getUserByPhone(countryCode, mobile);
        if (userByPhone != null) {
            throw exception(USER_PHONE_EXISTS);
        }
        UserDO user = new UserDO();
        // 如果传入昵称为空，则使用默认昵称，否则使用传入的昵称
        user.setNickname(
            StrUtil.isBlank(nickname) ? "用户" + mobile.substring(mobile.length() - 4) : nickname);
        user.setMobile(mobile);
        user.setCountryCode(countryCode);
        user.setCountry(CountryCodeUtils.getCountryByCode(countryCode, "未知"));
        // 默认密码
        user.setPassword(encodePassword(UserConstant.DEFAULT_PASSWORD));
        user.setUserSource(UserSourceEnum.MANUAL_REGISTER.getCode());
        user.setStatus(CommonStatusEnum.ENABLE.getStatus());
        user.setDeleteTime(0L);

        // 设置默认头像
        String defaultAvatar = configApi.getConfigValueByKey(DEFAULT_AVATAR_CONFIG_KEY);
        if (StringUtils.isNotBlank(defaultAvatar)) {
            user.setAvatar(defaultAvatar);
        }

        userMapper.insert(user);
        return user;
    }

    @Override
    @UserOperateLog(
        operateType = UserOperateTypeEnum.RESET_PASSWORD,
        userIdSpEL = "#id",
        remark = "管理员重置密码",
        beforeStatusSpEL = "0",
        afterStatusSpEL = "1",
        recordOperator = true
    )
    public void resetPassword(Long id) {
        // 设置为默认密码
        String encodePassword = encodePassword(UserConstant.DEFAULT_PASSWORD);
        UserDO userDO = new UserDO();
        userDO.setId(id);
        userDO.setPassword(encodePassword);
        userDO.setPasswordChanged(IsEnum.YES.isIs());
        userMapper.updateById(userDO);
    }

    @Override
    @UserOperateLog(
        operateType = UserOperateTypeEnum.DELETE,
        userIdSpEL = "#id",
        remark = "管理员删除",
        recordOperator = true
    )
    public void adminDeleteUser(Long id) {
        UserDO userDO = new UserDO();
        userDO.setId(id);
        userDO.setDeleteTime(DateUtil.currentSeconds());
        userMapper.updateById(userDO);
        userMapper.deleteById(id);

        // 踢人下线
        StpUtil.kickout(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatus(Long id) {
        // 校验存在
        UserDO userDO = validateUserExists(id);
        userDO.setStatus(userDO.getStatus() == 0 ? 1 : 0);
        userMapper.updateById(userDO);

        UserOperateTypeEnum userOperateTypeEnum;
        String remark = "";
        if (userDO.getStatus() == 0) {
            userOperateTypeEnum = UserOperateTypeEnum.ENABLE;
            remark = "启用用户";
        } else {
            // 踢人下线
            StpUtil.kickout(id);
            userOperateTypeEnum = UserOperateTypeEnum.DISABLE;
            remark = "禁用用户";
        }
        userOperateLogService.save(UserOperateLogDO.builder()
            .userId(id)
            .operateType(userOperateTypeEnum.getCode())
            .operatorId(WebFrameworkUtils.getLoginUserId())
            .remark(remark)
            .operateIp(ServletUtils.getClientIP())
            .build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @UserOperateLog(
        operateType = UserOperateTypeEnum.LOGOUT,
        userIdSpEL = "#userId",
        remark = "用户注销账号",
        beforeStatusSpEL = "0",
        afterStatusSpEL = "1"
    )
    public void logoutUser(Long userId) {
        // 校验用户存在
        UserDO userDO = validateUserExists(userId);

        // 设置删除时间（软删除）
        userDO.setDeleteTime(DateUtil.currentSeconds());
        userMapper.updateById(userDO);

        // 删除用户记录
        userMapper.deleteById(userId);

        // 踢用户下线
        StpUtil.kickout(userId);
    }

    @Override
    public boolean existsByPhone(String countryCode, String mobile) {
        return getUserByPhone(countryCode, mobile) != null;
    }

    @Override
    public Set<String> batchCheckPhonesExist(List<String> countryCodes, List<String> mobiles) {
        if (countryCodes == null || mobiles == null || countryCodes.size() != mobiles.size()) {
            throw new IllegalArgumentException("区号列表和手机号列表必须匹配且不能为空");
        }

        // 使用批量查询获取已存在的手机号
        return userMapper.batchCheckPhones(countryCodes, mobiles);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchCreateUsersByAdmin(List<UserImportExcelVO> importUsers) {
        if (importUsers == null || importUsers.isEmpty()) {
            return Collections.emptyList();
        }

        String clientIp = ServletUtils.getClientIP();
        Long operatorId = WebFrameworkUtils.getLoginUserId();
        List<Long> userIds = new ArrayList<>(importUsers.size());
        List<UserDO> userList = new ArrayList<>(importUsers.size());
        List<UserOperateLogDO> logList = new ArrayList<>(importUsers.size());

        // 设置默认头像
        String defaultAvatar = configApi.getConfigValueByKey(DEFAULT_AVATAR_CONFIG_KEY);

        // 1. 准备用户数据
        for (UserImportExcelVO userVO : importUsers) {
            UserDO user = new UserDO();
            user.setNickname(StringUtils.isBlank(userVO.getNickname()) ?
                "用户" + userVO.getMobile().substring(userVO.getMobile().length() - 4) :
                userVO.getNickname());
            user.setMobile(userVO.getMobile());
            user.setCountryCode(userVO.getCountryCode());
            user.setCountry(CountryCodeUtils.getCountryByCode(userVO.getCountryCode(), "未知"));
            user.setPassword(encodePassword(UserConstant.DEFAULT_PASSWORD));
            user.setUserSource(UserSourceEnum.MANUAL_REGISTER.getCode());
            user.setStatus(CommonStatusEnum.ENABLE.getStatus());
            user.setDeleteTime(0L);

            // 设置默认头像
            if (StringUtils.isNotBlank(defaultAvatar)) {
                user.setAvatar(defaultAvatar);
            }

            userList.add(user);
        }

        // 2. 批量插入用户
        userMapper.insertBatch(userList);

        // 3. 准备日志数据
        for (UserDO user : userList) {
            userIds.add(user.getId());

            UserOperateLogDO log = UserOperateLogDO.builder()
                .userId(user.getId())
                .operateType(UserOperateTypeEnum.CREATE.getCode())
                .operatorId(operatorId)
                .remark("管理员批量创建")
                .operateIp(clientIp)
                .build();

            logList.add(log);
        }

        // 4. 批量插入日志
        userOperateLogService.saveBatch(logList);

        return userIds;
    }


    /**
     * 验证用户是否存在
     *
     * @param id 身份证
     * @return {@code UserDO }
     */
    private UserDO validateUserExists(Long id) {
        UserDO userDO = this.getById(id);
        if (userDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return userDO;
    }

    @Override
    public void updateUserMobile(Long userId, String newCountryCode, String newMobile) {
        // 获取原用户信息
        UserDO oldUser = validateUserExists(userId);

        // 通过代理对象调用内部方法，确保 AOP 生效
        // 检查新手机号是否已存在
        UserDO existingUser = getUserByPhone(newCountryCode, newMobile);
        if (existingUser != null && !existingUser.getId().equals(userId)) {
            throw exception(USER_PHONE_EXISTS);
        }

        // 检查新手机号是否与原手机号相同
        if (oldUser.getCountryCode().equals(newCountryCode) && oldUser.getMobile().equals(newMobile)) {
            log.info("新手机号与原手机号相同，无需修改: userId={}, newMobile={}:{}", userId, newCountryCode, newMobile);
            return;
        }


        // 更新手机号
        UserDO updateUser = new UserDO();
        updateUser.setId(userId);
        updateUser.setCountryCode(newCountryCode);
        updateUser.setMobile(newMobile);
        userMapper.updateById(updateUser);

        Map<String, String> extraMap = new HashMap<>(4);
        extraMap.put("oldCountryCode", oldUser.getCountryCode());
        extraMap.put("oldMobile", oldUser.getMobile());
        extraMap.put("newCountryCode", newCountryCode);
        extraMap.put("newMobile", newMobile);

        // 记录操作日志
        userOperateLogService.save(UserOperateLogDO.builder()
            .userId(userId)
            .operateType(UserOperateTypeEnum.UPDATE_MOBILE.getCode())
            .operatorId(WebFrameworkUtils.getLoginUserId())
            .remark("修改手机号")
            .operateIp(ServletUtils.getClientIP())
            .extra(JSON.toJSONString(extraMap))
            .build());

        // 踢下线
        StpUtil.kickout(userId);
    }

    /**
     * 加密密码
     */
    private String encodePassword(String password) {
        return passwordEncoder.encode(password);
    }

}