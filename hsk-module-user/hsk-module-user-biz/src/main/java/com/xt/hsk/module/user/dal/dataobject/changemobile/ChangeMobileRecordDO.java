package com.xt.hsk.module.user.dal.dataobject.changemobile;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import com.xt.hsk.module.user.enums.ChangeMobileState;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 用户更换手机号记录 DO
 *
 * <AUTHOR>
 * @since 2025/07/28
 */
@TableName("user_change_mobile_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeMobileRecordDO extends AppBaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会话ID，用于关联Redis会话
     */
    private String sessionId;

    /**
     * 当前状态
     */
    private ChangeMobileState state;

    // ========== 旧手机号信息 ==========

    /**
     * 原手机号国家区号
     */
    private String oldCountryCode;

    /**
     * 原手机号
     */
    private String oldMobile;

    /**
     * 旧手机号是否已验证
     */
    private Boolean oldMobileVerified;

    /**
     * 旧手机号验证时间
     */
    private LocalDateTime oldMobileVerifiedTime;

    // ========== 新手机号信息 ==========

    /**
     * 新手机号国家区号
     */
    private String newCountryCode;

    /**
     * 新手机号
     */
    private String newMobile;

    /**
     * 新手机号验证码是否已发送
     */
    private Boolean newMobileCodeSent;

    /**
     * 新手机号验证码发送时间
     */
    private LocalDateTime newMobileCodeSentTime;

    // ========== 操作信息 ==========

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 客户端IP地址
     */
    private String clientIp;

    /**
     * 用户代理信息
     */
    private String userAgent;

    // ========== 完成信息 ==========

    /**
     * 更换完成时间
     */
    private LocalDateTime completedTime;

    /**
     * 失败原因
     */
    private String failedReason;

    /**
     * 失败时间
     */
    private LocalDateTime failedTime;

}
