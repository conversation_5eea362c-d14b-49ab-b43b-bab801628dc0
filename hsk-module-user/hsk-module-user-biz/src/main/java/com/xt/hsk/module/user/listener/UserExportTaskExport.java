package com.xt.hsk.module.user.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.BasicEnumUtil;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import com.xt.hsk.module.user.controller.admin.user.vo.UserAdminRespVO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserPageReqVO;
import com.xt.hsk.module.user.convert.appuser.AppUserConvert;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import com.xt.hsk.module.user.enums.UserSourceEnum;
import com.xt.hsk.module.user.service.user.UserService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户导出任务导出器
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Component
@Slf4j
public class UserExportTaskExport extends BaseEasyExcelExport<UserAdminRespVO> {

    @Resource
    private UserService userService;

    @Override
    protected List<List<String>> getExcelHead() {
        // 直接在这里定义表头，方便开发人员查看和维护
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("用户ID"));
        head.add(Collections.singletonList("区号"));
        head.add(Collections.singletonList("用户手机号"));
        head.add(Collections.singletonList("用户昵称"));
        head.add(Collections.singletonList("所属地区"));
        head.add(Collections.singletonList("用户来源"));
        head.add(Collections.singletonList("状态"));
        head.add(Collections.singletonList("创建时间"));
        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        // 手动转换Map到UserPageReqVO对象
        UserPageReqVO dto = convertMapToUserPageReqVO(conditions);

        // 如果有选中的ID，返回选中的数量
        if (CollUtil.isNotEmpty(dto.getIds())) {
            return (long) dto.getIds().size();
        }

        // 获取符合条件的用户总数
        PageResult<UserDO> userPage = userService.getUserPage(dto);
        return userPage.getTotal();
    }

    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition,
        Long pageNo, Long pageSize) {
        // 转换查询条件
        UserPageReqVO dto = convertMapToUserPageReqVO(queryCondition);
        dto.setPageNo(Math.toIntExact(pageNo));
        dto.setPageSize(Math.toIntExact(pageSize));

        // 查询符合条件的用户
        PageResult<UserDO> userPage = userService.getUserPage(dto);
        List<UserDO> list = userPage.getList();

        // 将 UserDO 转换为 UserRespVO
        List<UserAdminRespVO> userAdminRespVOList = AppUserConvert.INSTANCE.convertList2(list);

        // 设置枚举描述信息
        userAdminRespVOList.forEach(item -> {
            if (item.getUserSource() != null) {
                String desc = BasicEnumUtil.getDescByCode(UserSourceEnum.class,
                    item.getUserSource());
                item.setUserSourceStr(desc);
            }
        });

        // 构建导出数据行
        for (UserAdminRespVO userAdminRespVO : userAdminRespVOList) {
            List<String> row = new ArrayList<>();

            // 用户ID
            row.add(userAdminRespVO.getId() != null ? userAdminRespVO.getId().toString() : "");

            // 区号
            row.add(
                userAdminRespVO.getCountryCode() != null ? userAdminRespVO.getCountryCode()
                    .toString() : "");

            // 用户手机号
            row.add(userAdminRespVO.getMobile() != null ? userAdminRespVO.getMobile() : "");

            // 用户昵称
            row.add(userAdminRespVO.getNickname() != null ? userAdminRespVO.getNickname() : "");

            // 所属地区
            row.add(userAdminRespVO.getCountry() != null ? userAdminRespVO.getCountry() : "");

            // 用户来源
            row.add(
                userAdminRespVO.getUserSourceStr() != null ? userAdminRespVO.getUserSourceStr()
                    : "");

            // 状态
            String statusStr = "";
            if (userAdminRespVO.getStatus() != null) {
                statusStr = userAdminRespVO.getStatus() == 0 ? "启用" : "禁用";
            }
            row.add(statusStr);

            // 创建时间
            row.add(userAdminRespVO.getCreateTime() != null ?
                DateUtil.format(userAdminRespVO.getCreateTime(), "yyyy-MM-dd HH:mm:ss") : "");

            resultList.add(row);
        }
        log.info("用户导出，当前页：{}，每页条数：{}，总条数：{}", pageNo, pageSize, userPage.getTotal());
    }

    /**
     * 将Map转换为UserPageReqVO对象
     *
     * @param map 包含查询条件的Map
     * @return UserPageReqVO对象
     */
    private UserPageReqVO convertMapToUserPageReqVO(Map<String, Object> map) {
        UserPageReqVO reqVO = new UserPageReqVO();
        BeanUtil.copyProperties(map, reqVO);
        return reqVO;
    }
} 