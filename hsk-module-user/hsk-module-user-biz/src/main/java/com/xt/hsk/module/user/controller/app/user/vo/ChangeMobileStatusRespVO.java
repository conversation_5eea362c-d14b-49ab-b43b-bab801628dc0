package com.xt.hsk.module.user.controller.app.user.vo;

import com.xt.hsk.module.user.enums.ChangeMobileState;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 修改手机号状态响应 VO
 *
 * <AUTHOR>
 * @since 2025/07/25
 */
@Schema(description = "修改手机号状态响应 VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeMobileStatusRespVO {

    @Schema(description = "会话ID")
    private String sessionId;

    @Schema(description = "当前状态")
    private ChangeMobileState state;

    @Schema(description = "旧手机号是否已验证")
    private Boolean oldMobileVerified;

    @Schema(description = "新手机号验证码是否已发送")
    private Boolean newMobileCodeSent;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
