package com.xt.hsk.module.user.controller.app.user;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.user.controller.app.user.vo.UserBehaviorSaveVo;
import com.xt.hsk.module.user.controller.app.user.vo.UserCenterRespVO;
import com.xt.hsk.module.user.manager.user.AppUserManager;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * APP-用户中心相关接口
 */
@RestController
@RequestMapping("/user/center")
@Validated
public class AppUserCenterController {
    @Resource
    private AppUserManager appUserManager;

    /**
     * 获取用户中心信息
     */
    @PostMapping("/info")
    public CommonResult<UserCenterRespVO> getUserCenterInfo() {

        return CommonResult.success(appUserManager.getUserCenterInfo());
    }

    /**
     * 记录用户行为
     */
    @PostMapping("/recordUserBehavior")
    public CommonResult<Long> recordUserBehavior(UserBehaviorSaveVo reqVO) {
        return CommonResult.success(appUserManager.recordUserBehavior(reqVO));
    }


}
