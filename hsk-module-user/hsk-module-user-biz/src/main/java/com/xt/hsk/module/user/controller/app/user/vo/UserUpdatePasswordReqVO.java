package com.xt.hsk.module.user.controller.app.user.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * APP用户修改密码 Request VO
 *
 * <AUTHOR>
 * @since 2025/07/10
 */
@Data
public class UserUpdatePasswordReqVO {

    /**
     * 原密码
     */
    @NotEmpty(message = "{validation.old.password.required}")
    private String oldPassword;

    /**
     * 新密码：需在8-16位，必须包括字母和数字
     */
    @NotEmpty(message = "{validation.new.password.required}")
    @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d).{8,16}$", message = "{validation.password.invalid}")
    private String newPassword;

    /**
     * 确认新密码
     */
    @NotEmpty(message = "{validation.confirm.password.required}")
    private String confirmPassword;
}
