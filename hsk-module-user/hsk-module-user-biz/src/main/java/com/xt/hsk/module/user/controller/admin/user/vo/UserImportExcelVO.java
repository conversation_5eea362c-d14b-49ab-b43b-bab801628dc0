package com.xt.hsk.module.user.controller.admin.user.vo;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用户 Excel 导入 VO
 *
 * <AUTHOR>
 * @since 2025/06/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class UserImportExcelVO {

    @ExcelProperty("*区号")
    private String countryCode;

    @ExcelProperty("*用户手机号")
    private String mobile;

    @ExcelProperty("用户昵称")
    private String nickname;


} 