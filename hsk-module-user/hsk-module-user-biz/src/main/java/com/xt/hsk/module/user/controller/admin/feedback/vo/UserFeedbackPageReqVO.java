package com.xt.hsk.module.user.controller.admin.feedback.vo;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import com.xt.hsk.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 用户反馈分页查询 Request VO
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserFeedbackPageReqVO extends PageParam {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 手机区号
     */
    private String countryCode;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 联系方式类型 1 whchat 2 e-mail 3 zalo 4 手机号 5whatapp 6Facebook
     */
    private Integer contactInfoType;

    /**
     * 联系方式
     */
    private String contactInfo;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 反馈时间范围
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

} 