package com.xt.hsk.module.user.controller.admin.user.vo;

import com.xt.hsk.framework.common.constants.RegexpConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Schema(description = "管理后台 - 用户创建 Request VO")
@Data
public class UserCreateReqVO {

    /**
     * 用户昵称
     */
    @NotBlank(message = "用户昵称不能为空")
    @Size(max = 30, message = "用户昵称长度不能超过30个字符")
    private String nickname;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = RegexpConstant.MOBILE, message = "手机号必须是6-11位数字")
    private String mobile;

    /**
     * 手机区号
     */
    @NotBlank(message = "手机区号不能为空")
    @Size(max = 6, message = "手机区号最长6位")
    private String countryCode;

} 