package com.xt.hsk.module.user.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.user.controller.app.user.vo.UserExtPageReqVO;
import com.xt.hsk.module.user.dal.dataobject.ext.UserExtDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import jakarta.validation.Valid;

/**
 * 用户扩展 Service 接口
 *
 * <AUTHOR>
 */
public interface UserExtService extends IService<UserExtDO> {
    PageResult<UserExtDO> selectPage(@Valid UserExtPageReqVO pageReqVO);

}