package com.xt.hsk.module.user.manager.feedback;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.user.controller.admin.feedback.dto.UserFeedbackWithUserDTO;
import com.xt.hsk.module.user.controller.admin.feedback.vo.UserFeedbackPageReqVO;
import com.xt.hsk.module.user.controller.admin.feedback.vo.UserFeedbackRespVO;
import com.xt.hsk.module.user.convert.feedback.UserFeedbackConvert;
import com.xt.hsk.module.user.service.feedback.UserFeedbackService;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 用户反馈 Manager
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Service
public class UserFeedbackAdminManager {

    @Resource
    private UserFeedbackService userFeedbackService;

    /**
     * 获取用户反馈分页
     *
     * @param reqVO 查询条件
     * @return 反馈分页结果
     */
    public PageResult<UserFeedbackRespVO> getUserFeedbackPage(UserFeedbackPageReqVO reqVO) {
        // 直接通过 Service 获取包含用户信息的分页数据
        PageResult<UserFeedbackWithUserDTO> pageResult = userFeedbackService.getUserFeedbackWithUserPage(reqVO);
        
        // 如果没有数据，直接返回空结果
        if (pageResult.getList().isEmpty()) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }
        
        // 转换为VO并返回
        List<UserFeedbackRespVO> respList = UserFeedbackConvert.INSTANCE.convertList(pageResult.getList());
        return new PageResult<>(respList, pageResult.getTotal());
    }

    /**
     * 获取用户反馈详情
     *
     * @param id 反馈ID
     * @return 反馈详情
     */
    public UserFeedbackRespVO getUserFeedbackDetail(Long id) {
        // 直接通过 Service 获取包含用户信息的反馈详情
        UserFeedbackWithUserDTO feedbackDTO = userFeedbackService.getUserFeedbackWithUser(id);
        if (feedbackDTO == null) {
            return null;
        }

        return UserFeedbackConvert.INSTANCE.convert(feedbackDTO);
    }
} 