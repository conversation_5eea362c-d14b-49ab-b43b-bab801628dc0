package com.xt.hsk.module.user.controller.admin.user.vo;

import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 管理后台 - 用户响应数据对象。 用于封装用户信息返回给前端的数据结构。
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAdminRespVO implements java.io.Serializable {

    /**
     * 用户唯一标识。 示例值：1。
     */
    private Long id;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 用户昵称。
     * 示例值：张三。
     */
    private String nickname;

    /**
     * 手机号码。
     * 示例值：13888888888。
     */
    private String mobile;

    /**
     * 手机区号。
     * 示例值：86。
     */
    private Integer countryCode;

    /**
     * 所属地区（国家）。
     * 示例值：中国。
     */
    private String country;

    /**
     * 当前HSK等级。
     * 示例值：3。
     */
    private Integer currentHskLevel;

    /**
     * 出生日期。
     * 示例值：1990-01-01。
     */
    private LocalDate birthDate;

    /**
     * 考试时间。
     * 示例值：2024-06-01。
     */
    private LocalDate examDate;


    /**
     * 职业。
     * 示例值：1。
     */
    private Integer profession;

    /**
     * 学习汉语的目的。学习汉语目的（0-其他 1-留学 2-旅游 3-职业发展 4-个人兴趣）
     * 示例值：1。
     */
    private Integer learningPurpose;

    /**
     * 汉语水平。
     * 示例值：2。
     */
    private Integer chineseLevel;

    /**
     * 目标HSK等级。
     * 示例值：5。
     */
    private Integer targetHskLevel;

    /**
     * 计划考试时间。
     * 示例值：2024-12-01。
     */
    private LocalDate plannedExamDate;

    /**
     * 用户来源渠道。
     */
    private Integer userSource;

    /**
     * 用户来源渠道名称。
     */
    private String userSourceStr;

    /**
     * 用户当前状态。 示例值：0。
     */
    private Integer status;

    /**
     * 用户创建时间。
     */
    private LocalDateTime createTime;

    /**
     * 中文水平 STR
     */
    private String chineseLevelStr;
    /**
     * 专业 STR
     */
    private String professionStr;
    /**
     * 学习目的 STR
     */
    private String learningPurposeStr;

}
