package com.xt.hsk.module.user.controller.admin.feedback;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.user.controller.admin.feedback.vo.UserFeedbackPageReqVO;
import com.xt.hsk.module.user.controller.admin.feedback.vo.UserFeedbackRespVO;
import com.xt.hsk.module.user.manager.feedback.UserFeedbackAdminManager;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 后台管理 - 用户反馈
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@RestController
@RequestMapping("/user/feedback")
@Validated
public class UserFeedbackAdminController {

    @Resource
    private UserFeedbackAdminManager userFeedbackAdminManager;

    /**
     * 获得用户反馈分页
     *
     * @param pageReqVO 分页请求参数
     * @return 用户反馈分页结果
     */
    @PostMapping("/page")
    @PreAuthorize("@ss.hasPermission('user:feedback:query')")
    public CommonResult<PageResult<UserFeedbackRespVO>> getUserFeedbackPage(
        @RequestBody UserFeedbackPageReqVO pageReqVO) {
        return CommonResult.success(userFeedbackAdminManager.getUserFeedbackPage(pageReqVO));
    }

    /**
     * 获得用户反馈详情
     *
     * @param id 编号
     * @return 用户反馈详情
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('user:feedback:query')")
    public CommonResult<UserFeedbackRespVO> getUserFeedback(@RequestParam("id") Long id) {
        return CommonResult.success(userFeedbackAdminManager.getUserFeedbackDetail(id));
    }
} 