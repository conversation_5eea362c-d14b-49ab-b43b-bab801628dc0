package com.xt.hsk.module.user.controller.app.feedback.vo;

import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.user.enums.ContactInfoTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 用户反馈创建 Request VO
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Data
public class UserFeedbackCreateReqVO {

    /**
     * 反馈内容，必填，最大长度1000个字符
     */
    @NotBlank(message = "请先填写反馈内容")
    @Size(max = 1000, message = "反馈内容不能超过 1000 个字符")
    private String content;

    /**
     * 区号，最大长度6个字符
     */
    private String countryCode;

    /**
     * 联系方式类型 1 whchat 2 e-mail 3 zalo 4 手机号 5whatapp 6Facebook
     */
    @InEnum(ContactInfoTypeEnum.class)
    @NotNull(message = "请先填写联系方式")
    private Integer contactInfoType;

    /**
     * 联系方式，最大长度64个字符
     */
    @NotNull(message = "请先填写联系方式")
    private String contactInfo;
} 