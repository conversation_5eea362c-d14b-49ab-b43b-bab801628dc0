package com.xt.hsk.module.user.controller.app.user.vo;

import com.xt.hsk.framework.common.constants.RegexpConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 验证旧手机号 Request VO
 *
 * <AUTHOR>
 * @since 2025/07/25
 */
@Schema(description = "验证旧手机号 Request VO")
@Data
public class VerifyOldMobileReqVO {

    /**
     * 验证码
     */
    @Schema(description = "验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotBlank(message = "验证码不能为空")
    @Pattern(regexp = RegexpConstant.MOBILE_CODE, message = "{validation.verification.code.invalid}")
    private String code;
}
