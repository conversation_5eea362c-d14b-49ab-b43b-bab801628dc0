package com.xt.hsk.module.user.service.feedback;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.user.controller.admin.feedback.dto.UserFeedbackWithUserDTO;
import com.xt.hsk.module.user.controller.admin.feedback.vo.UserFeedbackPageReqVO;
import com.xt.hsk.module.user.controller.app.feedback.vo.UserFeedbackCreateReqVO;
import com.xt.hsk.module.user.dal.dataobject.feedback.UserFeedbackDO;

/**
 * 用户反馈 Service 接口
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
public interface UserFeedbackService extends IService<UserFeedbackDO> {

    /**
     * 创建用户反馈
     *
     * @param userId      用户编号
     * @param createReqVO 创建信息
     * @return 反馈编号
     */
    Long createUserFeedback(Long userId, UserFeedbackCreateReqVO createReqVO);

    /**
     * 获取用户反馈分页数量
     *
     * @param pageReqVO 分页查询
     * @return 用户反馈数量
     */
    Long getUserFeedbackPageCount(UserFeedbackPageReqVO pageReqVO);

    /**
     * 检查用户当天反馈次数是否达到上限
     *
     * @param userId 用户ID
     * @return 是否达到上限
     */
    boolean checkUserFeedbackLimit(Long userId);
    
    /**
     * 获取用户反馈分页（包含用户信息）
     *
     * @param pageReqVO 分页查询
     * @return 用户反馈分页结果（包含用户信息）
     */
    PageResult<UserFeedbackWithUserDTO> getUserFeedbackWithUserPage(UserFeedbackPageReqVO pageReqVO);

    /**
     * 获取用户反馈详情（包含用户信息）
     *
     * @param id 反馈ID
     * @return 包含用户信息的反馈详情
     */
    UserFeedbackWithUserDTO getUserFeedbackWithUser(Long id);
} 