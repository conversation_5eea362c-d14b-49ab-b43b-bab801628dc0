package com.xt.hsk.module.user.service.loginlog;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.user.dal.dataobject.loginlog.UserLoginLogDO;
import com.xt.hsk.module.user.dto.AppUserLoginLogCreateReqDTO;

/**
 * 用户登录日志 Service 接口
 *
 * <AUTHOR>
 * @since 2025/05/29
 */
public interface UserLoginLogService extends IService<UserLoginLogDO> {

    void createLoginLog(AppUserLoginLogCreateReqDTO reqDTO);
}