package com.xt.hsk.module.user.service.favorite;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.user.controller.app.favorite.vo.*;
import com.xt.hsk.module.user.dal.dataobject.favorite.UserFavoriteDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 用户收藏 Service 接口
 *
 * <AUTHOR>
 * @since 2025/07/22
 */
public interface UserFavoriteService extends IService<UserFavoriteDO> {


    /**
     * 收藏的小游戏题目列表
     *
     * @param reqVO 分页参数
     * @return 结果列表
     */
    PageResult<FavoriteGameQuestionsAppPageRespVO> favoriteGameList(GameFavoriteAppPageReqVO reqVO);

    /**
     * 获取用户收藏分页
     *
     * @param pageReqVO 分页参数
     * @return 用户收藏分页
     */
    PageResult<UserFavoriteDO> selectPage(@Valid UserFavoritePageReqVO pageReqVO);

    /**
     * 添加用户收藏
     *
     * @param userId          用户ID
     * @param favoriteType    收藏类型
     * @param favoriteSource  收藏来源
     * @param targetId        收藏目标ID
     * @param isWrongQuestion 是否错题
     * @return 批量添加结果
     */
    int add(Long userId, Integer favoriteType, Integer favoriteSource, Long targetId,
        Boolean isWrongQuestion);

    /**
     * 取消用户收藏
     *
     * @param userId         用户ID
     * @param favoriteType   收藏类型
     * @param targetId       删除目标ID
     * @return 删除数量
     */
    int cancel(Long userId, Integer favoriteType, Long targetId);

    /**
     * 批量添加用户收藏
     *
     * @param userId         用户ID
     * @param favoriteType   收藏类型
     * @param favoriteSource 收藏来源
     * @param targetIdList   收藏目标ID列表
     * @param isWrongQuestion 是否错题
     * @return 收藏数量
     */
    Boolean addBatch(Long userId, Integer favoriteType, Integer favoriteSource,
        List<Long> targetIdList, Boolean isWrongQuestion);

    /**
     * 批量收藏小游戏题目
     *
     * @param userId          用户ID
     * @param favoriteSource  收藏来源
     * @param targetIdList    收藏目标ID列表
     * @param isWrongQuestion 是否错题
     * @param gameId          专项练习ID
     * @return 收藏数量
     */
    Boolean addGameQuestionBatch(Long userId, Integer favoriteSource,
        List<Long> targetIdList, Long gameId, Boolean isWrongQuestion);

    /**
     * 批量取消用户收藏
     *
     * @param userId         用户ID
     * @param favoriteType   收藏类型
     * @param targetIdList   删除目标ID列表
     * @return 删除数量
     */
    int cancelBatch(Long userId, Integer favoriteType,
        List<Long> targetIdList);

    /**
     * 根据目标ID列表查询收藏状态
     *
     * @param userId       用户ID
     * @param targetIdList 目标ID列表
     * @param favoriteType 收藏类型
     * @return map<目标ID, 收藏状态>
     */
    Map<Long, Boolean> selectStatus(Long userId, List<Long> targetIdList, Integer favoriteType);

    /**
     * 收藏题目列表
     *
     * @param reqVO 分页参数
     * @return 结果列表
     */
    List<FavoriteQuestionPageRespVO> favoriteQuestionList(FavoriteQuestionAppPageReqVO reqVO);

    /**
     * 分页查询收藏的单词列表
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    PageResult<FavoriteWordAppPageRespVO> wordBookList(FavoriteWordAppPageReqVO reqVO);

    /**
     * 批量收藏真题题目
     *
     * @param userId 用户ID
     * @param favoriteSource 收藏类型
     * @param targetList 目标ID列表
     * @param isWrongQuestion 是否是错题
     * @return
     */
    Boolean addRealQuestionBatch(Long userId, Integer favoriteSource, List<UserFavoriteTargetVo> targetList, Boolean isWrongQuestion);
}
