package com.xt.hsk.module.user.service.feedback;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_FEEDBACK_DAILY_LIMIT_REACHED;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import com.xt.hsk.module.user.controller.admin.feedback.dto.UserFeedbackWithUserDTO;
import com.xt.hsk.module.user.controller.admin.feedback.vo.UserFeedbackPageReqVO;
import com.xt.hsk.module.user.controller.app.feedback.vo.UserFeedbackCreateReqVO;
import com.xt.hsk.module.user.convert.feedback.UserFeedbackConvert;
import com.xt.hsk.module.user.dal.dataobject.feedback.UserFeedbackDO;
import com.xt.hsk.module.user.dal.mysql.feedback.UserFeedbackMapper;
import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 用户反馈 Service 实现类
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Service
@Validated
@Slf4j
public class UserFeedbackServiceImpl extends
    ServiceImpl<UserFeedbackMapper, UserFeedbackDO> implements UserFeedbackService {

    /**
     * 每日反馈上限
     */
    private static final int MAX_FEEDBACK_PER_DAY = 5;

    @Resource
    private UserFeedbackMapper userFeedbackMapper;

    @Override
    public Long createUserFeedback(Long userId, UserFeedbackCreateReqVO createReqVO) {
        // 1. 校验用户反馈次数是否达到上限
        if (checkUserFeedbackLimit(userId)) {
            throw exception(USER_FEEDBACK_DAILY_LIMIT_REACHED);
        }

        // 2. 创建用户反馈
        UserFeedbackDO userFeedback = UserFeedbackConvert.INSTANCE.convert(createReqVO);
        userFeedback.setOperateIp(ServletUtils.getClientIP());
        userFeedback.setUserId(userId);
        userFeedbackMapper.insert(userFeedback);
        return userFeedback.getId();
    }


    @Override
    public Long getUserFeedbackPageCount(UserFeedbackPageReqVO pageReqVO) {
        // 这里可以使用简单的计数查询，不需要联表
        return lambdaQuery()
            .eq(pageReqVO.getUserId() != null, UserFeedbackDO::getUserId, pageReqVO.getUserId())
            .like(pageReqVO.getContent() != null && !pageReqVO.getContent().isEmpty(),
                UserFeedbackDO::getContent, pageReqVO.getContent())
            .eq(pageReqVO.getContactInfoType() != null,
                UserFeedbackDO::getContactInfoType, pageReqVO.getContactInfoType())
            .like(pageReqVO.getContactInfo() != null && !pageReqVO.getContactInfo().isEmpty(),
                UserFeedbackDO::getContactInfo, pageReqVO.getContactInfo())
            .between(pageReqVO.getCreateTime() != null && pageReqVO.getCreateTime().length == 2,
                UserFeedbackDO::getCreateTime,
                pageReqVO.getCreateTime() != null ? pageReqVO.getCreateTime()[0] : null,
                pageReqVO.getCreateTime() != null ? pageReqVO.getCreateTime()[1] : null)
            .count();
    }

    @Override
    public boolean checkUserFeedbackLimit(Long userId) {
        // 获取当天的开始和结束时间
        LocalDate today = LocalDate.now();
        String startOfDay = today.atStartOfDay()
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endOfDay = today.atTime(23, 59, 59)
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 统计用户当天的反馈次数
        Long count = userFeedbackMapper.countUserTodayFeedbacks(userId, startOfDay, endOfDay);
        return count >= MAX_FEEDBACK_PER_DAY;
    }
    
    @Override
    public PageResult<UserFeedbackWithUserDTO> getUserFeedbackWithUserPage(UserFeedbackPageReqVO pageReqVO) {
        // 直接使用Mapper的分页方法
        return userFeedbackMapper.selectJoinPage(pageReqVO, UserFeedbackWithUserDTO.class, 
                userFeedbackMapper.buildJoinWrapper(pageReqVO));
    }

    @Override
    public UserFeedbackWithUserDTO getUserFeedbackWithUser(Long id) {
        return userFeedbackMapper.selectJoinById(id);
    }
} 