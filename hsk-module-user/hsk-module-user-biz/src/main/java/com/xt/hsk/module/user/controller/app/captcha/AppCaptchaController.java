package com.xt.hsk.module.user.controller.app.captcha;

import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import com.xt.hsk.framework.tenant.core.aop.TenantIgnore;
import com.xt.hsk.module.system.api.captcha.CaptchaApi;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * app端验证码
 *
 * <AUTHOR>
 * @since 2025/05/29
 */
@RestController
@RequestMapping("/captcha")
@Slf4j
public class AppCaptchaController {

    @Resource
    private CaptchaApi captchaApi;

    @PostMapping({"/get"})
    @Operation(summary = "获得验证码")
    @TenantIgnore
    public CommonResult<ResponseModel> get(@RequestBody CaptchaVO data,
        HttpServletRequest request) {
        if (request.getRemoteHost() == null) {
            throw new AssertionError();
        }
        data.setBrowserInfo(getRemoteId(request));
        return CommonResult.success(captchaApi.get(data, request));
    }

    @PostMapping("/check")
    @Operation(summary = "校验验证码")
    @TenantIgnore
    public CommonResult<ResponseModel> check(@RequestBody CaptchaVO data,
        HttpServletRequest request) {
        data.setBrowserInfo(getRemoteId(request));
        return CommonResult.success(captchaApi.check(data, request));
    }

    public static String getRemoteId(HttpServletRequest request) {
        String ip = ServletUtils.getClientIP(request);
        String ua = request.getHeader("user-agent");
        if (StringUtils.isNotBlank(ip)) {
            return ip + ua;
        }
        return request.getRemoteAddr() + ua;
    }
}
