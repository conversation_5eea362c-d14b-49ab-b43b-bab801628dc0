package com.xt.hsk.module.user.controller.app.user.vo;

import com.xt.hsk.framework.common.constants.RegexpConstant;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 用户忘记密码 req vo
 *
 * <AUTHOR>
 * @since 2025/06/06
 */
@Data
public class UserForgetPasswordReqVO {

    /**
     * 新密码 8-16位 必须包含字母和数字
     */
    @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d).{8,16}$", message = "密码格式不正确：8-16位 必须包含字母和数字")
    private String newPassword;

    /**
     * 手机号码
     */
    @Pattern(regexp = RegexpConstant.MOBILE, message = "{validation.mobile.invalid}")
    private String mobile;

    /**
     * 手机区号
     */
    @Pattern(regexp = RegexpConstant.COUNTRY_CODE, message = "{validation.invalid.country.code}")
    private String countryCode;

    /**
     * 验证码
     */
    @Pattern(regexp = RegexpConstant.MOBILE_CODE, message = "{validation.verification.code.invalid}")
    private String smsCode;
}
