package com.xt.hsk.module.user.controller.app.user.vo;

import com.xt.hsk.framework.common.constants.RegexpConstant;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 用户短信登录/注册参数
 *
 * <AUTHOR>
 * @since 2025/05/30
 */
@Data
public class UserSmsLoginReqVO {

    /**
     * 手机号码
     */
    @Pattern(regexp = RegexpConstant.MOBILE, message = "{validation.mobile.invalid}")
    private String mobile;

    /**
     * 手机区号
     */
    @Pattern(regexp = RegexpConstant.COUNTRY_CODE, message = "{validation.invalid.country.code}")
    private String countryCode;

    /**
     * 验证码
     */
    @Pattern(regexp = RegexpConstant.MOBILE_CODE, message = "{validation.verification.code.invalid}")
    private String smsCode;


} 