package com.xt.hsk.module.user.service.loginlog;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.user.dal.dataobject.loginlog.UserLoginLogDO;
import com.xt.hsk.module.user.dal.mysql.loginlog.UserLoginLogMapper;
import com.xt.hsk.module.user.dto.AppUserLoginLogCreateReqDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 用户登录日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserLoginLogServiceImpl extends
    ServiceImpl<UserLoginLogMapper, UserLoginLogDO> implements
    UserLoginLogService {

    @Resource
    private UserLoginLogMapper userLoginLogMapper;

    @Override
    public void createLoginLog(AppUserLoginLogCreateReqDTO reqDTO) {
        UserLoginLogDO loginLog = BeanUtils.toBean(reqDTO, UserLoginLogDO.class);
        userLoginLogMapper.insert(loginLog);
    }
}