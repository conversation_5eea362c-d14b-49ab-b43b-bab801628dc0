package com.xt.hsk.module.user.controller.app.favorite;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.user.controller.app.favorite.vo.*;
import com.xt.hsk.module.user.manager.favorite.UserFavoriteAppManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

/**
 * APP端 - 用户收藏夹
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user/favorite")
@Validated
public class UserFavoriteAppController {

    @Resource
    private UserFavoriteAppManager userFavoriteAppManager;

    /**
     * 获取用户收藏的小游戏题目列表
     *
     * @param reqVO 查询条件
     * @return 结果
     */
    @PostMapping("/gameQuestionsList")
    public CommonResult<PageResult<FavoriteGameQuestionsAppPageRespVO>> gameQuestionsList(@Valid @RequestBody GameFavoriteAppPageReqVO reqVO) {
        return success(userFavoriteAppManager.favoriteGameList(reqVO));
    }

    /**
     * 收藏/取消收藏
     *
     * @param reqVO 请求参数
     * @return 操作结果
     */
    @PostMapping("/toggle")
    public CommonResult<Boolean> toggleFavorite(@RequestBody UserFavoriteToggleReqVO reqVO) {
        return success(userFavoriteAppManager.toggleFavorite(reqVO));
    }

    /**
     * 获取用户收藏的真题题目列表
     *
     * @param reqVO 查询条件
     * @return 结果
     */
    @PostMapping("/question-list")
    public CommonResult<List<FavoriteQuestionAppPageRespVO>> questionList(@RequestBody FavoriteQuestionAppPageReqVO reqVO) {
        return success(userFavoriteAppManager.favoriteQuestionList(reqVO));
    }

    /**
     * 获取用户收藏的单词列表
     *
     * @param reqVO 查询条件
     * @return 结果
     */
    @PostMapping("/word-book")
    public CommonResult<PageResult<FavoriteWordAppPageRespVO>> wordBookList(@RequestBody FavoriteWordAppPageReqVO reqVO) {
        return success(userFavoriteAppManager.wordBookList(reqVO));
    }
}
