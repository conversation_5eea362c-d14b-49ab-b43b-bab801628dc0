package com.xt.hsk.module.user.controller.app.favorite.vo;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 用户小游戏题目收藏分页列表响应
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Data
public class FavoriteGameQuestionsAppPageRespVO {

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 题干内容
     */
    private String questionContent;

    /**
     * 题目类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     */
    private Integer type;

    /**
     * 题目类型名称
     */
    private String typeName;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 难度等级 1 简单 2中等 3 困难
     */
    private Integer difficultyLevel;

    /**
     * 是否错题：1-是 0-否
     */
    private Integer isWrongQuestion;

    /**
     * 收藏时间
     */
    private LocalDateTime favoriteTime;

}
