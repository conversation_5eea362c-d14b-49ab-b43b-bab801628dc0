package com.xt.hsk.module.user.manager.changemobile;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.AUTH_REGISTER_CAPTCHA_CODE_ERROR;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.SMS_CODE_ERROR;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.SMS_CODE_NOT_FOUND;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_COUNTRY_CODE_ERROR;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_NOT_EXISTS;
import static com.xt.hsk.module.user.enums.ErrorCodeConstants.USER_PHONE_EXISTS;

import cn.dev33.satoken.stp.StpUtil;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import com.xt.hsk.framework.common.util.validation.ValidationUtils;
import com.xt.hsk.module.system.api.captcha.CaptchaApi;
import com.xt.hsk.module.system.api.sms.SmsCodeApi;
import com.xt.hsk.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import com.xt.hsk.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import com.xt.hsk.module.system.api.sms.dto.code.SmsCodeValidateReqDTO;
import com.xt.hsk.module.system.enums.sms.SmsSceneEnum;
import com.xt.hsk.module.user.controller.app.sms.vo.CaptchaVerificationReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.ChangeMobileReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.ChangeMobileStatusRespVO;
import com.xt.hsk.module.user.controller.app.user.vo.SendChangeMobileCodeReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.SendNewMobileCodeReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.VerifyOldMobileReqVO;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import com.xt.hsk.module.user.dto.ChangeMobileSession;
import com.xt.hsk.module.user.enums.ChangeMobileState;
import com.xt.hsk.module.user.service.changemobile.ChangeMobileSessionService;
import com.xt.hsk.module.user.service.user.UserService;
import com.xt.hsk.module.user.util.CountryCodeUtils;
import jakarta.annotation.Resource;
import jakarta.validation.Validator;
import java.time.LocalDateTime;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 因为更换手机号码是一个流程性的操作为了让代码好维护,所以专门创建一个Manager 一个完成的正向流程应该是 1. 发送旧手机号验证码 2. 验证旧手机号验证码 3. 发送新手机号验证码 4.
 * 验证新手机号验证码 5. 修改手机号 需要提供的接口有： 1. 发送旧手机号验证码 2. 验证旧手机号验证码 3. 发送新手机号验证码 5. 验证新手机号验证码并修改手机号
 *
 * <AUTHOR>
 * @since 2025/07/25
 */
@Service
@Slf4j
public class ChangeMobileManager {

    @Resource
    private ChangeMobileSessionService changeMobileSessionService;

    @Resource
    private UserService userService;

    @Resource
    private SmsCodeApi smsCodeApi;

    /**
     * 验证码的开关，默认为 true
     */
    @Value("${hsk.captcha.enable:true}")
    @Setter
    private Boolean captchaEnable;

    @Resource
    private Validator validator;

    @Resource
    private CaptchaApi captchaApi;

    private ResponseModel doValidateCaptcha(CaptchaVerificationReqVO reqVO) {
        // 如果验证码关闭，则不进行校验
        if (Boolean.FALSE.equals(captchaEnable)) {
            return ResponseModel.success();
        }
        ValidationUtils.validate(validator, reqVO, CaptchaVerificationReqVO.CodeEnableGroup.class);
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaVerification(reqVO.getCaptchaVerification());
        return captchaApi.verification(captchaVO);
    }


    /**
     * 发送旧手机号验证码
     */
    public void sendOldMobileCode(SendChangeMobileCodeReqVO reqVO) {

        // 校验图形验证码
        ResponseModel responseModel = doValidateCaptcha(reqVO);
        // 验证不通过
        if (!responseModel.isSuccess()) {
            throw exception(AUTH_REGISTER_CAPTCHA_CODE_ERROR, responseModel.getRepMsg());
        }

        Long userId = StpUtil.getLoginIdAsLong();

        // 1. 获取当前用户信息
        UserDO user = userService.getById(userId);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }

        String mobile = user.getMobile();
        String countryCode = user.getCountryCode();

        // 2. 创建修改手机号会话
        changeMobileSessionService.createSession(userId, mobile, countryCode);

        // 3. 发送短信验证码
        smsCodeApi.sendSmsCode(new SmsCodeSendReqDTO()
            .setMobile(mobile)
            .setCountryCode(countryCode)
            .setScene(SmsSceneEnum.MEMBER_UPDATE_MOBILE_CN.getScene())
            .setCreateIp(ServletUtils.getClientIP()));

        log.info("发送旧手机号验证码成功: userId={}, mobile={}:{}", userId, countryCode, mobile);
    }

    /**
     * 验证旧手机号
     */
    public boolean verifyOldMobile(VerifyOldMobileReqVO reqVO) {
        Long userId = StpUtil.getLoginIdAsLong();

        // 1. 获取会话状态
        ChangeMobileSession session = changeMobileSessionService.getSession(userId);
        if (session == null) {
            log.warn("更换手机号会话不存在: userId={}", userId);
            throw exception(SMS_CODE_NOT_FOUND);
        }

        // 2. 检查状态
        if (!ChangeMobileState.OLD_CODE_SENT.equals(session.getState())) {
            log.warn("更换手机号会话状态异常: userId={}, expected={}, actual={}",
                userId, ChangeMobileState.OLD_CODE_SENT, session.getState());
            throw exception(SMS_CODE_NOT_FOUND);
        }

        // 3. 验证验证码（只验证，不消耗）
        try {
            SmsCodeValidateReqDTO validateReq = new SmsCodeValidateReqDTO();
            validateReq.setMobile(session.getOldMobile());
            validateReq.setCountryCode(session.getOldCountryCode());
            validateReq.setCode(reqVO.getCode());
            validateReq.setScene(SmsSceneEnum.MEMBER_UPDATE_MOBILE_CN.getScene());

            smsCodeApi.validateSmsCode(validateReq);

        } catch (ServiceException e) {
            log.warn("旧手机号验证码验证失败: userId={}, error={}", userId, e.getMessage());
            // 记录失败原因到数据库
            changeMobileSessionService.markSessionFailed(userId, session.getSessionId(),
                "旧手机号验证码验证失败: " + e.getMessage());
            // 如果是业务异常 直接丢给全局处理器执行
            throw e;
        } catch (Exception e) {
            log.error("旧手机号验证码验证失败: userId={}, error={}", userId, e.getMessage(), e);
            // 记录失败原因到数据库
            changeMobileSessionService.markSessionFailed(userId, session.getSessionId(),
                "旧手机号验证码验证系统异常: " + e.getMessage());
            return false;
        }

        // 4. 更新会话状态
        session.setState(ChangeMobileState.OLD_CODE_VERIFIED);
        session.setOldMobileVerified(true);
        session.setOldMobileVerifiedTime(LocalDateTime.now());
        changeMobileSessionService.updateSession(session);

        log.info("旧手机号验证成功: userId={}, mobile={}:{}", userId, session.getOldCountryCode(),
            session.getOldMobile());

        return true;
    }

    /**
     * 发送新手机号验证码
     */
    public boolean sendNewMobileCode(SendNewMobileCodeReqVO reqVO) {
        Long userId = StpUtil.getLoginIdAsLong();

        // 1. 获取会话状态
        ChangeMobileSession session = changeMobileSessionService.getSession(userId);
        if (session == null) {
            // 抛的是验证码不存在 不想抛具体原因 如果触发了这个异常 可能的原因
            // 1.直接调我第三步的接口
            log.warn("更换手机号会话不存在: userId={}", userId);
            return false;
        }

        // 2. 检查前置状态
        if (!ChangeMobileState.OLD_CODE_VERIFIED.equals(session.getState())) {
            // 不按照步骤来 第二步验证有问题 都让他g 触发这个异常 肯定是自己调接口
            log.warn("更换手机号会话状态异常: userId={}, expected={}, actual={}",
                userId, ChangeMobileState.OLD_CODE_VERIFIED, session.getState());
            changeMobileSessionService.markSessionFailed(userId, session.getSessionId(),
                "会话状态异常，期望: " + ChangeMobileState.OLD_CODE_VERIFIED + "，实际: " + session.getState());
            return false;
        }

        try {
            // 3. 校验新手机号格式
            boolean checked = CountryCodeUtils.checkCountryCode(reqVO.getNewCountryCode());
            if (!checked) {
                changeMobileSessionService.markSessionFailed(userId, session.getSessionId(),
                    "新手机号国家代码格式错误: " + reqVO.getNewCountryCode());
                throw exception(USER_COUNTRY_CODE_ERROR);
            }

            // 4. 检查新手机号是否已存在
            boolean exists = userService.existsByPhone(reqVO.getNewCountryCode(), reqVO.getNewMobile());
            if (exists) {
                changeMobileSessionService.markSessionFailed(userId, session.getSessionId(),
                    "新手机号已存在: " + reqVO.getNewCountryCode() + ":" + reqVO.getNewMobile());
                throw exception(USER_PHONE_EXISTS);
            }

            // 5. 发送验证码到新手机号
            smsCodeApi.sendSmsCode(new SmsCodeSendReqDTO()
                .setMobile(reqVO.getNewMobile())
                .setCountryCode(reqVO.getNewCountryCode())
                .setScene(SmsSceneEnum.MEMBER_UPDATE_MOBILE_CN.getScene())
                .setCreateIp(ServletUtils.getClientIP()));

            // 6. 更新会话状态
            session.setState(ChangeMobileState.NEW_CODE_SENT);
            session.setNewMobile(reqVO.getNewMobile());
            session.setNewCountryCode(reqVO.getNewCountryCode());
            session.setNewMobileCodeSent(true);
            session.setNewMobileCodeSentTime(LocalDateTime.now());
            changeMobileSessionService.updateSession(session);

            log.info("新手机号验证码发送成功: userId={}, newMobile={}:{}",
                userId, reqVO.getNewCountryCode(), reqVO.getNewMobile());

            return true;

        } catch (ServiceException e) {
            log.error("发送新手机号验证码失败: userId={}, error={}", userId, e.getMessage());
            changeMobileSessionService.markSessionFailed(userId, session.getSessionId(),
                "发送新手机号验证码失败: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("发送新手机号验证码系统异常: userId={}, error={}", userId, e.getMessage(), e);
            changeMobileSessionService.markSessionFailed(userId, session.getSessionId(),
                "发送新手机号验证码系统异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 执行手机号修改
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean changeMobile(ChangeMobileReqVO reqVO) {
        Long userId = StpUtil.getLoginIdAsLong();

        // 1. 获取会话状态
        ChangeMobileSession session = changeMobileSessionService.getSession(userId);
        if (session == null) {
            log.warn("更换手机号会话不存在: userId={}", userId);
            throw exception(SMS_CODE_ERROR);
        }

        // 2. 检查状态
        if (!ChangeMobileState.NEW_CODE_SENT.equals(session.getState())) {
            log.warn("更换手机号会话状态异常: userId={}, expected={}, actual={}",
                userId, ChangeMobileState.NEW_CODE_SENT, session.getState());
            changeMobileSessionService.markSessionFailed(userId, session.getSessionId(),
                "会话状态异常，期望: " + ChangeMobileState.NEW_CODE_SENT + "，实际: " + session.getState());
            throw exception(SMS_CODE_ERROR);
        }

        // 3. 验证请求参数与会话中的手机号一致性（重要安全检查）
        if (!reqVO.getNewMobile().equals(session.getNewMobile()) ||
            !reqVO.getNewCountryCode().equals(session.getNewCountryCode())) {
            log.error("手机号参数不一致: userId={}, reqMobile={}:{}, sessionMobile={}:{}",
                userId, reqVO.getNewCountryCode(), reqVO.getNewMobile(),
                session.getNewCountryCode(), session.getNewMobile());
            changeMobileSessionService.markSessionFailed(userId, session.getSessionId(),
                String.format("手机号参数不一致，请求: %s:%s，会话: %s:%s",
                    reqVO.getNewCountryCode(), reqVO.getNewMobile(),
                    session.getNewCountryCode(), session.getNewMobile()));
            throw exception(SMS_CODE_NOT_FOUND);
        }

        // 4. 验证新手机号验证码并消耗
        try {
            SmsCodeUseReqDTO useReq = new SmsCodeUseReqDTO();
            useReq.setMobile(session.getNewMobile());
            useReq.setCountryCode(session.getNewCountryCode());
            useReq.setCode(reqVO.getNewCode());
            useReq.setScene(SmsSceneEnum.MEMBER_UPDATE_MOBILE_CN.getScene());
            useReq.setUsedIp(ServletUtils.getClientIP());

            smsCodeApi.useSmsCode(useReq);

        } catch (ServiceException e) {
            log.warn("新手机号验证码验证失败: userId={}, error={}", userId, e.getMessage());
            changeMobileSessionService.markSessionFailed(userId, session.getSessionId(),
                "新手机号验证码验证失败: " + e.getMessage());
            // 如果是业务异常 直接丢给全局处理器执行
            throw e;
        } catch (Exception e) {
            log.error("新手机号验证码验证失败: userId={}, error={}", userId, e.getMessage(), e);
            changeMobileSessionService.markSessionFailed(userId, session.getSessionId(),
                "新手机号验证码验证系统异常: " + e.getMessage());
            throw e;
        }

        // 5. 旧验证码已经在步骤2验证过了，这里不需要再次处理
        log.info("旧手机号验证码已在步骤2验证过: userId={}, oldMobile={}:{}",
            userId, session.getOldCountryCode(), session.getOldMobile());

        try {
            // 6. 更新用户手机号（使用会话中验证过的手机号，而不是请求参数）
            userService.updateUserMobile(userId, session.getNewCountryCode(), session.getNewMobile());

            // 7. 标记会话完成并清理Redis会话
            changeMobileSessionService.markSessionCompleted(userId, session.getSessionId());
            changeMobileSessionService.deleteSession(userId);

            log.info("用户修改手机号成功: userId={}, oldMobile={}:{}, newMobile={}:{}",
                userId, session.getOldCountryCode(), session.getOldMobile(),
                session.getNewCountryCode(), session.getNewMobile());

            return true;

        } catch (Exception e) {
            log.error("更新用户手机号失败: userId={}, error={}", userId, e.getMessage(), e);
            changeMobileSessionService.markSessionFailed(userId, session.getSessionId(),
                "更新用户手机号失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 查询修改手机号状态
     */
    public ChangeMobileStatusRespVO getChangeMobileStatus() {
        Long userId = StpUtil.getLoginIdAsLong();
        ChangeMobileSession session = changeMobileSessionService.getSession(userId);

        if (session == null) {
            return null;
        }

        return ChangeMobileStatusRespVO.builder()
            .sessionId(session.getSessionId())
            .state(session.getState())
            .oldMobileVerified(session.getOldMobileVerified())
            .newMobileCodeSent(session.getNewMobileCodeSent())
            .createTime(session.getCreateTime())
            .updateTime(session.getUpdateTime())
            .build();
    }


}
