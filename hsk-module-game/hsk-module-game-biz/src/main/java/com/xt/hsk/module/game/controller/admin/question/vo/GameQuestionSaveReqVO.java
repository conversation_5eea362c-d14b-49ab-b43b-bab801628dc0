package com.xt.hsk.module.game.controller.admin.question.vo;

import com.xt.hsk.module.game.enums.question.GameQuestionTypeEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 题库 保存 req vo
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
public class GameQuestionSaveReqVO {

    /**
     * 题库ID
     */
    private Long id;

    /**
     * HSK等级
     */
    @NotNull(message = "HSK等级不能为空")
    private Integer hskLevel;

    /**
     * 题目类型
     *
     * @see GameQuestionTypeEnum
     */
    @NotNull(message = "题目类型不能为空")
    private Integer type;

    /**
     * 题干内容
     */
    @NotEmpty(message = "题干内容不能为空")
    private String questionContent;

    /**
     * 拆分后的题干
     */
    private String questionSplit;

    /**
     * 题干的拼音
     */
    private String pinyin;

    /**
     * 音频链接
     */
    private String audioUrl;

    /**
     * 考察点
     */
    private String knowledgePoint;

    /**
     * 考察点拼音
     */
    private String knowledgePointPinyin;

    /**
     * 参考答案
     */
    private String referenceAnswer;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 最新版本
     */
    private Integer latestVersion;

    /**
     * 题目ID列表
     */
    private List<Long> ids;

    /**
     * 参考答案列表
     */
    private List<QuestionReferenceAnswerSaveReqVO> referenceAnswerList;

    /**
     * 内容
     */
    private String content;

}