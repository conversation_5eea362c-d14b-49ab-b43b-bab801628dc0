package com.xt.hsk.module.game.controller.app.specialexercise.vo;

import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionAppRespVO;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 专项练习组 app RESP VO
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
public class SpecialExerciseAppRespVO {

    /**
     * 专项练习ID
     */
    private Long id;

    /**
     * 专项练习id
     */
    private Long specialExerciseId;

    /**
     * 专项练习名称
     */
    private String name;

    /**
     * 练习类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 难度等级 1 简单 2中等 3 困难
     */
    private Integer difficultyLevel;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 练习题版本库的最新版本
     */
    private Integer relatedVersion;

    /**
     * 轮次列表（单词连连看）
     */
    private List<ExerciseRoundAppRespVO> roundList;

    /**
     * 练习组列表
     */
    private List<ExerciseQuestionAppRespVO> exerciseQuestionList;

    /**
     * 题目列表
     */
    private List<String> questionContentList;

    /**
     * 考察点列表
     */
    private List<String> knowledgePointList;
    /**
     * 总轮次
     */
    private Integer totalRound;
    /**
     * 已完成的轮次
     */
    private Integer completedRound;

}