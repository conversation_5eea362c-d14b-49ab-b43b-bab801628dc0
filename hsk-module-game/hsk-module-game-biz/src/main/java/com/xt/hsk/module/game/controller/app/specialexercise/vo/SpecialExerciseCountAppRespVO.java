package com.xt.hsk.module.game.controller.app.specialexercise.vo;

import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.Data;

/**
 * 专项练习组类型题数 app RESP VO
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
public class SpecialExerciseCountAppRespVO {

    /**
     * 专项练习类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer specialPracticeType;

    /**
     * 已练题数
     */
    private Integer completedNumber;

    /**
     * 总题数
     */
    private Integer totalNumber;

    public SpecialExerciseCountAppRespVO() {
        this.specialPracticeType = null;
        this.completedNumber = 0;
        this.totalNumber = 0;
    }

}