package com.xt.hsk.module.game.controller.admin.question.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.module.game.enums.question.GameQuestionTypeEnum;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 题库 resp vo
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
public class GameQuestionRespVO implements VO {

    /**
     * 题库ID
     */
    private Long id;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 题目类型
     *
     * @see GameQuestionTypeEnum
     */
    private Integer type;

    /**
     * 题干内容
     */
    private String questionContent;

    /**
     * 拆分后的题干
     */
    private String questionSplit;

    /**
     * 题干的拼音
     */
    private String pinyin;

    /**
     * 音频链接
     */
    private String audioUrl;

    /**
     * 考察点
     */
    private String knowledgePoint;

    /**
     * 考察点拼音
     */
    private String knowledgePointPinyin;

    /**
     * 参考答案
     */
    private String referenceAnswer;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 最新版本
     */
    private Integer latestVersion;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最近更新人ID
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "updaterName")
    private String updater;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 引用该题目的专项练习组列表
     */
    private List<GameQuestionExerciseReferenceVO> exerciseReferenceList;

    /**
     * 被引用次数（题目被专项练习使用的次数）
     */
    private Integer referenceCount;

}