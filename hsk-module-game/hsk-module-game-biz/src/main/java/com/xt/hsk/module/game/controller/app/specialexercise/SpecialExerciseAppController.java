package com.xt.hsk.module.game.controller.app.specialexercise;

import cn.dev33.satoken.annotation.SaIgnore;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.GameReportAppRespVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseAppPageReqVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseAppPageRespVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseAppReqVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseAppRespVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseCountAppRespVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseStartAppRespVO;
import com.xt.hsk.module.game.manager.specialexercise.app.SpecialExerciseAppManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 游戏-专项练习-练习组 app 控制器
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/game/special-exercise")
public class SpecialExerciseAppController {

    @Resource
    private SpecialExerciseAppManager specialExerciseAppManager;

    /**
     * 获取专项练习列表
     */
    @SaIgnore
    @PostMapping("/v1/page")
    public CommonResult<PageResult<SpecialExerciseAppPageRespVO>> getSpecialExercisePage(@RequestBody SpecialExerciseAppPageReqVO reqVO) {
        return CommonResult.success(specialExerciseAppManager.getSpecialExercisePage(reqVO));
    }

    /**
     * 获得专项练习id获取专项练习题目
     */
    @PostMapping("/v1/get")
    public CommonResult<SpecialExerciseAppRespVO> getSpecialExercise(@Validated @RequestBody SpecialExerciseAppReqVO reqVO) {
        return CommonResult.success(specialExerciseAppManager.getSpecialExercise(reqVO));
    }

    /**
     * 获取专项练习练习题数
     */
    @SaIgnore
    @PostMapping("/v1/practice-count")
    public CommonResult<SpecialExerciseCountAppRespVO> getSpecialExercisePracticeCount(@Validated @RequestBody SpecialExerciseAppReqVO reqVO) {
        return CommonResult.success(specialExerciseAppManager.getSpecialExercisePracticeCount(reqVO));
    }

    /**
     * 开始练习
     */
    @PostMapping("/v1/start")
    public CommonResult<SpecialExerciseStartAppRespVO> startSpecialExercise(@Validated @RequestBody SpecialExerciseAppReqVO reqVO) {
        return CommonResult.success(specialExerciseAppManager.startSpecialExercise(reqVO));
    }

    /**
     * 获取专项练习报告
     */
    @PostMapping("/v1/report")
    public CommonResult<GameReportAppRespVO> report(@Validated @RequestBody SpecialExerciseAppReqVO reqVO) {
        return CommonResult.success(specialExerciseAppManager.report(reqVO));
    }
}