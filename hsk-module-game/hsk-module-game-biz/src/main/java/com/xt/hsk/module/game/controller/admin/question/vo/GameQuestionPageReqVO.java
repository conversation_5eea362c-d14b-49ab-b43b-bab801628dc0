package com.xt.hsk.module.game.controller.admin.question.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.module.game.enums.question.GameQuestionTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 题库 分页 req vo
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GameQuestionPageReqVO extends PageParam {

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 题目类型
     *
     * @see GameQuestionTypeEnum
     */
    private Integer type;

    /**
     * 题干内容
     */
    private String questionContent;

    /**
     * 拆分后的题干
     */
    private String questionSplit;

    /**
     * 题干的拼音
     */
    private String pinyin;

    /**
     * 音频链接
     */
    private String audioUrl;

    /**
     * 考察点
     */
    private String knowledgePoint;

    /**
     * 参考答案
     */
    private String referenceAnswer;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 最新版本
     */
    private Integer latestVersion;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}