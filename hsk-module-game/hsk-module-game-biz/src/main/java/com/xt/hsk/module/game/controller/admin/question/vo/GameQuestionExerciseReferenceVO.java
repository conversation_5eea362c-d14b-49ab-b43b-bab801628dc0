package com.xt.hsk.module.game.controller.admin.question.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 题目被专项练习引用的信息 VO
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
public class GameQuestionExerciseReferenceVO implements VO {

    private Long id;

    /**
     * 专项练习ID
     */
    private Long specialExerciseId;

    /**
     * 专项练习名称-中文
     */
    private String nameCn;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 最近更新人ID
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "updaterName")
    private String updater;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}