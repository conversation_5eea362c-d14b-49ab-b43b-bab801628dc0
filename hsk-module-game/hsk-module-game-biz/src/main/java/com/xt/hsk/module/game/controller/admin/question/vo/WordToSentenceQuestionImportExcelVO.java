package com.xt.hsk.module.game.controller.admin.question.vo;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 连词成句题目 Excel 导入 VO
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class WordToSentenceQuestionImportExcelVO {

    @ExcelProperty("*题干")
    private String questionContent;

    @ExcelProperty("*拆分后的题干")
    private String questionSplit;

    @ExcelProperty("*HSK等级")
    private Integer hskLevel;

    @ExcelProperty("考察点")
    private String knowledgePoint;

    @ExcelProperty("*参考答案1")
    private String referenceAnswerCn1;

    @ExcelProperty("参考答案1越南语")
    private String referenceAnswerOt1;

    @ExcelProperty("参考答案1英语")
    private String referenceAnswerEn1;

    @ExcelProperty("参考答案2")
    private String referenceAnswerCn2;

    @ExcelProperty("参考答案2越南语")
    private String referenceAnswerOt2;

    @ExcelProperty("参考答案2英语")
    private String referenceAnswerEn2;

    @ExcelProperty("参考答案3")
    private String referenceAnswerCn3;

    @ExcelProperty("参考答案3越南语")
    private String referenceAnswerOt3;

    @ExcelProperty("参考答案3英语")
    private String referenceAnswerEn3;

    @ExcelProperty("参考答案4")
    private String referenceAnswerCn4;

    @ExcelProperty("参考答案4越南语")
    private String referenceAnswerOt4;

    @ExcelProperty("参考答案4英语")
    private String referenceAnswerEn4;

} 