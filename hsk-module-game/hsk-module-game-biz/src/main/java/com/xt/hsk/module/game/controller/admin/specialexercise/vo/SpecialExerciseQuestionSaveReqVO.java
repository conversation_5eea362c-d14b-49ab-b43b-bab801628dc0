package com.xt.hsk.module.game.controller.admin.specialexercise.vo;

import lombok.Data;

/**
 * 游戏-专项练习-题目 save req vo
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Data
public class SpecialExerciseQuestionSaveReqVO {

    /**
     * 练习组题目ID
     */
    private Long exerciseQuestionId;
    /**
     * 题目ID
     */
    private Long questionId;
    /**
     * 翻译
     */
    private String translationOt;
    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 展示轮次（仅word_matching）
     */
    private Integer showRound;
    /**
     * 轮次数（仅word_matching）
     */
    private Integer roundNumber;
    /**
     * 单词
     */
    private String word;

}