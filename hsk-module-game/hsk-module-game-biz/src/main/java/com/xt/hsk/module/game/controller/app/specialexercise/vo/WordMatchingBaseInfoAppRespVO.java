package com.xt.hsk.module.game.controller.app.specialexercise.vo;

import lombok.Data;

/**
 * 单词连连看题目基本信息 app resp vo
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
public class WordMatchingBaseInfoAppRespVO {

    /**
     * 专项练习ID
     */
    private Long specialExerciseId;

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 翻译
     */
    private String translationOt;

    /**
     * 轮次数（仅word_matching）
     */
    private Integer roundNumber;

    /**
     * 题干内容
     */
    private String questionContent;

    /**
     * 题干的拼音
     */
    private String pinyin;

    /**
     * 练习组题目ID
     */
    private Long exerciseQuestionId;

    /**
     * 练习组题目版本库ID
     */
    private Long exerciseQuestionVersionId;

}