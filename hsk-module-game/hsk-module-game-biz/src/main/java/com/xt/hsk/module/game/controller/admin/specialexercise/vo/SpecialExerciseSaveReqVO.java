package com.xt.hsk.module.game.controller.admin.specialexercise.vo;

import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 游戏-专项练习-练习组 save req vo
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Data
public class SpecialExerciseSaveReqVO {

    /**
     * 专项练习ID
     */
    private Long id;

    /**
     * 专项练习名称-中文
     */
    @NotBlank(message = "练习组名称不能为空")
    private String nameCn;

    /**
     * 专项练习名称-英文
     */
    private String nameEn;

    /**
     * 专项练习名称-其他
     */
    private String nameOt;

    /**
     * 练习类型
     *
     * @see SpecialExerciseTypeEnum
     */
    @NotNull(message = "练习类型不能为空")
    private Integer type;

    /**
     * HSK等级
     */
    @NotNull(message = "HSK等级不能为空")
    private Integer hskLevel;

    /**
     * 难度等级 1 简单 2中等 3 困难
     */
    private Integer difficultyLevel;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 排序序号
     */
    private Integer sort;
    /**
     * 练习题版本库的最新版本
     */
    private Integer relatedVersion;

    /**
     * 轮次列表（单词连连看）
     */
    private List<SpecialExerciseRoundSaveReqVO> roundList;

    /**
     * 练习题列表（笔画书写、连词成句、卡拉OK）
     */
    private List<SpecialExerciseQuestionSaveReqVO> exerciseQuestionList;

    /**
     * 专项练习ID列表
     */
    private List<Long> ids;

}