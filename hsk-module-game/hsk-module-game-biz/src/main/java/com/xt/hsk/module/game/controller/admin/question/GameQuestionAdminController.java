package com.xt.hsk.module.game.controller.admin.question;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.framework.common.pojo.CommonResult.success;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.GAME_QUESTION_TYPE_NOT_EXISTS;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.ImportResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionDeleteCheckRespVO;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionExportReqVO;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionHideCheckRespVO;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionPageReqVO;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionRespVO;
import com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionSaveReqVO;
import com.xt.hsk.module.game.enums.question.GameQuestionTypeEnum;
import com.xt.hsk.module.game.manager.question.admin.GameQuestionAdminManager;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.FileCopyUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 游戏-题库 后台管理 控制器
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/game/question")
public class GameQuestionAdminController {

    @Resource
    private GameQuestionAdminManager gameQuestionAdminManager;

    @Resource
    private ExportTaskApi exportTaskApi;

    /**
     * 创建题目
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('game:question:create')")
    @LogRecord(type = LogRecordType.GAME_QUESTION_TYPE, subType = "创建游戏题目", bizNo = "{{#questionId}}", success = "创建游戏题目：【{{#question.questionContent}}】，类型：{{#questionTypeName}}，HSK等级：{{#question.hskLevel}}")
    public CommonResult<Long> createQuestion(@Valid @RequestBody GameQuestionSaveReqVO createReqVO) {
        return success(gameQuestionAdminManager.createQuestion(createReqVO));
    }

    /**
     * 更新题目
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('game:question:update')")
    @LogRecord(type = LogRecordType.GAME_QUESTION_TYPE, subType = "修改游戏题目", bizNo = "{{#updateReqVO.id}}", success = "修改游戏题目：【{{#question.questionContent}}】，类型：{{#questionTypeName}}，HSK等级：{{#question.hskLevel}}")
    public CommonResult<Boolean> updateQuestion(@Valid @RequestBody GameQuestionSaveReqVO updateReqVO) {
        gameQuestionAdminManager.updateQuestion(updateReqVO);
        return success(true);
    }

    /**
     * 删除题目
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('game:question:delete')")
    @LogRecord(type = LogRecordType.GAME_QUESTION_TYPE, subType = "删除游戏题目", bizNo = "{{#id}}", success = "删除游戏题目：【{{#question.questionContent}}】，类型：{{#questionTypeName}}，HSK等级：{{#question.hskLevel}}")
    public CommonResult<Boolean> deleteQuestion(@RequestParam("id") Long id) {
        gameQuestionAdminManager.deleteQuestion(id);
        return success(true);
    }

    /**
     * 根据id获得题目
     */
    @PostMapping("/get")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('game:question:query')")
    public CommonResult<GameQuestionRespVO> getQuestion(@RequestParam("id") Long id) {
        return success(gameQuestionAdminManager.getQuestion(id));
    }

    /**
     * 分页获得题目
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('game:question:query')")
    public CommonResult<PageResult<GameQuestionRespVO>> getQuestionPage(@Valid @RequestBody GameQuestionPageReqVO pageReqVO) {
        return success(gameQuestionAdminManager.getQuestionPage(pageReqVO));
    }

    /**
     * 批量删除题目
     */
    @DeleteMapping("/batch-delete")
    @PreAuthorize("@ss.hasPermission('game:question:delete')")
    @LogRecord(type = LogRecordType.GAME_QUESTION_TYPE, subType = "批量删除游戏题目", bizNo = "{{#batchBizNo}}", success = "批量删除游戏题目：共删除{{#deleteCount}}个题目，ID列表：{{#updateReqVO.ids}}")
    public CommonResult<Boolean> deleteByIds(@RequestBody GameQuestionSaveReqVO updateReqVO) {
        gameQuestionAdminManager.deleteByIds(updateReqVO.getIds());
        return success(true);
    }

    /**
     * 批量显示/隐藏题目
     */
    @PutMapping("/batch-status")
    @PreAuthorize("@ss.hasPermission('game:question:update')")
    @LogRecord(type = LogRecordType.GAME_QUESTION_TYPE, subType = "批量更新游戏题目状态", bizNo = "{{#batchBizNo}}", success = "批量{{#statusText}}游戏题目：共{{#statusText}}{{#updateCount}}个题目，状态改为{{#statusName}}")
    public CommonResult<Boolean> batchUpdateStatus(@RequestBody GameQuestionSaveReqVO updateReqVO) {
        gameQuestionAdminManager.batchUpdateStatus(updateReqVO.getIds(), updateReqVO.getIsShow());
        return success(true);
    }

    /**
     * 导出游戏题目数据
     */
    @LogRecord(type = LogRecordType.GAME_QUESTION_TYPE, bizNo = "{{#taskId}}", success = "创建游戏题目异步导出任务")
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermission('game:question:export')")
    public CommonResult<Long> exportGameQuestion(@RequestBody @Valid GameQuestionExportReqVO exportReqVO) {
        try {

            ExportTaskTypeEnum taskType;
            if (GameQuestionTypeEnum.WORD_TO_SENTENCE.getCode().equals(exportReqVO.getType())) {
                taskType = ExportTaskTypeEnum.WORD_TO_SENTENCE_QUESTION;
            } else if (GameQuestionTypeEnum.KARAOKE.getCode().equals(exportReqVO.getType())) {
                taskType = ExportTaskTypeEnum.KARAOKE_QUESTION;
            } else {
                throw exception(GAME_QUESTION_TYPE_NOT_EXISTS);
            }


            ObjectMapper objectMapper = new ObjectMapper();
            // 将导出请求参数和任务名称一起传递
            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(exportReqVO.getTaskName());
            taskParams.setQueryParams(exportReqVO);

            String params = objectMapper.writeValueAsString(taskParams);
            Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
                    taskType, params);
            return success(taskId);
        } catch (Exception e) {
            log.error("创建游戏题目导出任务失败", e);
            return CommonResult.error(500, "创建导出任务失败：" + e.getMessage());
        }
    }

    /**
     * 导入题目
     *
     * @param file 导入文件
     * @param type 类型
     * @return 导入结果
     */
    @PostMapping("/import")
    @PreAuthorize("@ss.hasPermission('game:question:import')")
    @LogRecord(type = LogRecordType.GAME_QUESTION_TYPE, subType = "导入游戏题目", bizNo = "{{#type}}", success = "导入游戏题目：成功导入{{#importResult.validCount}}个题目，失败{{#importResult.invalidCount}}个，题目类型：{{#typeName}}")
    public CommonResult<ImportResult> importQuestion(@RequestParam("file") MultipartFile file, @RequestParam("type") Integer type) {
        return success(gameQuestionAdminManager.importQuestion(file, type));
    }

    /**
     * 下载导入模板
     *
     * @param response 响应
     */
    @PostMapping("/import/template")
    @PreAuthorize("@ss.hasPermission('game:question:import')")
    public void downloadImportTemplate(HttpServletResponse response, @RequestParam("type") Integer type) throws IOException {

        String fileNameStr;

        if (GameQuestionTypeEnum.WORD_TO_SENTENCE.getCode().equals(type)) {
            fileNameStr = "连词成句题目上传模板";
        } else if (GameQuestionTypeEnum.KARAOKE.getCode().equals(type)) {
            fileNameStr = "卡拉OK题目上传模板";
        } else {
            throw exception(GAME_QUESTION_TYPE_NOT_EXISTS);
        }

        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(fileNameStr, StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        // 获取模板文件路径
        ClassPathResource resource = new ClassPathResource("excelTemplate/" + fileNameStr + ".xlsx");
        InputStream inputStream = resource.getInputStream();

        // 写入响应流
        FileCopyUtils.copy(inputStream, response.getOutputStream());
    }

    /**
     * 检查题目是否可以删除
     */
    @PostMapping("/check-delete")
    @PreAuthorize("@ss.hasPermission('game:question:query')")
    public CommonResult<GameQuestionDeleteCheckRespVO> checkQuestionCanDelete(@RequestBody GameQuestionSaveReqVO updateReqVO) {
        return success(gameQuestionAdminManager.checkQuestionCanDelete(updateReqVO.getIds()));
    }

    /**
     * 检查题目是否可以隐藏
     */
    @PostMapping("/check-hide")
    @PreAuthorize("@ss.hasPermission('game:question:query')")
    public CommonResult<GameQuestionHideCheckRespVO> checkQuestionCanHide(@RequestBody GameQuestionSaveReqVO updateReqVO) {
        return success(gameQuestionAdminManager.checkQuestionCanHide(updateReqVO.getIds()));
    }

    /**
     * 生成拼音
     */
    @PostMapping("/generate-pinyin")
    public CommonResult<GameQuestionRespVO> generatePinyin(@RequestBody GameQuestionSaveReqVO reqVO) {
        return success(gameQuestionAdminManager.generatePinyin(reqVO.getContent(), reqVO.getType()));
    }

}