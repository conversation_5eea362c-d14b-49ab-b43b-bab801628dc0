package com.xt.hsk.module.game.controller.app.record.vo;

import com.xt.hsk.module.game.enums.record.GameAnswerStatusEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.Data;

/**
 * 游戏作答记录 app req vo
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
public class GameKaraokeAnswerRecordAppRespVO {

    /**
     * 练习总记录id
     */
    private Long recordSummaryId;

    /**
     * 专项练习的id
     */
    private Long specialExerciseId;

    /**
     * 类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;

    /**
     * 第三方返回结果 json
     */
    private String result;

    /**
     * 用户作答音频
     */
    private String userAudio;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 答题状态 0-未答 1-已答 2-失败
     *
     * @see GameAnswerStatusEnum
     */
    private Integer answerStatus;
}