package com.xt.hsk.module.game.controller.app.record.vo;

import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 游戏作答记录 app req vo
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
public class GameAnswerRecordAppRespVO {

    /**
     * 练习总记录id
     */
    private Long recordSummaryId;

    /**
     * 专项练习的id
     */
    private Long specialExerciseId;

    /**
     * 类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;

    /**
     * 答题开始时间
     */
    private LocalDateTime answerStartTime;

    /**
     * 答题结束时间
     */
    private LocalDateTime answerEndTime;
    /**
     * 总轮次
     */
    private Integer totalRound;

    /**
     * 已完成的轮次
     */
    private Integer currentRound;

    /**
     * 答题详情列表
     */
    private List<GameAnswerRecordDetailsAppReqVO> answerDetailsList;


}