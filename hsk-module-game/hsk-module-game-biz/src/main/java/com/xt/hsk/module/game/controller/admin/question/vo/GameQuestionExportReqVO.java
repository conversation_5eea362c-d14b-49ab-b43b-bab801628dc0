package com.xt.hsk.module.game.controller.admin.question.vo;

import com.xt.hsk.module.game.enums.question.GameQuestionTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 游戏题目导出请求参数
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GameQuestionExportReqVO extends GameQuestionPageReqVO {

    /**
     * 导出任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    /**
     * 题目类型
     *
     * @see GameQuestionTypeEnum
     */
    @NotNull(message = "题目类型不能为空")
    private Integer type;

} 