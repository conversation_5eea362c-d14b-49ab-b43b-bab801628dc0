package com.xt.hsk.module.game.controller.admin.exercisequestion.vo;

import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 练习组题目版本库 save req vo
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
public class ExerciseQuestionVersionSaveReqVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 练习组题目ID
     */
    private Long exerciseQuestionId;

    /**
     * 专项练习ID
     */
    @NotNull(message = "专项练习ID不能为空")
    private Long specialExerciseId;

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 翻译
     */
    @NotEmpty(message = "翻译不能为空")
    private String translationOt;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空")
    private Integer sort;

    /**
     * 展示轮次（仅word_matching）
     */
    private Integer showRound;

    /**
     * 轮次数（仅word_matching）
     */
    private Integer roundNumber;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 类型
     *
     * @see SpecialExerciseTypeEnum
     */
    @NotEmpty(message = "类型不能为空")
    private String type;

}