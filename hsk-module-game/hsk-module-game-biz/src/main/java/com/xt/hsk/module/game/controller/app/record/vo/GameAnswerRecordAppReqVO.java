package com.xt.hsk.module.game.controller.app.record.vo;

import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 游戏作答记录 app req vo
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
public class GameAnswerRecordAppReqVO {

    /**
     * 练习总记录id
     */
    private Long recordSummaryId;

    /**
     * 专项练习的id
     */
    private Long specialExerciseId;

    /**
     * 类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;

    /**
     * 答题开始时间
     */
    private LocalDateTime answerStartTime;

    /**
     * 答题结束时间
     */
    private LocalDateTime answerEndTime;

    /**
     * 是否完成 0否 1是
     */
    private Integer isCompleted;

    /**
     * 答题详情列表
     */
    private List<GameAnswerRecordDetailsAppReqVO> answerDetailsList;

    /**
     * 互动课单元ID（可选，当来源是互动课时传入）
     */
    private Long interactiveCourseUnitId;

    /**
     * 当前回合
     */
    private Integer currentRound;


}