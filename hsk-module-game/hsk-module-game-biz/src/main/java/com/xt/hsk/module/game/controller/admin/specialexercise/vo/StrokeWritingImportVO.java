package com.xt.hsk.module.game.controller.admin.specialexercise.vo;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 笔画书写 导入 VO
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StrokeWritingImportVO {

    private String nameCn;

    private String nameEn;

    private String nameOt;

    private Integer hskLevel;

    private String difficultyLevel;

    private List<ExerciseQuestionImportVO> questionImportList;

    /**
     * 单词连连看
     *
     * <AUTHOR>
     * @since 2025-06-25
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExerciseQuestionImportVO {

        private Integer sort;

        private String questionContent;

        private String translationOt;

        /**
         * 行号
         */
        private Integer rowNum;
        /**
         * 题目ID
         */
        private Long questionId;
    }


    /**
     * 行号
     */
    private Integer rowNum;

} 