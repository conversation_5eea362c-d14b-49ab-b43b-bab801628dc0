package com.xt.hsk.module.game.controller.admin.specialexercise.vo;

import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 专项练习导出请求参数
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpecialExerciseExportReqVO extends SpecialExercisePageReqVO {

    /**
     * 导出任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    /**
     * 练习类型
     *
     * @see SpecialExerciseTypeEnum
     */
    @NotNull(message = "练习类型不能为空")
    private Integer type;

} 