package com.xt.hsk.module.game.controller.admin.specialexercise.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 游戏-专项练习-练习组 分页 req vo
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpecialExercisePageReqVO extends PageParam {

    /**
     * 专项练习名称-中文
     */
    private String nameCn;

    /**
     * 专项练习名称-英文
     */
    private String nameEn;

    /**
     * 专项练习名称-其他
     */
    private String nameOt;

    /**
     * 练习类型
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 难度等级 1 简单 2中等 3 困难
     */
    private Integer difficultyLevel;

    /**
     * 题目集合
     */
    private String question;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 专项练习ID
     */
    private Long specialExerciseId;

    /**
     * 互动课名称
     */
    private String interactiveCourseName;

    /**
     * IDS
     */
    private List<Long> ids;

    /**
     * 排除的IDS
     */
    private List<Long> excludeIds;

}