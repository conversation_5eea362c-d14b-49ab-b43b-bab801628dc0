package com.xt.hsk.module.game.controller.app.specialexercise.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.game.enums.record.GameRecordPracticeStatusEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseRecordSourceEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 游戏-专项练习-练习组 分页 req vo
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpecialExerciseAppPageReqVO extends PageParam {

    /**
     * 专项练习名称
     */
    private String name;

    /**
     * 练习类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 难度等级 1 简单 2中等 3 困难
     */
    private Integer difficultyLevel;

    /**
     * 练习状态 1 进行中 2 已完成 3未开始
     *
     * @see GameRecordPracticeStatusEnum
     */
    private Integer practiceStatus;

    /**
     * 难度等级列表 1 简单 2中等 3 困难
     */
    private List<Integer> difficultyLevelList;

    /**
     * 练习状态列表 1 进行中 2 已完成 3未开始
     *
     * @see GameRecordPracticeStatusEnum
     */
    private List<Integer> practiceStatusList;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 来源 1专项练习 2互动课
     *
     * @see SpecialExerciseRecordSourceEnum
     */
    private Integer source;

}