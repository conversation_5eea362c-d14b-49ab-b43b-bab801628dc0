package com.xt.hsk.module.game.controller.app.record.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xt.hsk.module.game.enums.record.GameRecordCorrectTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 游戏作答记录详细信息 App req VO
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
public class GameAnswerRecordDetailsAppReqVO {

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 练习组ID
     */
    private Long exerciseQuestionId;

    /**
     * 练习组题目版本库ID
     */
    private Long exerciseQuestionVersionId;

    /**
     * 类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     */
    private Integer type;

    /**
     * 题目内容
     */
    private String questionContent;

    /**
     * 参考答案
     */
    private String referenceAnswer;

    /**
     * 用户答案
     */
    private String userAnswer;

    /**
     * 是否正确 0错误 1正确
     *
     * @see GameRecordCorrectTypeEnum
     */
    private Integer isCorrect;

    /**
     * 答题时间
     */
    private LocalDateTime answerTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 第几次答题
     */
    private Integer answerSequence;

    /**
     * 拼音
     */
    private String pinyin;


}