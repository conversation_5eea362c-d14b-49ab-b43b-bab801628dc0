package com.xt.hsk.module.game.controller.admin.exercisequestion.vo;

import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 练习组题目 app resp vo
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
public class ExerciseQuestionAppRespVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 专项练习ID
     */
    private Long specialExerciseId;

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 翻译
     */
    private String translationOt;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 展示轮次（仅word_matching）
     */
    private Integer showRound;

    /**
     * 轮次数（仅word_matching）
     */
    private Integer roundNumber;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 练习类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     * @see SpecialExerciseTypeEnum
     */
    private String type;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 题干内容
     */
    private String questionContent;

    /**
     * 题干的拼音
     */
    private String pinyin;

    /**
     * 拆分后的题干
     */
    private String questionSplit;

    /**
     * 音频链接
     */
    private String audioUrl;

    /**
     * 考察点
     */
    private String knowledgePoint;

    /**
     * 考察点拼音
     */
    private String knowledgePointPinyin;

    /**
     * 参考答案
     */
    private String referenceAnswer;

    /**
     * 练习组题目ID
     */
    private Long exerciseQuestionId;

    /**
     * 练习组题目版本库ID
     */
    private Long exerciseQuestionVersionId;
    /**
     * 是否被收藏
     */
    private Boolean isFavorite;

}