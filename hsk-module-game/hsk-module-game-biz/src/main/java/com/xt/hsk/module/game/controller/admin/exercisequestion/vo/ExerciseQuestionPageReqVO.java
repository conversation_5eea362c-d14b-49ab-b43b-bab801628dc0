package com.xt.hsk.module.game.controller.admin.exercisequestion.vo;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 练习组题目 req vo
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ExerciseQuestionPageReqVO extends PageParam {

    /**
     * 专项练习ID
     */
    private Long specialExerciseId;

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 翻译
     */
    private String translationOt;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 展示轮次（仅word_matching）
     */
    private Integer showRound;

    /**
     * 轮次数（仅word_matching）
     */
    private Integer roundNumber;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 类型
     *
     * @see SpecialExerciseTypeEnum
     */
    private String type;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}