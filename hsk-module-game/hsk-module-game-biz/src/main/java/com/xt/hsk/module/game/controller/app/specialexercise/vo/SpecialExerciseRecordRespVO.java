package com.xt.hsk.module.game.controller.app.specialexercise.vo;

import com.xt.hsk.module.game.enums.record.GameRecordPracticeStatusEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.Data;

/**
 * 专项练习记录 RESP VO
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Data
public class SpecialExerciseRecordRespVO {

    /**
     * id
     */
    private Long id;

    /**
     * 专项练习ID
     */
    private Long specialExerciseId;

    /**
     * 专项练习名称-中文
     */
    private String nameCn;

    /**
     * 专项练习名称-英文
     */
    private String nameEn;

    /**
     * 专项练习名称-其他
     */
    private String nameOt;

    /**
     * 练习类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 难度等级 1 简单 2中等 3 困难
     */
    private Integer difficultyLevel;

    /**
     * 总题数
     */
    private Integer totalQuestions;

    /**
     * 答对数量
     */
    private Integer correctQuestions;

    /**
     * 答错数量
     */
    private Integer wrongQuestions;

    /**
     * 进度
     */
    private Integer progress;

    /**
     * 练习状态 1进行中 2已完成 3未开始
     *
     * @see GameRecordPracticeStatusEnum
     */
    private Integer practiceStatus;

    /**
     * 练习总记录id
     */
    private Long recordSummaryId;

    /**
     * 互动课单元ID（当练习来源于互动课时）
     */
    private Long interactiveCourseUnitId;


}