package com.xt.hsk.module.game.controller.admin.specialexercise;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.framework.common.pojo.CommonResult.success;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.SPECIAL_EXERCISE_UNKNOWN_EXERCISE_TYPE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.ImportResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExerciseDeleteCheckRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExerciseExportReqVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExerciseInfoRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExercisePageReqVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExercisePageRespVO;
import com.xt.hsk.module.game.controller.admin.specialexercise.vo.SpecialExerciseSaveReqVO;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import com.xt.hsk.module.game.manager.specialexercise.admin.SpecialExerciseAdminManager;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.FileCopyUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 游戏-专项练习-练习组 后台管理 控制器
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/game/special-exercise")
public class SpecialExerciseAdminController {

    @Resource
    private SpecialExerciseAdminManager specialExerciseAdminManager;

    @Resource
    private ExportTaskApi exportTaskApi;

    /**
     * 创建专项练习-练习组
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('game:special-exercise:create')")
    @LogRecord(type = LogRecordType.SPECIAL_EXERCISE_TYPE,
        subType = "创建专项练习", bizNo = "{{#exerciseId}}",
        success = "创建专项练习：【{{#exercise.nameCn}}】，类型：{{#typeDesc}}")
    public CommonResult<Boolean> createSpecialExercise(@Valid @RequestBody SpecialExerciseSaveReqVO createReqVO) {
        specialExerciseAdminManager.createSpecialExercise(createReqVO);
        return success(true);
    }

    /**
     * 更新专项练习-练习组
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('game:special-exercise:update')")
    @LogRecord(type = LogRecordType.SPECIAL_EXERCISE_TYPE,
        subType = "修改专项练习",
        bizNo = "{{#updateReqVO.id}}",
        success = "修改专项练习：【{{#exercise.nameCn}}】，类型：{{#typeDesc}}")
    public CommonResult<Boolean> updateSpecialExercise(@Valid @RequestBody SpecialExerciseSaveReqVO updateReqVO) {
        specialExerciseAdminManager.updateSpecialExercise(updateReqVO);
        return success(true);
    }

    /**
     * 批量删除专项练习-练习组
     */
    @DeleteMapping("/batch-delete")
    @PreAuthorize("@ss.hasPermission('game:special-exercise:delete')")
    @LogRecord(type = LogRecordType.SPECIAL_EXERCISE_TYPE,
        subType = "批量删除专项练习",
        bizNo = "{{#batchBizNo}}",
        success = "批量删除专项练习：共删除{{#deleteCount}}个练习，ID列表：{{#updateReqVO.ids}}")
    public CommonResult<Boolean> deleteByIds(@RequestBody SpecialExerciseSaveReqVO updateReqVO) {
        specialExerciseAdminManager.deleteByIds(updateReqVO.getIds());
        return success(true);
    }

    /**
     * 根据id获取专项练习-练习组
     */
    @PostMapping("/get")
    @PreAuthorize("@ss.hasPermission('game:special-exercise:query')")
    public CommonResult<SpecialExerciseInfoRespVO> getSpecialExercise(@RequestParam("id") Long id) {
        return success(specialExerciseAdminManager.getSpecialExercise(id));
    }

    /**
     * 分页获取专项练习-练习组
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('game:special-exercise:query')")
    public CommonResult<PageResult<SpecialExercisePageRespVO>> getSpecialExercisePage(@Valid @RequestBody SpecialExercisePageReqVO pageReqVO) {
        return success(specialExerciseAdminManager.getSpecialExercisePage(pageReqVO));
    }

    /**
     * 批量显示/隐藏专项练习
     */
    @PutMapping("/batch-status")
    @LogRecord(type = LogRecordType.SPECIAL_EXERCISE_TYPE,
        subType = "批量更新专项练习状态", bizNo = "{{#batchBizNo}}",
        success = "批量{{#statusText}}专项练习：共{{#statusText}}{{#updateCount}}个练习")
    @PreAuthorize("@ss.hasPermission('game:special-exercise:update')")
    public CommonResult<Boolean> batchUpdateStatus(@RequestBody SpecialExerciseSaveReqVO updateReqVO) {
        specialExerciseAdminManager.batchUpdateStatus(updateReqVO.getIds(), updateReqVO.getIsShow());
        return success(true);
    }

    /**
     * 修改专项练习排序
     */
    @PutMapping("/sort")
    @LogRecord(type = LogRecordType.SPECIAL_EXERCISE_TYPE,
        subType = "修改专项练习排序", bizNo = "{{#id}}", success = "修改专项练习排序：【{{#exercise.nameCn}}】从第{{#oldSort}}位调整到第{{#sort}}位")
    @PreAuthorize("@ss.hasPermission('game:special-exercise:update')")
    public CommonResult<Boolean> updateSort(@RequestParam("id") Long id, @RequestParam("sort") Integer sort) {
        specialExerciseAdminManager.updateSort(id, sort);
        return success(true);
    }


    /**
     * 导出专项练习数据
     */
    @LogRecord(type = LogRecordType.SPECIAL_EXERCISE_TYPE, subType = "导出专项练习", bizNo = "{{#taskId}}", success = "导出专项练习：创建异步导出任务")
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermission('game:special-exercise:export')")
    public CommonResult<Long> exportSpecialExercise(@RequestBody @Valid SpecialExerciseExportReqVO exportReqVO) {
        try {
            // 根据类型选择不同的导出任务
            ExportTaskTypeEnum taskType;
            if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(exportReqVO.getType())) {
                taskType = ExportTaskTypeEnum.WORD_MATCHING_EXERCISE;
            } else if (SpecialExerciseTypeEnum.STROKE_WRITING.getCode().equals(exportReqVO.getType())) {
                taskType = ExportTaskTypeEnum.STROKE_WRITING_EXERCISE;
            } else if (SpecialExerciseTypeEnum.WORD_TO_SENTENCE.getCode().equals(exportReqVO.getType())) {
                taskType = ExportTaskTypeEnum.WORD_TO_SENTENCE_EXERCISE;
            } else if (SpecialExerciseTypeEnum.KARAOKE.getCode().equals(exportReqVO.getType())) {
                taskType = ExportTaskTypeEnum.KARAOKE_EXERCISE;
            } else {
                throw exception(SPECIAL_EXERCISE_UNKNOWN_EXERCISE_TYPE);
            }

            ObjectMapper objectMapper = new ObjectMapper();
            // 将导出请求参数和任务名称一起传递
            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(exportReqVO.getTaskName());
            taskParams.setQueryParams(exportReqVO);

            String params = objectMapper.writeValueAsString(taskParams);
            Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(), taskType, params);
            LogRecordContext.putVariable("taskId", taskId);
            return success(taskId);
        } catch (Exception e) {
            log.error("创建专项练习导出任务失败", e);
            return CommonResult.error(500, "创建导出任务失败：" + e.getMessage());
        }
    }

    /**
     * 导入专项练习
     *
     * @param file 导入文件
     * @param type 类型
     * @return 导入结果
     */
    @PostMapping("/import")
    @PreAuthorize("@ss.hasPermission('game:special-exercise:import')")
    public CommonResult<ImportResult> importSpecialExercise(@RequestParam("file") MultipartFile file,@RequestParam("type") Integer type) {
        return success(specialExerciseAdminManager.importSpecialExercise(file,type));
    }

    /**
     * 下载导入模板
     *
     * @param response 响应
     */
    @PostMapping("/import/template")
    @PreAuthorize("@ss.hasPermission('game:special-exercise:import')")
    public void downloadImportTemplate(HttpServletResponse response, @RequestParam("type") Integer type) throws IOException {

        String fileNameStr;

        if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(type)) {
            fileNameStr = "单词连连看练习组上传模板";
        } else if (SpecialExerciseTypeEnum.STROKE_WRITING.getCode().equals(type)) {
            fileNameStr = "笔画书写练习组上传模板";
        } else if (SpecialExerciseTypeEnum.WORD_TO_SENTENCE.getCode().equals(type)) {
            fileNameStr = "连词成句练习组上传模板";
        } else if (SpecialExerciseTypeEnum.KARAOKE.getCode().equals(type)) {
            fileNameStr = "卡拉OK练习组上传模板";
        } else {
            throw exception(SPECIAL_EXERCISE_UNKNOWN_EXERCISE_TYPE);
        }

        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(fileNameStr, StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        // 获取模板文件路径
        ClassPathResource resource = new ClassPathResource("excelTemplate/" + fileNameStr + ".xlsx");
        InputStream inputStream = resource.getInputStream();

        // 写入响应流
        FileCopyUtils.copy(inputStream, response.getOutputStream());
    }

    /**
     * 检查专项练习是否可以删除
     */
    @PostMapping("/check-delete")
    @PreAuthorize("@ss.hasPermission('game:special-exercise:query')")
    public CommonResult<SpecialExerciseDeleteCheckRespVO> checkSpecialExerciseCanDelete(@RequestBody SpecialExerciseSaveReqVO updateReqVO) {
        return success(specialExerciseAdminManager.checkSpecialExerciseCanDelete(updateReqVO.getIds()));
    }

}