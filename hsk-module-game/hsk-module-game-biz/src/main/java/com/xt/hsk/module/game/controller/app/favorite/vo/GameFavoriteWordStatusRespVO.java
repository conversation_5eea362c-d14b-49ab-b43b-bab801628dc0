package com.xt.hsk.module.game.controller.app.favorite.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询字词收藏状态响应VO
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GameFavoriteWordStatusRespVO {

    /**
     * 收藏状态 0-未收藏，1-已收藏，2-字词不存在
     */
    private Integer favoriteStatus;

    /**
     * 字词不存在
     */
    public static GameFavoriteWordStatusRespVO notExist() {
        return new GameFavoriteWordStatusRespVO(2);
    }

}