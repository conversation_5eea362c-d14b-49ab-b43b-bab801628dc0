package com.xt.hsk.module.game.controller.app.record;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.game.controller.app.record.vo.GameAnswerRecordAppReqVO;
import com.xt.hsk.module.game.controller.app.record.vo.GameAnswerRecordAppRespVO;
import com.xt.hsk.module.game.controller.app.record.vo.GameKaraokeAnswerRecordAppRespVO;
import com.xt.hsk.module.game.manager.record.app.GameAnswerRecordAppManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 游戏作答记录 app 控制层
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@RestController
@RequestMapping("/game/record")
public class GameAnswerRecordAppController {

    @Resource
    private GameAnswerRecordAppManager gameAnswerRecordAppManager;

    /**
     * 提交答案
     *
     * @param reqVO 请求参数
     * @return 提交结果
     */
    @PostMapping("/submit-answer")
    public CommonResult<GameAnswerRecordAppRespVO> submitAnswer(@RequestBody GameAnswerRecordAppReqVO reqVO) {
        return CommonResult.success(gameAnswerRecordAppManager.submitAnswer(reqVO));
    }

    /**
     * 获取答题报告
     *
     * @param audioFile                    音频文件
     * @param recordSummaryIdStr           记录汇总ID
     * @param specialExerciseIdStr         专项练习ID
     * @param exerciseQuestionVersionIdStr 练习题版本ID
     * @param questionContent              题目内容
     * @param currentRoundStr              当前回合
     * @return 答题报告
     */
    @PostMapping("/answer-report")
    public CommonResult<GameKaraokeAnswerRecordAppRespVO> getAnswerReport(
            @RequestParam("audio") MultipartFile audioFile,
            @RequestParam("recordSummaryId") String recordSummaryIdStr,
            @RequestParam("specialExerciseId") String specialExerciseIdStr,
            @RequestParam("exerciseQuestionVersionId") String exerciseQuestionVersionIdStr,
            @RequestParam("questionContent") String questionContent,
            @RequestParam("currentRound") String currentRoundStr
    ) {
        return CommonResult.success(gameAnswerRecordAppManager.getAnswerReport(
                audioFile,
                recordSummaryIdStr,
                specialExerciseIdStr,
                exerciseQuestionVersionIdStr,
                questionContent,
                currentRoundStr));
    }

    /**
     * 保存当前回合数
     */
    @PostMapping("/save-current-round")
    public CommonResult<Boolean> saveCurrentRound(@RequestBody GameAnswerRecordAppReqVO reqVO) {
        gameAnswerRecordAppManager.saveCurrentRound(reqVO);
        return CommonResult.success(true);
    }

}
