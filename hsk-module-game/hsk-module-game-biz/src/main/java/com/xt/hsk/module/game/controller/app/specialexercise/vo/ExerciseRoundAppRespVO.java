package com.xt.hsk.module.game.controller.app.specialexercise.vo;

import cn.hutool.core.bean.BeanUtil;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionAppRespVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 游戏-专项练习-轮次 app resp vo
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExerciseRoundAppRespVO {

    /**
     * 题目列表
     */
    private List<WordMatchingBaseInfoAppRespVO> questionList;

    /**
     * 答案列表
     */
    private List<WordMatchingBaseInfoAppRespVO> answerList;

    /**
     * 操作类型：true-收藏 false-取消收藏
     */
    private Boolean isFavorite;

    public ExerciseRoundAppRespVO(List<ExerciseQuestionAppRespVO> voList) {
        List<WordMatchingBaseInfoAppRespVO> wordMatchingVoList = BeanUtil.copyToList(voList, WordMatchingBaseInfoAppRespVO.class);
        this.questionList = wordMatchingVoList;
        this.answerList = wordMatchingVoList;
    }

}