package com.xt.hsk.module.game.controller.admin.question.vo;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 连词成句题目 Excel 导入 VO
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class KaraokeQuestionImportExcelVO {

    @ExcelProperty("*题干")
    private String questionContent;

    @ExcelProperty("*HSK等级")
    private Integer hskLevel;
} 