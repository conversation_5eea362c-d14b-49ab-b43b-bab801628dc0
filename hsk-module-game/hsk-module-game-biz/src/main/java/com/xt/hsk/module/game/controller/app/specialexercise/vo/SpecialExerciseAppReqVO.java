package com.xt.hsk.module.game.controller.app.specialexercise.vo;

import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseAnswerModeEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseRecordSourceEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.Data;

/**
 * 游戏-专项练习-练习组 app req vo
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Data
public class SpecialExerciseAppReqVO {

    /**
     * 专项练习id
     */
    private Long specialExerciseId;

    /**
     * 练习类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     * @see SpecialExerciseTypeEnum
     */
    @InEnum(value = SpecialExerciseTypeEnum.class,message = "请选择正确的练习类型")
    private Integer type;

    /**
     * 答题模式：1 重新作答 2 继续作答
     *
     * @see SpecialExerciseAnswerModeEnum
     */
    @InEnum(value = SpecialExerciseAnswerModeEnum.class,message = "请选择正确的答题模式")
    private Integer answerType;

    /**
     * 练习总记录id
     */
    private Long recordSummaryId;

    /**
     * 互动课单元ID 只有从互动课进来练习时才会有
     */
    private Long interactiveCourseUnitId;

    /**
     * 来源 1专项练习 2互动课
     * @see SpecialExerciseRecordSourceEnum
     */
    @InEnum(value = SpecialExerciseRecordSourceEnum.class,message = "请选择正确的来源")
    private Integer source;

    /**
     * HSK等级
     */
    private Integer hskLevel;
}