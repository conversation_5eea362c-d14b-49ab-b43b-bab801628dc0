package com.xt.hsk.module.game.producer.game;

import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseRecordBizTypeEnum;
import com.xt.hsk.module.edu.event.interactivecourse.PracticeCompletedEvent;
import com.xt.hsk.module.edu.event.interactivecourse.PracticeStartedEvent;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 专项练习相关的 Producer
 *
 * <AUTHOR>
 * @since 2025/07/14
 */
@Slf4j
@Component
public class GameProducer {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 交互式课程消息
     *
     * @param userId 用户 ID
     * @param unitId 互动课单元 ID
     * @param gameId 专项练习 ID
     * @param bizId  专项练习记录 ID
     */
    public void interactiveCourseMessage(Long userId,Long unitId,Long gameId,Long bizId) {
        PracticeStartedEvent startedEvent = new PracticeStartedEvent(this, userId, unitId,
            InteractiveCourseRecordBizTypeEnum.SPECIAL_EXERCISE_RECORD, bizId, gameId, null);
        applicationContext.publishEvent(startedEvent);
    }

    /**
     * 发送互动课练习完成事件
     *
     * @param userId 用户 ID
     * @param unitId 互动课单元 ID
     * @param recordSummaryId 专项练习记录汇总 ID
     * @param accuracy 正确率
     * @param hasAICorrection 是否有AI批改
     */
    public void sendPracticeCompletedEvent(Long userId, Long unitId, Long recordSummaryId,
                                         BigDecimal accuracy, Boolean hasAICorrection) {
        if (unitId == null) {
            log.debug("互动课单元ID为空，跳过发送练习完成事件: userId={}, recordSummaryId={}",
                     userId, recordSummaryId);
            return;
        }

        log.info("发送互动课练习完成事件: userId={}, unitId={}, recordSummaryId={}, accuracy={}, hasAICorrection={}",
                userId, unitId, recordSummaryId, accuracy, hasAICorrection);

        PracticeCompletedEvent completedEvent = new PracticeCompletedEvent(
            this, userId, unitId, InteractiveCourseRecordBizTypeEnum.SPECIAL_EXERCISE_RECORD,
            accuracy, hasAICorrection, recordSummaryId);
        applicationContext.publishEvent(completedEvent);
    }


}
