package com.xt.hsk.module.game.controller.app.favorite;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.game.controller.app.favorite.vo.GameFavoriteWordStatusReqVO;
import com.xt.hsk.module.game.controller.app.favorite.vo.GameFavoriteWordStatusRespVO;
import com.xt.hsk.module.game.manager.favorite.app.GameFavoriteAppManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 游戏收藏 app 控制层
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/game/favorite")
public class GameFavoriteAppController {

    @Resource
    private GameFavoriteAppManager gameFavoriteAppManager;

    /**
     * 检查单词收藏状态
     */
    @PostMapping("/word/status")
    public CommonResult<GameFavoriteWordStatusRespVO> checkWordFavoriteStatus(@RequestBody GameFavoriteWordStatusReqVO reqVO) {
        return CommonResult.success(gameFavoriteAppManager.checkWordFavoriteStatus(reqVO));
    }

}