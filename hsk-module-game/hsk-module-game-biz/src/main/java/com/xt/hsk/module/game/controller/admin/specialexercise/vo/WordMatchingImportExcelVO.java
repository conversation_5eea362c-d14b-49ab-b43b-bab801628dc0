package com.xt.hsk.module.game.controller.admin.specialexercise.vo;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 单词连连看 Excel 导入 VO
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class WordMatchingImportExcelVO {

    @ExcelProperty("*练习组名称")
    private String nameCn;

    @ExcelProperty("练习组名称英语")
    private String nameEn;

    @ExcelProperty("练习组名称越南语")
    private String nameOt;

    @ExcelProperty("*HSK等级")
    private Integer hskLevel;

    @ExcelProperty("难度")
    private String difficultyLevel;

    @ExcelProperty("*轮次")
    private Integer roundNumber;

    @ExcelProperty("*汉字")
    private String questionContent;

    @ExcelProperty("*越南语")
    private String translationOt;

    /**
     * 行号
     */
    private Integer rowNum;

} 