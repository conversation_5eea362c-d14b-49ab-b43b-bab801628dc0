package com.xt.hsk.module.game.controller.admin.question.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 题库版本库 保存请求 vo
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
public class GameQuestionVersionSaveReqVO {

    /**
     * 题库ID
     */
    private Long id;

    /**
     * HSK等级
     */
    @NotNull(message = "HSK等级不能为空")
    private Integer hskLevel;

    /**
     * 题目类型
     */
    @NotNull(message = "题目类型不能为空")
    private Integer type;

    /**
     * 题目ID
     */
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /**
     * 题干内容
     */
    private String questionContent;

    /**
     * 拆分后的题干
     */
    private String questionSplit;

    /**
     * 题干的拼音
     */
    private String pinyin;

    /**
     * 音频链接
     */
    private String audioUrl;

    /**
     * 考察点
     */
    private String knowledgePoint;

    /**
     * 参考答案
     */
    private String referenceAnswer;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

}