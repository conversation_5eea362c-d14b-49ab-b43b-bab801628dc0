package com.xt.hsk.module.game.controller.app.specialexercise.vo;

import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 专项练习报告 app RESP VO
 *
 * <AUTHOR>
 * @since 2025-07-12
 */
@Data
public class GameReportAppRespVO {

    /**
     * 专项练习ID
     */
    private Long specialExerciseId;

    /**
     * 专项练习名称
     */
    private String name;

    /**
     * 练习类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;

    /**
     * 总题数
     */
    private Integer totalQuestions;

    /**
     * 答对数量
     */
    private Integer correctQuestions;

    /**
     * 答错数量
     */
    private Integer wrongQuestions;

    /**
     * 正确率
     */
    private Integer accuracy;

    /**
     * 平均分
     */
    private Integer averageScore;

    /**
     * 正确单词列表
     */
    private List<GameReportDetailsAppRespVO> correctList;

    /**
     * 错误单词列表
     */
    private List<GameReportDetailsAppRespVO> wrongList;


}