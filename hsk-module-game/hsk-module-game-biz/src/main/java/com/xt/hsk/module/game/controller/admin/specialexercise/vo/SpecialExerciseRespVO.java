package com.xt.hsk.module.game.controller.admin.specialexercise.vo;

import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 游戏-专项练习-练习组 RESP VO
 *
 * <AUTHOR>
 * @since 2025-06-13
 */
@Data
public class SpecialExerciseRespVO {

    /**
     * 专项练习ID
     */
    private Long id;

    /**
     * 专项练习名称-中文
     */
    private String nameCn;

    /**
     * 专项练习名称-英文
     */
    private String nameEn;

    /**
     * 专项练习名称-其他
     */
    private String nameOt;

    /**
     * 练习类型
     *
     * @see SpecialExerciseTypeEnum
     */
    private Integer type;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 难度等级 1 简单 2中等 3 困难
     */
    private Integer difficultyLevel;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 练习题版本库的最新版本
     */
    private Integer relatedVersion;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}