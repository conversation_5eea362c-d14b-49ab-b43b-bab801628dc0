package com.xt.hsk.module.game.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.listener.BaseExportTask;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 连词成句导出任务
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Slf4j
@Component("wordToSentenceQuestionExportTask")
public class WordToSentenceQuestionExportTask extends
    BaseExportTask<WordToSentenceQuestionExportTaskExport> {

    @Resource
    private WordToSentenceQuestionExportTaskExport wordToSentenceQuestionExportTaskExport;

    @Override
    protected WordToSentenceQuestionExportTaskExport getExporter() {
        return wordToSentenceQuestionExportTaskExport;
    }

    @Override
    protected String getFileName() {
        return "连词成句题目数据";
    }

    @Override
    protected Map<String, Object> buildQueryParams(String params) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            ExportTaskParams taskParams = objectMapper.readValue(params, ExportTaskParams.class);

            // 返回查询参数，任务名称已经在创建任务时使用，这里不需要处理
            return objectMapper.convertValue(taskParams.getQueryParams(),
                    new TypeReference<Map<String, Object>>() {
                    });
        } catch (Exception e) {
            log.error("解析导出参数失败: {}", e.getMessage(), e);
            return Map.of();
        }
    }
} 