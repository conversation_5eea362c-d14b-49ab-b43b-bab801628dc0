package com.xt.hsk.module.game.controller.admin.specialexercise.vo;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 笔画书写 Excel 导入 VO
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class StrokeWritingImportExcelVO {

    @ExcelProperty("*练习组名称")
    private String nameCn;

    @ExcelProperty("练习组名称英语")
    private String nameEn;

    @ExcelProperty("练习组名称越南语")
    private String nameOt;

    @ExcelProperty("*HSK等级")
    private Integer hskLevel;

    @ExcelProperty("难度")
    private String difficultyLevel;

    @ExcelProperty("*序号")
    private Integer sort;

    @ExcelProperty("*汉字")
    private String questionContent;

    /**
     * 行号
     */
    private Integer rowNum;

} 