package com.xt.hsk.module.game.controller.app.correction;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.game.controller.app.correction.vo.UserCorrectionRemainingCountReqVO;
import com.xt.hsk.module.game.controller.app.correction.vo.UserCorrectionRemainingCountRespVO;
import com.xt.hsk.module.game.manager.correction.app.GameCorrectionAppManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 专项练习批改 app 控制层
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Slf4j
@RestController
@RequestMapping("/game/correction")
public class GameCorrectionAppController {

    @Resource
    private GameCorrectionAppManager gameCorrectionAppManager;

    /**
     * 获取用户剩余批改次数
     */
    @PostMapping("/v1/remaining-count")
    public CommonResult<UserCorrectionRemainingCountRespVO> getUserRemainingCount(@RequestBody UserCorrectionRemainingCountReqVO reqVO) {
        return CommonResult.success(gameCorrectionAppManager.getUserRemainingCount(reqVO.getType()));
    }

}
