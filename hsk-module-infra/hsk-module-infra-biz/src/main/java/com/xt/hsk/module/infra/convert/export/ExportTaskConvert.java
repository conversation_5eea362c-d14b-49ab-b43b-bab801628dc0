package com.xt.hsk.module.infra.convert.export;

import com.xt.hsk.module.infra.controller.admin.export.vo.ExportTaskRespVO;
import com.xt.hsk.module.infra.dal.dataobject.export.ExportTaskDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 导出任务 Convert
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Mapper
public interface ExportTaskConvert {

    ExportTaskConvert INSTANCE = Mappers.getMapper(ExportTaskConvert.class);


    List<ExportTaskRespVO> convertToRespVOPage(List<ExportTaskDO> page);
} 