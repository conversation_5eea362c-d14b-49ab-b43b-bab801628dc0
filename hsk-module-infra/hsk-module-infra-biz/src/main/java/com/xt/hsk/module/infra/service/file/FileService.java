package com.xt.hsk.module.infra.service.file;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.infra.controller.admin.file.vo.file.FileCreateReqVO;
import com.xt.hsk.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import com.xt.hsk.module.infra.controller.admin.file.vo.file.FilePresignedUrlRespVO;
import com.xt.hsk.module.infra.dal.dataobject.file.FileDO;
import jakarta.validation.constraints.NotEmpty;

/**
 * 文件 Service 接口
 *
 * <AUTHOR>
 */
public interface FileService {

    /**
     * 获得文件分页
     *
     * @param pageReqVO 分页查询
     * @return 文件分页
     */
    PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO);

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param content 文件内容
     * @param name    文件名称，允许空
     * @param directory 目录，允许空
     * @param type    文件的 MIME 类型，允许空
     * @return 文件路径
     */
    String createFile(@NotEmpty(message = "文件内容不能为空") byte[] content,
                      String name, String directory, String type);

    /**
     * 生成文件预签名地址信息
     *
     * @param name 文件名
     * @param directory 目录
     * @return 预签名地址信息
     */
    FilePresignedUrlRespVO getFilePresignedUrl(@NotEmpty(message = "文件名不能为空") String name,
                                               String directory);

    /**
     * 创建文件
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createFile(FileCreateReqVO createReqVO);

    /**
     * 删除文件
     *
     * @param id 编号
     */
    void deleteFile(Long id) throws Exception;

    /**
     * 获得文件内容
     *
     * @param configId 配置编号
     * @param path     文件路径
     * @return 文件内容
     */
    byte[] getFileContent(Long configId, String path) throws Exception;

}
