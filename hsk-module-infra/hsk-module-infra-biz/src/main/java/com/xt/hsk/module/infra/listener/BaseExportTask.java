package com.xt.hsk.module.infra.listener;

import com.xt.hsk.module.infra.dal.dataobject.export.ExportTaskDO;
import com.xt.hsk.module.infra.dal.mysql.export.ExportTasksMapper;
import com.xt.hsk.module.infra.enums.export.ExportTaskStatusEnum;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * Excel导出任务的基础抽象类 提供了导出任务的基本流程和状态管理
 *
 * <AUTHOR>
 * @since 2025/02/26
 */
@Slf4j
public abstract class BaseExportTask<T extends BaseEasyExcelExport<?>> implements IExportTask {

    /**
     * 导出任务数据访问对象
     */
    @Resource
    private ExportTasksMapper exportTasksMapper;

    /**
     * 获取具体的Excel导出器实例
     *
     * @return 导出器实例
     */
    protected abstract T getExporter();

    /**
     * 获取导出文件名称
     *
     * @return 文件名称
     */
    protected abstract String getFileName();

    /**
     * 构建查询参数
     *
     * @param params 任务参数字符串
     * @return 解析后的查询参数Map
     */
    protected abstract Map<String, Object> buildQueryParams(String params);

    /**
     * 执行导出任务 包含任务状态更新、数据导出和异常处理
     *
     * @param task 导出任务实体
     */
    @Override
    public void executeExport(ExportTaskDO task) {
        log.info("=== 开始执行导出任务 === taskId: {}, taskName: {}, type: {}",
            task.getId(), task.getTaskName(), task.getType());

        try {
            log.info("步骤1: 更新任务状态为执行中, taskId: {}", task.getId());

            // 更新任务状态为执行中
            task.setStatus(ExportTaskStatusEnum.PROCESSING.getCode());
            task.setStartTime(new Date());
            task.setPid(String.valueOf(Thread.currentThread().getId()));

            log.info("步骤1.1: 准备更新数据库, taskId: {}, status: {}, pid: {}",
                task.getId(), task.getStatus(), task.getPid());

            exportTasksMapper.updateById(task);

            log.info("步骤1.2: 数据库更新完成, taskId: {}", task.getId());

            log.info("步骤2: 构建查询参数, taskId: {}", task.getId());

            // 执行导出
            Map<String, Object> queryParams = buildQueryParams(task.getParams());

            log.info("步骤2.1: 查询参数构建完成, taskId: {}, params: {}", task.getId(),
                queryParams);

            log.info("步骤3: 开始调用导出器生成Excel, taskId: {}", task.getId());

            // 调用具体的导出实现，生成Excel并上传到云存储
            String fileUrl = getExporter().uploadExcelToCloudStorage(getFileName(), queryParams,task);

            log.info("步骤3.1: Excel生成并上传完成, taskId: {}, fileUrl: {}", task.getId(),
                fileUrl);

            log.info("步骤4: 更新任务状态为已完成, taskId: {}", task.getId());

            // 更新任务状态为已完成
            task.setStatus(ExportTaskStatusEnum.COMPLETED.getCode());
            task.setFinishedAt(new Date());
            task.setDownloadUrl(fileUrl);

            log.info("步骤4.1: 准备更新最终状态到数据库, taskId: {}, status: {}, url: {}",
                task.getId(), task.getStatus(), fileUrl);

        } catch (Exception e) {
            // 记录错误日志
            log.error("=== 导出任务执行失败 === taskId: {}, 错误类型: {}, 错误信息: {}",
                task.getId(), e.getClass().getSimpleName(), e.getMessage(), e);

            // 更新任务状态为失败
            task.setStatus(ExportTaskStatusEnum.FAILED.getCode());
            task.setError(e.getClass().getSimpleName() + ": " + e.getMessage());

            log.info("步骤ERROR: 设置任务状态为失败, taskId: {}, error: {}",
                task.getId(), task.getError());

        } finally {
            try {
                log.info("步骤FINAL: 执行最终数据库更新, taskId: {}, finalStatus: {}",
                    task.getId(), task.getStatus());

                // 无论成功或失败，都更新任务信息到数据库
                exportTasksMapper.updateById(task);

                log.info("=== 导出任务处理完成 === taskId: {}, finalStatus: {}, url: {}",
                    task.getId(), task.getStatus(), task.getDownloadUrl());

            } catch (Exception finalException) {
                log.error("=== 最终数据库更新失败 === taskId: {}, 错误: {}",
                    task.getId(), finalException.getMessage(), finalException);
            }
        }
    }
}
