package com.xt.hsk.module.infra.listener;

import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import cn.idev.excel.exception.ExcelAnalysisException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 组合行数限制监听器
 * 同时处理行数限制和数据导入
 *
 * <AUTHOR>
 * @since 2025/02/26
 */
@Slf4j
public class CompositeRowLimitListener<T> extends AnalysisEventListener<T> {

    /**
     * 最大行数限制
     */
    private final int maxRows;

    /**
     * 当前行数
     */
    @Getter
    private int currentRowCount = 0;

    /**
     * 实际的数据处理监听器
     */
    private final AnalysisEventListener<T> delegate;

    /**
     * 超过限制时的错误消息
     */
    private final String errorMessage;

    /**
     * 构造函数
     *
     * @param maxRows  最大行数限制
     * @param delegate 实际的数据处理监听器
     */
    public CompositeRowLimitListener(int maxRows, AnalysisEventListener<T> delegate) {
        this(max<PERSON><PERSON>, delegate, "导入数据超过最大限制" + maxR<PERSON> + "行，请分批导入");
    }

    /**
     * 构造函数
     *
     * @param maxRows      最大行数限制
     * @param delegate     实际的数据处理监听器
     * @param errorMessage 超过限制时的错误消息
     */
    public CompositeRowLimitListener(int maxRows, AnalysisEventListener<T> delegate, String errorMessage) {
        this.maxRows = maxRows;
        this.delegate = delegate;
        this.errorMessage = errorMessage;
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        currentRowCount++;

        // 检查是否超过最大行数限制
        if (currentRowCount > maxRows) {
            log.warn("导入数据行数({})超过最大限制({})", currentRowCount, maxRows);
            throw new ExcelAnalysisException(errorMessage);
        }

        // 委托给实际的监听器处理数据
        delegate.invoke(data, context);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("共读取{}行数据，最大限制{}行", currentRowCount, maxRows);
        delegate.doAfterAllAnalysed(context);
    }
} 