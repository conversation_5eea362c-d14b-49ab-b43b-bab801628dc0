package com.xt.hsk.module.system.listener;

import cn.idev.excel.metadata.Head;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.write.handler.CellWriteHandler;
import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteTableHolder;
import java.util.Arrays;
import java.util.List;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

public class QuestionMultiColumnMergeStrategy implements CellWriteHandler {

    private final int mergeRowIndex;
    private final List<Integer> mergeColumnIndices;
    private final int firstColumnIndex = 0; // 第一列索引

    // 记录当前分组的起始行和第一列值
    private String currentGroupFirstValue = null;
    private int currentGroupStartRow = -1;
    private int currentGroupEndRow = 0;

    public QuestionMultiColumnMergeStrategy(int mergeRowIndex, Integer... mergeColumnIndices) {
        this.mergeRowIndex = mergeRowIndex;
        this.mergeColumnIndices = Arrays.asList(mergeColumnIndices);
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                 List<WriteCellData<?>> cellDataList, Cell cell, Head head,
                                 Integer relativeRowIndex, Boolean isHead) {
        int curRowIndex = cell.getRowIndex();
        int curColIndex = cell.getColumnIndex();

        // 跳过表头行和非合并列
        if (curRowIndex <= mergeRowIndex || !mergeColumnIndices.contains(curColIndex)) {
            return;
        }

        // 获取当前行第一列的值
        Cell firstColumnCell = cell.getSheet().getRow(curRowIndex).getCell(firstColumnIndex);
        String currentFirstValue = getCellValue(firstColumnCell);

        // 初始化第一个分组
        if (currentGroupFirstValue == null) {
            currentGroupFirstValue = currentFirstValue;
            currentGroupStartRow = curRowIndex;
            return;
        }

        // 核心修复1：只要第一列值相同，立即触发合并（不再依赖最后一列）
        if (currentFirstValue.equals(currentGroupFirstValue)) {
            currentGroupEndRow=curRowIndex;
        } else {
            // 合并当前行与分组起始行（确保至少2行）
            if (currentGroupEndRow > currentGroupStartRow) {
                for (int col : mergeColumnIndices) {
                    mergeCells(writeSheetHolder.getSheet(), currentGroupStartRow, currentGroupEndRow, col);
                }
            }
            // 第一列值不同：结束当前分组，开始新分组
            currentGroupFirstValue = currentFirstValue;
            currentGroupStartRow = curRowIndex;
            currentGroupEndRow = curRowIndex;
        }
    }

    // 执行合并操作
    private void mergeCells(Sheet sheet, int startRow, int endRow, int colIndex) {
        if (startRow >= endRow) {
            return;
        }
        sheet.addMergedRegion(new CellRangeAddress(startRow, endRow, colIndex, colIndex));
    }




    // 处理不同类型的单元格值
    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((int) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    // 其他空实现的接口方法
    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                 Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {}

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {}

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, WriteCellData<?> cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
    }
}
