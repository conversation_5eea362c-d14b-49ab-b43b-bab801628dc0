package com.xt.hsk.module.infra.listener;

import cn.idev.excel.event.AnalysisEventListener;
import java.util.ArrayList;
import java.util.List;


/**
 * 自定义解析结果监听器
 *
 * <AUTHOR>
 * @since 2025/05/26
 */
public abstract class BaseAnalysisEventListener<T> extends AnalysisEventListener<T> {

    /**
     * 有效行数
     */
    public int VALID_COUNT;

    /**
     * 无效行数
     */
    public int INVALID_COUNT;

    /**
     * 是否成功 只要有一条数据落库就是成功
     */
    public boolean SUCCESS = true;


    /**
     * 错误信息
     */
    public final List<String> msg = new ArrayList<>();

}
