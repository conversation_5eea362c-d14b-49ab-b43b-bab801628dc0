package com.xt.hsk.module.infra.listener;

import cn.idev.excel.metadata.Head;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.write.handler.CellWriteHandler;
import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteTableHolder;
import java.util.List;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;

/**
 * Excel样式处理器 提供专业美观的表头和数据单元格样式
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
public class ExcelStyleHandler implements CellWriteHandler {

    /**
     * 表头背景色（现代蓝色）
     */
    private static final short HEADER_BACKGROUND_COLOR = IndexedColors.ROYAL_BLUE.getIndex();

    /**
     * 表头字体色（白色）
     */
    private static final short HEADER_FONT_COLOR = IndexedColors.WHITE.getIndex();

    /**
     * 副表头背景色（浅蓝色）
     */
    private static final short SUB_HEADER_BACKGROUND_COLOR = IndexedColors.CORNFLOWER_BLUE.getIndex();

    /**
     * 数据行背景色（白色）
     */
    private static final short DATA_BACKGROUND_COLOR = IndexedColors.WHITE.getIndex();

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder,
        WriteTableHolder writeTableHolder,
        Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 在单元格创建后设置样式
    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder,
        WriteTableHolder writeTableHolder,
        WriteCellData<?> cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 在数据转换后设置样式
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder,
        WriteTableHolder writeTableHolder,
        List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex,
        Boolean isHead) {

        Workbook workbook = writeSheetHolder.getSheet().getWorkbook();

        if (isHead != null && isHead) {
            // 表头样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            cell.setCellStyle(headerStyle);
        } else {
            // 数据行样式 - 修复行索引计算
            CellStyle dataStyle = createDataStyle(workbook, cell.getRowIndex());
            cell.setCellStyle(dataStyle);
        }
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 现代化背景色 - 使用皇家蓝
        style.setFillForegroundColor(HEADER_BACKGROUND_COLOR);
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 优化字体样式
        Font font = workbook.createFont();
        font.setColor(HEADER_FONT_COLOR);
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);  // 增大字体
        font.setFontName("Microsoft YaHei UI");  // 使用更现代的字体
        style.setFont(font);

        // 居中对齐
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 现代化边框样式
        setModernBorders(style);

        // 设置行高（通过设置自动换行来增加高度感）
        style.setWrapText(false);

        return style;
    }

    /**
     * 创建数据行样式
     */
    private CellStyle createDataStyle(Workbook workbook, int rowIndex) {
        CellStyle style = workbook.createCellStyle();

        // 统一使用白色背景，去掉斑马纹效果
        style.setFillForegroundColor(DATA_BACKGROUND_COLOR); // 统一使用白色
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 优化数据行字体
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);
        font.setFontName("Microsoft YaHei UI");
        font.setColor(IndexedColors.BLACK.getIndex());
        style.setFont(font);

        // 左对齐，垂直居中
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 轻量级边框
        setLightBorders(style);

        // 设置自动换行
        style.setWrapText(true);

        return style;
    }

    /**
     * 设置现代化边框样式（表头用）
     */
    private void setModernBorders(CellStyle style) {
        // 使用稍粗的边框突出表头
        style.setBorderTop(BorderStyle.MEDIUM);
        style.setBorderBottom(BorderStyle.MEDIUM);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 白色边框让表头更清晰
        style.setTopBorderColor(IndexedColors.WHITE.getIndex());
        style.setBottomBorderColor(IndexedColors.WHITE.getIndex());
        style.setLeftBorderColor(IndexedColors.WHITE.getIndex());
        style.setRightBorderColor(IndexedColors.WHITE.getIndex());
    }

    /**
     * 设置轻量级边框样式（数据行用）
     */
    private void setLightBorders(CellStyle style) {
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 使用浅灰色边框，更subtle
        style.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        style.setBottomBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        style.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        style.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
    }
} 