package com.xt.hsk.module.infra.service.export;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.infra.controller.admin.export.vo.ExportTaskPageReqVO;
import com.xt.hsk.module.infra.dal.dataobject.export.ExportTaskDO;
import com.xt.hsk.module.infra.dal.mysql.export.ExportTaskMapper;
import com.xt.hsk.module.system.api.user.AdminUserApi;
import com.xt.hsk.module.system.api.user.dto.AdminUserRespDTO;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 导出任务服务实现
 *
 * <AUTHOR>
 * @since 2025/05/26
 */
@Service
public class ExportTasksServiceImpl extends
    ServiceImpl<ExportTaskMapper, ExportTaskDO> implements
    ExportTasksService {

    @Resource
    private ExportTaskMapper exportTaskMapper;

    @Resource
    private AdminUserApi adminUserApi;


    public PageResult<ExportTaskDO> getExportTaskPage(ExportTaskPageReqVO pageReqVO) {
        if (StrUtil.isNotBlank(pageReqVO.getCreator())) {
            List<Long> adminUserIds = adminUserApi.getUserListByNickname(pageReqVO.getCreator())
                .stream()
                .map(AdminUserRespDTO::getId).toList();

            if (CollUtil.isEmpty(adminUserIds)) {
                pageReqVO.setCreatorIds(Collections.singletonList(-1L));
            } else {
                pageReqVO.setCreatorIds(adminUserIds);
            }
        }
        return exportTaskMapper.selectPage(pageReqVO);
    }
}
