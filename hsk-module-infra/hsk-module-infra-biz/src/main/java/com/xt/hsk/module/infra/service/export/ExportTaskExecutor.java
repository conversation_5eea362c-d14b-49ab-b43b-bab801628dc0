package com.xt.hsk.module.infra.service.export;

import com.xt.hsk.module.infra.dal.dataobject.export.ExportTaskDO;
import com.xt.hsk.module.infra.enums.export.ExportTaskStatusEnum;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import com.xt.hsk.module.infra.listener.IExportTask;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 导出任务执行器
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Service
@Slf4j
public class ExportTaskExecutor {

    @Resource
    private ExportTasksService exportTasksService;

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 任务类型与任务实现类的映射关系
     */
    private final Map<ExportTaskTypeEnum, IExportTask> exportTaskMap = new ConcurrentHashMap<>();

    /**
     * 任务类型与Bean名称的映射配置
     */
    private static final Map<ExportTaskTypeEnum, String> TASK_BEAN_MAP = new EnumMap<>(
        ExportTaskTypeEnum.class);

    static {
        // 在这里配置任务类型与Bean名称的映射关系
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.TEACHER, "teacherExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.INTERACTIVE_COURSE, "interactiveCourseExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.INTERACTIVE_COURSE_UNIT,
            "interactiveCourseUnitExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.WORD,
                "wordExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.ELITE_COURSE, "eliteCourseExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.USER, "userExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.ELITE_COURSE_USER, "eliteCourseUserExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.ELITE_COURSE_STUDY_STATS, "eliteCourseStudyStatsExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.ELITE_CLASS_HOUR_STUDY_STATS, "eliteClassHourStudyStatsExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.TEACHER_ELITE_COURSE, "teacherEliteCourseExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.ORDER, "orderExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.WORD_MATCHING_EXERCISE, "specialExerciseExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.STROKE_WRITING_EXERCISE, "specialExerciseExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.WORD_TO_SENTENCE_EXERCISE, "specialExerciseExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.KARAOKE_EXERCISE, "specialExerciseExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.WORD_TO_SENTENCE_QUESTION, "wordToSentenceQuestionExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.KARAOKE_QUESTION, "karaokeQuestionExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.EDU_QUESTION, "questionExportTask");
        TASK_BEAN_MAP.put(ExportTaskTypeEnum.USER_EXAM_RECORD, "userExamRecordExportTask");
        // 新增导出类型时，只需要在这里添加一行配置即可
    }

    /**
     * 执行导出任务
     *
     * @param taskId 任务ID
     */
    public void executeExportTask(Long taskId) {
        log.info("开始执行导出任务: taskId={}", taskId);

        ExportTaskDO task = exportTasksService.getById(taskId);
        if (task == null) {
            log.error("导出任务不存在: taskId={}", taskId);
            return;
        }

        if (!ExportTaskStatusEnum.WAITING.getCode().equals(task.getStatus())) {
            log.warn("导出任务状态不正确: taskId={}, status={}", taskId, task.getStatus());
            return;
        }

        ExportTaskTypeEnum taskType = ExportTaskTypeEnum.values()[task.getType() - 1];
        log.info("导出任务类型: {}", taskType);

        IExportTask exportTask = getExportTask(taskType);
        if (exportTask == null) {
            log.error("未找到导出任务实现类: taskType={}", taskType);
            task.setStatus(ExportTaskStatusEnum.FAILED.getCode());
            task.setError("未找到导出任务实现类");
            exportTasksService.updateById(task);
            return;
        }

        try {
            log.info("开始调用导出任务实现类: taskId={}, taskType={}", taskId, taskType);
            exportTask.executeExport(task);
            log.info("导出任务执行完成: taskId={}", taskId);
        } catch (Exception e) {
            log.error("执行导出任务失败: taskId={}, 错误信息: {}", taskId, e.getMessage(), e);
            // 确保任务状态被更新为失败
            try {
                task.setStatus(ExportTaskStatusEnum.FAILED.getCode());
                task.setError("执行导出任务失败: " + e.getMessage());
                exportTasksService.updateById(task);
                log.info("已更新任务状态为失败: taskId={}", taskId);
            } catch (Exception updateException) {
                log.error("更新任务状态失败: taskId={}, 错误: {}", taskId,
                    updateException.getMessage(), updateException);
            }
        }
    }

    /**
     * 获取导出任务实现类
     */
    private IExportTask getExportTask(ExportTaskTypeEnum taskType) {
        return exportTaskMap.computeIfAbsent(taskType, type -> {
            String beanName = TASK_BEAN_MAP.get(type);
            if (beanName == null) {
                log.error("未配置导出任务类型: {}", type);
                return null;
            }

            try {
                return applicationContext.getBean(beanName, IExportTask.class);
            } catch (Exception e) {
                log.error("获取导出任务Bean失败: beanName={}, error={}", beanName, e.getMessage());
                return null;
            }
        });
    }
} 