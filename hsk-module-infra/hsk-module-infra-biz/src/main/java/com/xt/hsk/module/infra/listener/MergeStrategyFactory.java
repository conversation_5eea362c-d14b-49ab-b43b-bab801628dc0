package com.xt.hsk.module.system.listener;

import cn.idev.excel.write.handler.CellWriteHandler;

public class MergeStrategyFactory {

    public static CellWriteHandler createMergeStrategy(Integer businessType) {
        switch (businessType) {
            case 18:
                // 业务A：合并前7列（根据第一列的值）
                return new com.xt.hsk.module.system.listener.QuestionMultiColumnMergeStrategy(0, 0, 1, 2, 3, 4, 5, 6,7,8,9,10,11,12,13,14,15,16);

            default:
                // 默认策略：不合并
                return null;
        }
    }
}
