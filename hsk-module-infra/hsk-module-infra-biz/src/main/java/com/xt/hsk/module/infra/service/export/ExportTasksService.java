package com.xt.hsk.module.infra.service.export;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.infra.controller.admin.export.vo.ExportTaskPageReqVO;
import com.xt.hsk.module.infra.dal.dataobject.export.ExportTaskDO;

/**
 * 导出任务服务
 *
 * <AUTHOR>
 * @since 2025/05/26
 */
public interface ExportTasksService extends IService<ExportTaskDO> {

    /**
     * 分页查询导出任务列表
     *
     * @param pageReqVO 分页查询条件
     * @return 导出任务分页结果
     */
    PageResult<ExportTaskDO> getExportTaskPage(ExportTaskPageReqVO pageReqVO);
}
