package com.xt.hsk.module.infra.listener;

import cn.idev.excel.metadata.Head;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.write.handler.CellWriteHandler;
import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import cn.idev.excel.write.metadata.holder.WriteTableHolder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;

/**
 * Excel列宽处理器 智能调整列宽，提供更好的阅读体验
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
public class ExcelColumnWidthHandler implements CellWriteHandler {

    /**
     * 存储每列的最大宽度
     */
    private final Map<Integer, Integer> maxColumnWidthMap = new HashMap<>();

    /**
     * 最小列宽（字符数）
     */
    private static final int MIN_COLUMN_WIDTH = 12;

    /**
     * 最大列宽（字符数）
     */
    private static final int MAX_COLUMN_WIDTH = 60;

    /**
     * 中文字符宽度因子
     */
    private static final double CHINESE_CHAR_WIDTH_FACTOR = 1.7;

    /**
     * 表头额外宽度
     */
    private static final int HEADER_EXTRA_WIDTH = 4;

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder,
        WriteTableHolder writeTableHolder,
        Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 记录表头宽度，并给表头额外的空间
        if (isHead != null && isHead && head != null) {
            int columnIndex = cell.getColumnIndex();
            String headName = head.getHeadNameList().get(head.getHeadNameList().size() - 1);
            int headWidth = calculateTextWidth(headName) + HEADER_EXTRA_WIDTH;
            updateMaxWidth(columnIndex, headWidth);
        }
    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder,
        WriteTableHolder writeTableHolder,
        WriteCellData<?> cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 记录数据行宽度
        if (isHead == null || !isHead) {
            int columnIndex = cell.getColumnIndex();
            String cellValue = getCellValue(cellData);
            int cellWidth = calculateTextWidth(cellValue);
            updateMaxWidth(columnIndex, cellWidth);
        }
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder,
        WriteTableHolder writeTableHolder,
        List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex,
        Boolean isHead) {
        // 在每次写入完成后调整列宽
        if (relativeRowIndex != null && relativeRowIndex >= 0) {
            // 最后一批数据写入完成后，调整所有列宽
            Sheet sheet = writeSheetHolder.getSheet();
            if (relativeRowIndex == (cellDataList.size() - 1)) {
                adjustColumnWidths(sheet);
            }
        }
    }

    /**
     * 获取单元格值
     */
    private String getCellValue(WriteCellData<?> cellData) {
        if (cellData == null || cellData.getData() == null) {
            return "";
        }
        return cellData.getData().toString();
    }

    /**
     * 计算文本显示宽度
     */
    private int calculateTextWidth(String text) {
        if (text == null || text.isEmpty()) {
            return MIN_COLUMN_WIDTH;
        }

        double width = 0;
        for (char c : text.toCharArray()) {
            if (isChinese(c)) {
                width += CHINESE_CHAR_WIDTH_FACTOR;
            } else if (isWideChar(c)) {
                width += 1.5;
            } else {
                width += 1;
            }
        }

        return (int) Math.ceil(width);
    }

    /**
     * 判断是否为中文字符
     */
    private boolean isChinese(char c) {
        return c >= 0x4e00 && c <= 0x9fff;
    }

    /**
     * 判断是否为全角字符
     */
    private boolean isWideChar(char c) {
        return c >= 0xff00 && c <= 0xffef;
    }

    /**
     * 更新最大宽度
     */
    private void updateMaxWidth(int columnIndex, int width) {
        int adjustedWidth = Math.max(MIN_COLUMN_WIDTH, Math.min(MAX_COLUMN_WIDTH, width));
        maxColumnWidthMap.merge(columnIndex, adjustedWidth, Math::max);
    }

    /**
     * 调整所有列的宽度
     */
    private void adjustColumnWidths(Sheet sheet) {
        for (Map.Entry<Integer, Integer> entry : maxColumnWidthMap.entrySet()) {
            int columnIndex = entry.getKey();
            int width = entry.getValue();
            // Excel列宽单位是1/256字符宽度，增加一些padding
            int excelWidth = (width + 2) * 256;
            sheet.setColumnWidth(columnIndex, excelWidth);
        }
    }

    /**
     * 最终调整列宽（在所有数据写入完成后调用）
     */
    public void finalizeColumnWidths(Sheet sheet) {
        adjustColumnWidths(sheet);
    }
} 