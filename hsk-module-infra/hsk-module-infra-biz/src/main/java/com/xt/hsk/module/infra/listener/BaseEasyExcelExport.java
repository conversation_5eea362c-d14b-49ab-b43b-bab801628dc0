package com.xt.hsk.module.infra.listener;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.converters.longconverter.LongStringConverter;
import cn.idev.excel.write.builder.ExcelWriterBuilder;
import cn.idev.excel.write.handler.CellWriteHandler;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.xt.hsk.module.infra.dal.dataobject.export.ExportTaskDO;
import com.xt.hsk.module.infra.service.file.FileService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Excel数据导出基础模板
 * 支持大数据量分批导出和多云厂商存储
 *
 * <AUTHOR>
 * @since 2025/05/26
 */
@Slf4j
@Component
public abstract class BaseEasyExcelExport<T> {


    protected final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Resource
    private FileService fileService;

    /**
     * 分页配置
     */
    private static class PaginationConfig {

        /**
         * 每个Sheet最大行数
         */
        private final long maxRowsPerSheet;
        /**
         * 每次写入行数
         */
        private final long rowsPerWrite;
        /**
         * 总数据量
         */
        private final long totalCount;

        public PaginationConfig(long totalCount, long maxRowsPerSheet, long rowsPerWrite) {
            this.totalCount = totalCount;
            this.maxRowsPerSheet = maxRowsPerSheet;
            this.rowsPerWrite = rowsPerWrite;
        }

        /**
         * 计算Sheet数量
         */
        public int getSheetCount() {
            if (totalCount == 0) {
                return 1;
            }
            return (int) Math.ceil((double) totalCount / maxRowsPerSheet);
        }

        /**
         * 计算指定Sheet的数据量
         */
        public long getSheetDataCount(int sheetIndex) {
            if (totalCount == 0) {
                return 0;
            }

            long remainingCount = totalCount - (long) sheetIndex * maxRowsPerSheet;
            return Math.min(remainingCount, maxRowsPerSheet);
        }

        /**
         * 计算指定Sheet需要写入的次数
         */
        public int getWriteCount(int sheetIndex) {
            long sheetDataCount = getSheetDataCount(sheetIndex);
            if (sheetDataCount == 0) {
                return 0;
            }

            return (int) Math.ceil((double) sheetDataCount / rowsPerWrite);
        }

        /**
         * 计算分页参数
         */
        public long getPageNo(int sheetIndex, int writeIndex) {
            return sheetIndex * (maxRowsPerSheet / rowsPerWrite) + writeIndex + 1;
        }
    }

    /**
     * 直接导出到HTTP响应
     */
    public void exportToResponse(HttpServletResponse response, String fileName,
        Map<String, Object> queryCondition, ExportTaskDO taskDO) {
        try {
            setupResponseHeaders(response, fileName);

            PaginationConfig config = createPaginationConfig(queryCondition);
            List<List<String>> headers = getExcelHead();

            try (OutputStream outputStream = response.getOutputStream()) {
                writeExcelData(outputStream, fileName, headers, queryCondition, config,taskDO);
                outputStream.flush();
            }

        } catch (Exception e) {
            log.error("导出Excel到响应失败: fileName={}, error={}", fileName, e.getMessage(), e);
            throw new ServiceException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 导出并上传到云存储
     */
    public String uploadExcelToCloudStorage(String fileName, Map<String, Object> queryCondition, ExportTaskDO taskDO) throws IOException{
        String timestampedFileName = addTimestamp(fileName);

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            PaginationConfig config = createPaginationConfig(queryCondition);
            List<List<String>> headers = getExcelHead();

            writeExcelData(outputStream, timestampedFileName, headers, queryCondition, config,taskDO);
            outputStream.flush();

            return uploadToCloudStorage(outputStream.toByteArray(), timestampedFileName + ".xlsx");

        } catch (Exception e) {
            log.error("生成并上传Excel文件失败: fileName={}, error={}", fileName, e.getMessage(),
                e);
            throw e;
        }
    }

    /**
     * 写入Excel数据
     */
    private void writeExcelData(OutputStream outputStream, String fileName,
        List<List<String>> headers, Map<String, Object> queryCondition,
        PaginationConfig config,ExportTaskDO taskDO) {

        log.info("开始写入Excel数据，文件名：{}，总数据量：{}", fileName, config.totalCount);

        int sheetCount = config.getSheetCount();
        log.info("计算得出Sheet数量：{}", sheetCount);
        CellWriteHandler mergeStrategy = com.xt.hsk.module.system.listener.MergeStrategyFactory.createMergeStrategy(taskDO.getType());
        // 创建Excel写入器
        ExcelColumnWidthHandler columnWidthHandler = new ExcelColumnWidthHandler();
        ExcelWriterBuilder writerBuilder = EasyExcel.write(outputStream)
            .registerWriteHandler(new ExcelStyleHandler())
            .registerWriteHandler(columnWidthHandler)
            .registerConverter(new LongStringConverter());
        if(mergeStrategy!=null){
            writerBuilder.registerWriteHandler(mergeStrategy);
        }
        var excelWriter = writerBuilder.build();
        try {
            for (int sheetIndex = 0; sheetIndex < sheetCount; sheetIndex++) {
                try {
                    log.info("开始处理第{}个Sheet", sheetIndex + 1);
                    String sheetName = generateSheetName(fileName, sheetIndex, sheetCount);

                    // 创建Sheet写入器
                    var writeSheet = EasyExcel.writerSheet(sheetIndex, sheetName)
                        .head(headers)
                        .build();

                    int writeCount = config.getWriteCount(sheetIndex);
                    log.info("Sheet {} 需要写入 {} 次", sheetIndex + 1, writeCount);

                    for (int writeIndex = 0; writeIndex < writeCount; writeIndex++) {
                        try {
                            log.info("开始第{}次写入数据到Sheet {}", writeIndex + 1,
                                sheetIndex + 1);
                            List<List<String>> dataList = loadSheetData(queryCondition, config,
                                sheetIndex, writeIndex);
                            log.info("加载到{}条数据", dataList.size());
                            if (writeIndex == 0) {
                                // 第一次写入包含表头
                                excelWriter.write(dataList, writeSheet);
                            } else {
                                // 后续写入不包含表头
                                excelWriter.write(dataList, writeSheet);
                            }
                            log.info("第{}次写入完成", writeIndex + 1);
                        } catch (Exception e) {
                            log.error("写入数据到Sheet {}第{}次时出错：{}", sheetIndex + 1,
                                writeIndex + 1, e.getMessage(), e);
                            throw e; // 重新抛出异常
                        }
                    }
                    log.info("Sheet {} 处理完成", sheetIndex + 1);
                } catch (Exception e) {
                    log.error("处理Sheet {}时出错：{}", sheetIndex + 1, e.getMessage(), e);
                    throw e; // 重新抛出异常
                }
            }
            log.info("所有Sheet处理完成");
        } catch (Exception e) {
            log.error("写入Excel数据时出错：{}", e.getMessage(), e);
            throw new ServiceException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(),
                "写入Excel数据失败：" + e.getMessage());
        } finally {
            try {
                excelWriter.finish();
                log.info("Excel写入器关闭完成");
            } catch (Exception e) {
                log.error("关闭Excel写入器时出错：{}", e.getMessage(), e);
            }
        }
    }

    /**
     * 加载单次写入的数据
     */
    private List<List<String>> loadSheetData(Map<String, Object> queryCondition,
        PaginationConfig config, int sheetIndex, int writeIndex) {
        try {
            List<List<String>> dataList = new ArrayList<>();
            long pageNo = config.getPageNo(sheetIndex, writeIndex);
            long pageSize = config.rowsPerWrite;

            log.info("开始加载数据，页码：{}，页面大小：{}", pageNo, pageSize);
            buildDataList(dataList, queryCondition, pageNo, pageSize);
            log.info("数据加载完成，实际加载{}条", dataList.size());

            return dataList;
        } catch (Exception e) {
            log.error("加载Sheet数据时出错，sheetIndex：{}，writeIndex：{}，错误：{}",
                sheetIndex, writeIndex, e.getMessage(), e);
            throw new ServiceException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(),
                "加载数据失败：" + e.getMessage());
        }
    }

    /**
     * 创建分页配置
     */
    private PaginationConfig createPaginationConfig(Map<String, Object> queryCondition) {
        long totalCount = dataTotalCount(queryCondition);
        long maxRowsPerSheet = eachSheetTotalCount();
        long rowsPerWrite = eachTimesWriteSheetTotalCount();

        return new PaginationConfig(totalCount, maxRowsPerSheet, rowsPerWrite);
    }

    /**
     * 生成Sheet名称
     */
    private String generateSheetName(String fileName, int sheetIndex, int totalSheets) {
        return totalSheets == 1 ? fileName : fileName + "_" + (sheetIndex + 1);
    }

    /**
     * 添加时间戳到文件名
     */
    private String addTimestamp(String fileName) {
        return fileName + "_" + DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN);
    }

    /**
     * 设置响应头
     */
    private void setupResponseHeaders(HttpServletResponse response, String fileName)
        throws Exception {
        response.setHeader("Content-Disposition", "attachment;filename="
            + new String(fileName.getBytes("gb2312"), "ISO-8859-1") + ".xlsx");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
    }

    /**
     * 上传到云存储
     */
    private String uploadToCloudStorage(byte[] fileData, String fileName) {
        try {
            // 获取文件类型
            return fileService.createFile(fileData, fileName,
                null, null);
        } catch (Exception e) {
            log.error("上传文件到云存储失败: fileName={}, error={}", fileName, e.getMessage(), e);
            throw new ServiceException(GlobalErrorCodeConstants.OSS_FILE_UPLOAD_ERROR);
        }
    }

    // ================================= 抽象方法 =================================

    /**
     * 获取Excel表头
     */
    protected abstract List<List<String>> getExcelHead();

    /**
     * 统计总数据量
     */
    protected abstract Long dataTotalCount(Map<String, Object> conditions);

    /**
     * 每个Sheet的最大数据量
     */
    protected Long eachSheetTotalCount() {
        return 5000L;
    }

    ;

    /**
     * 每次写入的数据量
     */
    protected Long eachTimesWriteSheetTotalCount() {
        return 1000L;
    }

    ;

    /**
     * 构建数据列表
     */
    protected abstract void buildDataList(List<List<String>> resultList,
        Map<String, Object> queryCondition,
                                        Long pageNo, Long pageSize);
}
