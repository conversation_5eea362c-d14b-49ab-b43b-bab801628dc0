package com.xt.hsk.framework.locale;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Locale;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.LocaleResolver;

/**
 * 自定义区域解析程序
 * <p>
 * 请求头与配置文件对应关系：
 * 1. 请求头: language:zh-Hans -> 使用配置文件: messages.properties (中文简体)
 * 2. 请求头: language:en -> 使用配置文件: messages_en.properties 
 * 3. 请求头: language:zh-CN 或 language:zh_CN -> 使用配置文件: messages_zh_CN.properties 
 * 4. 请求头: language:vi -> 使用配置文件: messages_vi.properties 
 * 5. 未指定请求头或请求头无效 -> 使用默认配置文件: messages.properties (中文简体)
 *
 * <AUTHOR>
 * @since 2025/06/16
 */
@Slf4j
public class CustomLocaleResolver implements LocaleResolver {

    /**
     * 语言请求头名称 固定使用 "language" 作为请求头名称 
     * 例如: language:zh-Hans, language:en, language:zh-CN, language:vi
     */
    private static final String LANGUAGE_HEADER = "language";

    /**
     * 默认语言为中文简体 
     * 当请求头不存在或无效时使用此默认值
     */
    private static final Locale DEFAULT_LOCALE = Locale.forLanguageTag("zh-Hans");

    /**
     * 解析区域设置 根据请求头 "language" 的值确定使用哪个语言配置文件
     *
     * @param request HTTP请求
     * @return Locale 对象，表示要使用的语言
     */
    @Override
    public Locale resolveLocale(HttpServletRequest request) {

        // 从请求头获取语言标识
        String languageHeader = request.getHeader(LANGUAGE_HEADER);

        // 如果请求头不存在，使用默认语言
        if (!StringUtils.hasText(languageHeader)) {
            log.info("【国际化】未找到语言请求头，使用默认语言: {}", DEFAULT_LOCALE);
            return DEFAULT_LOCALE;
        }

        // 解析语言标识 (支持格式: zh-Hans, en, zh-CN, vi 等)
        try {
            // 将下划线替换为连字符，确保符合 Locale.forLanguageTag 的格式要求
            // 例如: zh_CN -> zh-CN
            return Locale.forLanguageTag(languageHeader.replace("_", "-"));
        } catch (Exception e) {
            log.warn("【国际化】语言请求头解析失败: {}, 使用默认语言: {}", languageHeader, DEFAULT_LOCALE);
            return DEFAULT_LOCALE;
        }
    }

    @Override
    public void setLocale(HttpServletRequest request,
        HttpServletResponse response,
        Locale locale) {
        // 不需要实现（只读解析器）
        throw new UnsupportedOperationException("Cannot change locale - use Language header");
    }
}
