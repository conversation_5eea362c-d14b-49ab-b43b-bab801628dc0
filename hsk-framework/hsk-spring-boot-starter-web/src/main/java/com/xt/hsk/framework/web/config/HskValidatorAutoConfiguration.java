package com.xt.hsk.framework.web.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

/**
 * 验证器自动配置类 支持国际化的参数校验消息
 * 确保在Spring Boot的验证配置前执行
 * <AUTHOR>
 * @since 2025/06/16
 */
@Slf4j
@AutoConfiguration(before = ValidationAutoConfiguration.class)
public class HskValidatorAutoConfiguration {

    @Bean
    public MessageSource validationMessageSource() {
        log.info("【国际化】创建参数校验国际化消息源");
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setBasename("classpath:i18n/validation_messages");
        messageSource.setDefaultEncoding("UTF-8");
        return messageSource;
    }

    @Bean
    @Primary
    public LocalValidatorFactoryBean getValidator(MessageSource validationMessageSource) {  // 修改方法名
        log.info("【国际化】创建支持国际化的验证器工厂");
        LocalValidatorFactoryBean factoryBean = new LocalValidatorFactoryBean();
        factoryBean.setValidationMessageSource(validationMessageSource);
        return factoryBean;
    }
}