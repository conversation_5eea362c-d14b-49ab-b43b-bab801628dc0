package com.xt.hsk.framework.locale;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.web.servlet.LocaleResolver;

/**
 * locale 配置 AutoConfiguration 确保在 WebMvcAutoConfiguration 之前初始化
 *
 * <AUTHOR>
 * @since 2025/06/16
 */
@Slf4j
@AutoConfiguration(before = WebMvcAutoConfiguration.class)
public class LocaleConfig {

    @PostConstruct
    public void init() {
        log.info("【国际化】LocaleConfig 初始化");
    }

    /**
     * 区域设置解析器 Primary 确保这个 Bean 优先于其他同类型的 Bean 被注入
     *
     * @return {@code LocaleResolver }
     */
    @Bean("localeResolver")
    @Primary
    public LocaleResolver localeResolver() {
        log.info("【国际化】创建 CustomLocaleResolver Bean");
        return new CustomLocaleResolver();
    }
}
