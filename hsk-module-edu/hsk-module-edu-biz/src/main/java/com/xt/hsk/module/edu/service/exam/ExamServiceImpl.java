package com.xt.hsk.module.edu.service.exam;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.constants.RedisKeyPrefix;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPageReqVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.ExamAppPageReqVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.ExamAppPageRespVO;
import com.xt.hsk.module.edu.convert.exam.ExamConvert;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDO;
import com.xt.hsk.module.edu.dal.mysql.exam.ExamMapper;
import com.xt.hsk.module.edu.enums.exam.ExamPublishStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;


/**
 * 模考 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class ExamServiceImpl extends ServiceImpl<ExamMapper, ExamDO> implements ExamService {

    @Resource
    private ExamMapper examMapper;

    @Override
    public PageResult<ExamDO> selectPage(ExamPageReqVO pageReqVO) {

        return examMapper.selectPage(pageReqVO);
    }

    /**
     * 根据模考ID获取模考
     *
     * @param id 模考ID
     * @return 模考DO
     */
    @Override
    public ExamDO getExam(Long id) {
        ExamDO examDO = this.getById(id);
        if (examDO == null) {
            throw exception(EXAM_NOT_EXISTS);
        }
        return examDO;
    }

    /**
     * 获取下一个排序
     *
     * @param hskLevel HSK 级别
     * @return 排序
     */
    @Override
    public Integer getNextSort(Integer hskLevel) {
        ExamDO examDO = this.lambdaQuery()
                .eq(ExamDO::getHskLevel, hskLevel)
                .orderByDesc(ExamDO::getSort)
                .last("LIMIT 1")
                .one();

        return examDO == null ? 1 : examDO.getSort() + 1;
    }

    /**
     * 更新排序
     *
     * @param oldHskLevel 旧 HSK 级别
     * @param newHskLevel 新 HSK 级别
     * @param sort        排序
     */
    @Override
    public void updateSort(Integer oldHskLevel, Integer newHskLevel, Integer sort) {

        if (newHskLevel == null || oldHskLevel == null || sort == null) {
            return;
        }

        if (Objects.equals(newHskLevel, oldHskLevel)) {
            return;
        }

        this.lambdaUpdate()
                .eq(ExamDO::getHskLevel, newHskLevel)
                .ge(ExamDO::getSort, sort)
                .setSql("sort = sort + 1")
                .set(ExamDO::getUpdateTime, LocalDateTime.now())
                .set(ExamDO::getUpdater, WebFrameworkUtils.getLoginUserId())
                .update();
    }

    /**
     * 分页获取App模考列表
     *
     * @param reqVO 请求参数
     * @return 模考列表
     */
    @Override
    public PageResult<ExamAppPageRespVO> getAppExamPage(ExamAppPageReqVO reqVO) {

        if (reqVO.getUserId() == null || ExamTypeEnum.FULL_REAL.getCode().equals(reqVO.getType())) {
            // 构建查询参数
            ExamPageReqVO pageReqVO = new ExamPageReqVO();
            pageReqVO.setPageNo(reqVO.getPageNo());
            pageReqVO.setPageSize(reqVO.getPageSize());
            pageReqVO.setHskLevel(reqVO.getHskLevel());
            pageReqVO.setType(reqVO.getType());
            pageReqVO.setPublishStatus(ExamPublishStatusEnum.PUBLISHED.getCode());

            // 执行查询
            PageResult<ExamDO> page = this.selectPage(pageReqVO);

            // 转换并返回结果
            List<ExamDO> pageList = page.getList();
            if (CollUtil.isEmpty(pageList)) {
                return PageResult.empty();
            }

            List<ExamAppPageRespVO> voList = ExamConvert.INSTANCE.toAppPageRespVoList(pageList);
            return new PageResult<>(voList, page.getTotal());
        }

        IPage<ExamAppPageRespVO> page = examMapper.getAppExamPage(new Page<>(reqVO.getPageNo(), reqVO.getPageSize()), reqVO);

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Cacheable(value = RedisKeyPrefix.EXAM_AVAILABILITY,
        key = "#hskLevel",
        unless = "#hskLevel == null")
    @Override
    public Map<String, Boolean> getExamAvailability(Integer hskLevel) {
        if (hskLevel == null) {
            return Map.of();
        }
        // 遍历所有模考类型，检查是否存在可用的模考
        Map<String, Boolean> result = new HashMap<>(ExamTypeEnum.values().length);

        Map<Integer, List<ExamDO>> examTypeMap = this.lambdaQuery()
            .select(ExamDO::getId, ExamDO::getType)
            .eq(ExamDO::getHskLevel, hskLevel)
            .eq(ExamDO::getPublishStatus, ExamPublishStatusEnum.PUBLISHED.getCode())
            .list()
            .stream()
            .collect(Collectors.groupingBy(ExamDO::getType));

        for (ExamTypeEnum typeEnum : ExamTypeEnum.values()) {
            if (examTypeMap.containsKey(typeEnum.getCode())) {
                result.put(String.valueOf(typeEnum.getCode()), true);
            }
        }
        return result;
    }
}