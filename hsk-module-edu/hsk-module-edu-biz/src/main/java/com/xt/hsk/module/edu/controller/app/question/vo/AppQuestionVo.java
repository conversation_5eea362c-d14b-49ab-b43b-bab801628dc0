package com.xt.hsk.module.edu.controller.app.question.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.module.edu.dal.dataobject.question.questiontype.QuestionTypeDO;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class AppQuestionVo implements java.io.Serializable, VO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 题目编码 #100000开始
     */
    private String questionCode;
//    /**
//     * 教材分类 考场真题，模拟题
//     */
//    @Trans(type = TransType.ENUM, key = "code", target = TextbookTypeEnum.class, ref = "textbookTypeDesc")
//    private Integer textbookType;
//    /**
//     * 教材分类 考场真题，模拟题
//     */
//    private String textbookTypeDesc;
    /**
     * HSK等级
     */
    @Trans(type = TransType.ENUM, key = "code", target = HskEnum.class, ref = "hskLevelDesc")
    private Integer hskLevel;
    /**
     * hsk等级
     */
    private String hskLevelDesc;


//    /**
//     * 教材ID
//     */
//    @Trans(type = TransType.SIMPLE, target = TextbookDO.class, fields = "nameCn", ref = "textbookName")
//    private Long textbookId;
//    /**
//     * 教材名
//     */
//    private String textbookName;

//
//    /**
//     * 章节ID
//     */
//    @Trans(type = TransType.SIMPLE, target = ChapterDO.class, fields = "chapterNameCn", ref = "chapterName")
//    private Long chapterId;
//    /**
//     * 章节名
//     */
//    private String chapterName;
//
//
//    /**
//     * 单元ID
//     */
//    @Trans(type = TransType.SIMPLE, target = UnitDO.class, fields = "unitNameCn", ref = "unitName")
//    private Long unitId;
//    /**
//     * 单元名
//     */
//    private String unitName;
//

    /**
     * 题型ID
     */
    @Trans(type = TransType.SIMPLE, target = QuestionTypeDO.class, fields = "nameCn", ref = "questionTypeDesc")
    private Long typeId;
    /**
     * 题型
     */
    private String questionTypeDesc;


    /**
     * 科目
     */
    @Trans(type = TransType.ENUM, key = "code", target = SubjectEnum.class, ref = "subjectDesc")
    private Integer subject;
    /**
     * 科目
     */
    private String subjectDesc;


    /**
     * 材料音频
     */
    private String materialAudio;
    /**
     * 材料音频
     */
    private String materialAudioContent;

    /**
     * 材料图片
     */
    private String materialImage;


    /**
     * 材料文字
     */
    private String materialContent;


    /**
     * 题目选项 json
     */
    private String options;
    /**
     * 材料选项
     */
    private List<AppOptionContentVO> optionContents;


    /**
     * 小题数量
     */
    private Integer questionNum;


    /**
     * 状态 0开启 1关闭
     */
//    @Trans(type = TransType.ENUM, key = "code", target = CommonStatusEnum.class, ref = "statusDesc")
    private Integer status;
    /**
     * 状态 0开启 1关闭
     */
    private String statusDesc;
    /**
     * 创建人
     */
    @Trans(type = TransType.SIMPLE, fields = "nickname", targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", ref = "creatorName")
    private String creator;


    /**
     * 是否展示 1展示 0不展示
     */
    @Trans(type = TransType.ENUM, key = "code", target = IsShowEnum.class, ref = "isShowDesc")
    private Integer isShow;
    /**
     * 是否展示 1展示 0不展示
     */
    private String isShowDesc;


    /**
     * 版本号
     */
    private Integer version;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 创建人
     */

    private String creatorName;

//    /**
//     * 引用次数
//     */
//    private Integer referenceCount;
//    /**
//     * 正确率
//     */
//    private Integer correctRate;
//    /**
//     * 正确作答次数
//     */
//    private Integer correctAnswerCount;

//    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "updaterName")
//    private String updater;
//
//    /**
//     * 是否展示 1展示 0不展示
//     */
//    private String updaterName;
//    /**
//     * /**
//     * 总作答次数
//     */
//    private Integer totalAnswerCount;

    /**
     * 题目详情
     */
    private List<AppQuestionDetailVO> questionDetailList;
    /**
     * 下一题id
     */
    private Long nextQuestionId;
    /**
     * 题目作答记录id
     */
    private Long questionRecordId;

}
