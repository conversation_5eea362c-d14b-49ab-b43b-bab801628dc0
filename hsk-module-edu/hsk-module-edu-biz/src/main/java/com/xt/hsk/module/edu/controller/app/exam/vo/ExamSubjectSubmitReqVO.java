package com.xt.hsk.module.edu.controller.app.exam.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 模考科目提交 req vo
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Data
public class ExamSubjectSubmitReqVO {

    /**
     * 模考记录ID
     */
    @NotNull(message = "模考记录ID不能为空")
    private Long examRecordId;

    /**
     * 科目 1-听力 2-阅读 4-书写
     */
    @NotNull(message = "科目不能为空")
    private Integer subject;

}