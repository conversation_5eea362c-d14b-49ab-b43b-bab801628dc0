package com.xt.hsk.module.edu.service.question.questionversion;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.question.questionversion.vo.QuestionVersionPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questionversion.QuestionVersionDO;
import com.xt.hsk.module.edu.dal.mysql.questionversion.QuestionVersionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 题目表版本库 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionVersionServiceImpl extends ServiceImpl<QuestionVersionMapper, QuestionVersionDO> implements QuestionVersionService {

    @Resource
    private QuestionVersionMapper questionVersionMapper;

    @Override
    public PageResult<QuestionVersionDO> selectPage(QuestionVersionPageReqVO pageReqVO) {

        return questionVersionMapper.selectPage(pageReqVO);
    }

    @Override
    public QuestionVersionDO getQuestionByIdAndVersion(Long questionId, Integer version) {

        return questionVersionMapper.getQuestionByIdAndVersion(questionId, version);
    }

}