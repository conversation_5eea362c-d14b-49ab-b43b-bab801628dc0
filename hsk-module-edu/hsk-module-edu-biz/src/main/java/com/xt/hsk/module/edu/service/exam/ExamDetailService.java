package com.xt.hsk.module.edu.service.exam;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamDetailPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRefCountDto;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDetailDO;
import java.util.Collection;
import java.util.List;

/**
 * 模考详情 Service 接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface ExamDetailService extends IService<ExamDetailDO> {

    PageResult<ExamDetailDO> selectPage(ExamDetailPageReqVO pageReqVO);

    long countQuestionQuote(List<Long> questionIds);

    List<QuestionRefCountDto> getExamQuestionQuoteCount(Collection<Long> questionIds);
}