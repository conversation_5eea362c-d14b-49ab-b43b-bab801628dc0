package com.xt.hsk.module.edu.service.elitecourse;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseVideoPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseVideoDO;
import com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteCourseVideoMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 精品课-视频 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
@Validated
public class EliteCourseVideoServiceImpl extends ServiceImpl<EliteCourseVideoMapper, EliteCourseVideoDO> implements EliteCourseVideoService {

    @Resource
    private EliteCourseVideoMapper eliteCourseVideoMapper;

    @Override
    public PageResult<EliteCourseVideoDO> selectPage(EliteCourseVideoPageReqVO pageReqVO) {

        return eliteCourseVideoMapper.selectPage(pageReqVO);
    }

}