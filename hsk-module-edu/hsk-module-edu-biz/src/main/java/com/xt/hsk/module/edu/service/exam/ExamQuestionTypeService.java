package com.xt.hsk.module.edu.service.exam;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamQuestionTypeDO;
import jakarta.validation.Valid;

/**
 * 模考题型 Service 接口
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
public interface ExamQuestionTypeService extends IService<ExamQuestionTypeDO> {
    PageResult<ExamQuestionTypeDO> selectPage(@Valid ExamQuestionTypePageReqVO pageReqVO);

}