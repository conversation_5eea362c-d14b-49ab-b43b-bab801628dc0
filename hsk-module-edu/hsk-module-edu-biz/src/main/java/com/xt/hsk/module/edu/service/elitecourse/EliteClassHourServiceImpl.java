package com.xt.hsk.module.edu.service.elitecourse;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteChapterDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteClassHourDO;
import com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteClassHourMapper;
import com.xt.hsk.module.edu.manager.elitecourse.dto.CourseHourCountDto;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_CLASS_HOUR_NOT_EXISTS;


/**
 * 精品课课时 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
@Validated
public class EliteClassHourServiceImpl extends ServiceImpl<EliteClassHourMapper, EliteClassHourDO> implements EliteClassHourService {

    @Resource
    private EliteClassHourMapper eliteClassHourMapper;

    @Override
    public PageResult<EliteClassHourDO> selectPage(EliteClassHourPageReqVO pageReqVO) {

        return eliteClassHourMapper.selectPage(pageReqVO);
    }

    /**
     * 根据id获得精品课课时
     *
     * @param id 精品课课时id
     * @return 精品课课时DO
     */
    @Override
    public EliteClassHourDO getEliteClassHour(Long id) {
        EliteClassHourDO eliteClassHourDO = getById(id);
        if (eliteClassHourDO == null) {
            throw exception(ELITE_CLASS_HOUR_NOT_EXISTS);
        }
        return eliteClassHourDO;
    }

    /**
     * 分页获取精品课时
     * <p>
     * 先按章节排序升序，再按课时排序升序
     *
     * @param pageReqVO 查询条件
     * @return 精品课时
     */
    @Override
    public PageResult<EliteClassHourDO> getClassHourPage(EliteClassHourPageReqVO pageReqVO) {

        MPJLambdaWrapper<EliteClassHourDO> wrapper = new MPJLambdaWrapperX<EliteClassHourDO>()
                .leftJoin(EliteChapterDO.class, EliteChapterDO::getId, EliteClassHourDO::getChapterId)
                .eq(EliteClassHourDO::getCourseId, pageReqVO.getCourseId())
                .eq(EliteClassHourDO::getDeleted, false)
                .eq(EliteChapterDO::getDeleted, false)
                .orderByAsc(EliteChapterDO::getSort)
                .orderByAsc(EliteClassHourDO::getSort);


        return eliteClassHourMapper.selectJoinPage(pageReqVO, EliteClassHourDO.class, wrapper);

    }

    @Override
    public List<CourseHourCountDto> getEliteClassHourCount(List<Long> courseIds) {

        return eliteClassHourMapper.getEliteClassHourCount(courseIds);
    }
}