package com.xt.hsk.module.edu.service.userquestionanswerrecord.userquestionanswerdata;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.userquestionanswerdata.vo.UserQuestionAnswerDataPageReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerdata.UserQuestionAnswerDataDO;

import com.xt.hsk.module.edu.dal.mysql.userquestionanswerdata.UserQuestionAnswerDataMapper;


/**
 * 用户题目作答数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserQuestionAnswerDataServiceImpl extends ServiceImpl<UserQuestionAnswerDataMapper, UserQuestionAnswerDataDO> implements UserQuestionAnswerDataService {

    @Resource
    private UserQuestionAnswerDataMapper userQuestionAnswerDataMapper;

    @Override
    public PageResult<UserQuestionAnswerDataDO> selectPage(UserQuestionAnswerDataPageReqVO pageReqVO) {

        return userQuestionAnswerDataMapper.selectPage(pageReqVO);
    }

}