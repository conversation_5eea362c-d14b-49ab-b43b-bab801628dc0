package com.xt.hsk.module.edu.controller.admin.exam;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamDetailReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamDetailSubjectRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPageRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDO;
import com.xt.hsk.module.edu.manager.exam.admin.ExamAdminManager;
import com.xt.hsk.module.edu.manager.exam.admin.ExamCommandManager;
import com.xt.hsk.module.edu.manager.exam.admin.ExamComposeManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 模考 后台 控制器
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Validated
@RestController
@RequestMapping("/edu/exam")
public class ExamAdminController {

    @Resource
    private ExamAdminManager examAdminManager;

    @Resource
    private ExamCommandManager examCommandManager;

    @Resource
    private ExamComposeManager examComposeManager;

    /**
     * 创建模考
     */
    @PostMapping("/create")
    @LogRecord(type = LogRecordType.EXAM,
        subType = "创建模考", bizNo = "{{#examId}}",
        success = "创建模考：【{{#exam.name}}】，HSK等级：{{#exam.hskLevel}}")
    @PreAuthorize("@ss.hasPermission('edu:exam:create')")
    public CommonResult<Long> createExam(@Valid @RequestBody ExamSaveReqVO createReqVO) {
        return success(examCommandManager.createExam(createReqVO));
    }

    /**
     * 更新模考
     */
    @PutMapping("/update")
    @LogRecord(type = LogRecordType.EXAM, subType = "修改模考",
        bizNo = "{{#updateReqVO.id}}",
        success = "修改模考：【{{#exam.name}}】，HSK等级：{{#exam.hskLevel}}")
    @PreAuthorize("@ss.hasPermission('edu:exam:update')")
    public CommonResult<Boolean> updateExam(@Valid @RequestBody ExamSaveReqVO updateReqVO) {
        examCommandManager.updateExam(updateReqVO);
        return success(true);
    }

    /**
     * 删除模考
     */
    @DeleteMapping("/delete")
    @LogRecord(type = LogRecordType.EXAM,
        subType = "删除模考", bizNo = "{{#id}}",
        success = "删除模考：【{{#exam.name}}】，"
            + "HSK等级：{{#exam.hskLevel}}")
    @PreAuthorize("@ss.hasPermission('edu:exam:delete')")
    public CommonResult<Boolean> deleteExam(@RequestParam("id") Long id) {
        examAdminManager.deleteExam(id);
        return success(true);
    }

    /**
     * 根据id获取模考
     */
    @PostMapping("/get")
    @PreAuthorize("@ss.hasPermission('edu:exam:query')")
    public CommonResult<ExamRespVO> getExam(@RequestParam("id") Long id) {
        ExamDO exam = examAdminManager.getExam(id);
        return success(BeanUtils.toBean(exam, ExamRespVO.class));
    }

    /**
     * 分页获取模考
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:exam:query')")
    public CommonResult<PageResult<ExamPageRespVO>> getExamPage(@Valid @RequestBody ExamPageReqVO pageReqVO) {
        return success(examAdminManager.getExamPage(pageReqVO));
    }

    /**
     * 更新模考发布状态
     */
    @PutMapping("/publish-status")
    @LogRecord(type = LogRecordType.EXAM,
        subType = "更新模考发布状态", bizNo = "{{#id}}",
        success = "{{#statusText}}模考：【{{#exam.name}}】")
    @PreAuthorize("@ss.hasPermission('edu:exam:update')")
    public CommonResult<Boolean> updateExamPublishStatus(@RequestParam("id") Long id, @RequestParam("targetStatus") Integer targetStatus) {
        examAdminManager.updateExamPublishStatus(id, targetStatus);
        return success(true);
    }

    /**
     * 获取模考详情科目列表
     */
    @PostMapping("/subject-list")
    @PreAuthorize("@ss.hasPermission('edu:exam:query')")
    public CommonResult<List<ExamDetailSubjectRespVO>> getExamDetailSubjectList(@RequestBody ExamDetailReqVO reqVO) {
        return success(examComposeManager.getExamDetailSubjectList(reqVO.getExamId(), reqVO.getPaperRuleId()));
    }

    /**
     * 组卷
     */
    @PostMapping("/random-compose")
    @PreAuthorize("@ss.hasPermission('edu:exam:query')")
    public CommonResult<List<ExamDetailSubjectRespVO>> randomCompose(@RequestBody ExamDetailReqVO reqVO) {
        return success(examComposeManager.randomCompose(reqVO.getPaperRuleId()));
    }

}