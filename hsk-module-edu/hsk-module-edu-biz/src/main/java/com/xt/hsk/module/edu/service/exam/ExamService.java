package com.xt.hsk.module.edu.service.exam;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPageReqVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.ExamAppPageReqVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.ExamAppPageRespVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDO;
import jakarta.validation.Valid;
import java.util.Map;

/**
 * 模考 Service 接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface ExamService extends IService<ExamDO> {

    PageResult<ExamDO> selectPage(@Valid ExamPageReqVO pageReqVO);

    /**
     * 根据模考ID获取模考
     *
     * @param id 模考ID
     * @return 模考DO
     */
    ExamDO getExam(Long id);

    /**
     * 获取下一个排序
     *
     * @param hskLevel HSK 级别
     * @return 排序
     */
    Integer getNextSort(Integer hskLevel);

    /**
     * 更新排序
     *
     * @param oldHskLevel 旧 HSK 级别
     * @param newHskLevel 新 HSK 级别
     * @param sort        排序
     */
    void updateSort(Integer oldHskLevel, Integer newHskLevel, Integer sort);

    /**
     * 分页获取App模考列表
     *
     * @param reqVO 请求参数
     * @return 模考列表
     */
    PageResult<ExamAppPageRespVO> getAppExamPage(ExamAppPageReqVO reqVO);

    /**
     * 获取考试可用性
     *
     * @param hskLevel HSK级别
     * @return {@code Map<String, Boolean> }
     */
    Map<String, Boolean> getExamAvailability(Integer hskLevel);
}