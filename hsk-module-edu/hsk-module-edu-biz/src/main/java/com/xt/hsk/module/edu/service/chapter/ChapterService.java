package com.xt.hsk.module.edu.service.chapter;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterRespVO;
import com.xt.hsk.module.edu.dal.dataobject.chapter.ChapterDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import jakarta.validation.Valid;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterPageReqVO;

import java.util.List;

/**
 * 课程大纲 Service 接口
 *
 * <AUTHOR>
 */
public interface ChapterService extends IService<ChapterDO> {
    PageResult<ChapterDO> selectPage(@Valid ChapterPageReqVO pageReqVO);

    List<ChapterDO> getByTextbookId(Long id);

    PageResult<ChapterRespVO> selectChapterRespVOPage(@Valid ChapterPageReqVO pageReqVO);
}