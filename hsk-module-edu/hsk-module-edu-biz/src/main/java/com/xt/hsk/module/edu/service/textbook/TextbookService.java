package com.xt.hsk.module.edu.service.textbook;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.edu.dal.dataobject.textbook.TextbookDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import jakarta.validation.Valid;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.TextbookPageReqVO;

import java.util.List;

/**
 * 教材 Service 接口
 *
 * <AUTHOR>
 */
public interface TextbookService extends IService<TextbookDO> {
    PageResult<TextbookDO> selectPage(@Valid TextbookPageReqVO pageReqVO);

    List<TextbookDO> getByHskLevel(Integer hskLevel);
}