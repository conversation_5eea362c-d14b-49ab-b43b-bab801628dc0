package com.xt.hsk.module.edu.service.question.questiondetailversion;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.question.questiondetailversion.vo.QuestionDetailVersionPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.questiondetailversion.vo.QuestionDetailVersionSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetailversion.QuestionDetailVersionDO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 题目详情表版本库 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionDetailVersionManager {

    @Resource
    private QuestionDetailVersionService questionDetailVersionService;


    public Long createQuestionDetailVersion(QuestionDetailVersionSaveReqVO createReqVO) {
        // 插入
        QuestionDetailVersionDO questionDetailVersion = BeanUtils.toBean(createReqVO, QuestionDetailVersionDO.class);
        questionDetailVersionService.save(questionDetailVersion);

        // 返回
        return questionDetailVersion.getId();
    }


    public void updateQuestionDetailVersion(QuestionDetailVersionSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionDetailVersionExists(updateReqVO.getId());
        // 更新
        QuestionDetailVersionDO updateObj = BeanUtils.toBean(updateReqVO, QuestionDetailVersionDO.class);
        questionDetailVersionService.updateById(updateObj);
    }


    public void deleteQuestionDetailVersion(Long id) {
        // 校验存在
        validateQuestionDetailVersionExists(id);
        // 删除
        questionDetailVersionService.removeById(id);
    }

    private void validateQuestionDetailVersionExists(Long id) {
        if (questionDetailVersionService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public QuestionDetailVersionDO getQuestionDetailVersion(Long id) {
        return questionDetailVersionService.getById(id);
    }

    public PageResult<QuestionDetailVersionDO> getQuestionDetailVersionPage(@Valid QuestionDetailVersionPageReqVO pageReqVO) {
        return questionDetailVersionService.selectPage(pageReqVO);
    }

}