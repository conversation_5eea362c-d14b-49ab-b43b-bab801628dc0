package com.xt.hsk.module.edu.service.userpracticerecord;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.userpracticerecord.vo.UserPracticeRecordPageReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.AppUserPracticeRecordRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionSearchReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionStatisticsRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionRespVO;
import com.xt.hsk.module.edu.dal.dataobject.userpracticerecord.UserPracticeRecordDO;
import com.xt.hsk.module.edu.dal.mysql.userpracticerecord.UserPracticeRecordMapper;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 用户练习记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserPracticeRecordServiceImpl extends ServiceImpl<UserPracticeRecordMapper, UserPracticeRecordDO> implements UserPracticeRecordService {

    @Resource
    private UserPracticeRecordMapper userPracticeRecordMapper;

    @Override
    public PageResult<UserPracticeRecordDO> selectPage(UserPracticeRecordPageReqVO pageReqVO) {

        return userPracticeRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public QuestionStatisticsRespVO getQuestionNewestPracticeRecord(QuestionSearchReqVO reqVO) {

        return userPracticeRecordMapper.getQuestionNewestPracticeRecord(reqVO);
    }

    @Override
    public List<QuestionTypeCountRespVO> getUserUnitSortQuestionTypeCount(QuestionSearchReqVO reqVO) {

        return userPracticeRecordMapper.getUserUnitSortQuestionTypeCount(reqVO);
    }

    @Override
    public List<TextbookChapterQuestionRespVO> getUserTextbookChapterQuestions(QuestionSearchReqVO reqVO) {
        return userPracticeRecordMapper.getUserTextbookChapterQuestions(reqVO);
    }

    @Override
    public AppUserPracticeRecordRespVO getUserNotFinishedPracticeRecord(QuestionSearchReqVO reqVO) {
        return userPracticeRecordMapper.getUserNotFinishedPracticeRecord(reqVO);
    }

    /**
     * 获取用户练习中心的答题数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public List<TextbookChapterQuestionRespVO> getUserPracticeTextbookChapterList(QuestionSearchReqVO pageReqVO) {
        return userPracticeRecordMapper.getUserPracticeTextbookChapterList(pageReqVO);
    }

    @Override
    public QuestionStatisticsRespVO getQuestionNewestHaveReportPracticeRecord(QuestionSearchReqVO pageReqVO) {
        return userPracticeRecordMapper.getQuestionNewestHaveReportPracticeRecord(pageReqVO);
    }

    @Override
    public List<QuestionTypeCountRespVO> getHistoryUserUnitSortQuestionTypeCount(QuestionSearchReqVO pageReqVO) {
        return userPracticeRecordMapper.getHistoryUserUnitSortQuestionTypeCount(pageReqVO);
    }
}