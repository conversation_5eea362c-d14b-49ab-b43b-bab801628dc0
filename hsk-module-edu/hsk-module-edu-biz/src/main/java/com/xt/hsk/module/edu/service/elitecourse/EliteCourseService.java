package com.xt.hsk.module.edu.service.elitecourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCoursePageReqVO;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.EliteCourseAppPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import jakarta.validation.Valid;

/**
 * 精品课 Service 接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface EliteCourseService extends IService<EliteCourseDO> {

    /**
     * 获取精品课分页
     *
     * @param pageReqVO 分页查询
     * @return 精品课分页
     */
    PageResult<EliteCourseDO> selectPage(@Valid EliteCoursePageReqVO pageReqVO);

    /**
     * 获取精品课
     *
     * @param id 编号
     * @return 精品课
     */
    EliteCourseDO getEliteCourse(Long id);

    /**
     * 获取APP端精品课分页
     *
     * @param pageReqVO 分页查询
     * @return 精品课分页
     */
    PageResult<EliteCourseDO> getEliteCoursePage(EliteCourseAppPageReqVO pageReqVO);


    /**
     * 数据总数
     *
     * @param pageReqVO 分页查询
     * @return 数据总数
     */
    Long dataTotalCount(EliteCoursePageReqVO pageReqVO);

    /**
     * 更新精品课注册数
     *
     * @param courseId 课程ID
     */
    void updateCourseRegisterCount(Long courseId);

    /**
     * 定时任务上架精品课程
     */
    Integer releaseEliteCourse();
}