package com.xt.hsk.module.edu.controller.app.exam.vo;

import com.xt.hsk.module.edu.enums.exam.ExamAnswerModeEnum;
import com.xt.hsk.module.edu.enums.exam.ExamSubjectSectionsEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.Data;

/**
 * 模考 app req vo
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class ExamAppReqVO {

    /**
     * 模考ID
     */
    private Long examId;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer type;

    /**
     * 答题模式：1 重新作答 2 继续作答
     *
     * @see ExamAnswerModeEnum
     */
    private Integer answerType;

    /**
     * 记录id
     */
    private Long examRecordId;

    /**
     * 参与的模考科目 0-完整模考 1-听力 2-阅读 4-书写
     *
     * @see ExamSubjectSectionsEnum
     */
    private Integer examSections;

}