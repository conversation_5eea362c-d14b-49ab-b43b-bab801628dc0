package com.xt.hsk.module.edu.service.sourceword;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordMeaningPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordMeaningDO;
import com.xt.hsk.module.edu.dal.mysql.sourceword.SourceWordMeaningMapper;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Collections;
import java.util.List;


/**
 * 词语多释义表（冗余word） Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SourceWordMeaningServiceImpl extends ServiceImpl<SourceWordMeaningMapper, SourceWordMeaningDO> implements SourceWordMeaningService {

    @Autowired
    private SourceWordMeaningMapper wordMeaningMapper;

    @Override
    public PageResult<SourceWordMeaningDO> selectPage(WordMeaningPageReqVO pageReqVO) {

        return wordMeaningMapper.selectPage(pageReqVO);
    }

    @Override
    public void removeByWordId(Long wordId) {
        LambdaUpdateWrapper<SourceWordMeaningDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(SourceWordMeaningDO::getWordId, wordId);
        wordMeaningMapper.delete(queryWrapper);
    }


    @Override
    public List<SourceWordMeaningDO> getByWordId(Long wordId) {
        LambdaUpdateWrapper<SourceWordMeaningDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(SourceWordMeaningDO::getWordId, wordId);
        return wordMeaningMapper.selectList(queryWrapper);
    }


    @Override
    public List<SourceWordMeaningDO> getByWordIds(List<Long> wordIds) {
        if (wordIds == null || wordIds.isEmpty()) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SourceWordMeaningDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SourceWordMeaningDO::getWordId, wordIds);
        return wordMeaningMapper.selectList(queryWrapper);
    }

    @Override
    public List<SourceWordMeaningDO> getByWord(String word) {
        LambdaUpdateWrapper<SourceWordMeaningDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(SourceWordMeaningDO::getWord, word);
        return wordMeaningMapper.selectList(queryWrapper);
    }
}