package com.xt.hsk.module.edu.service.elitecourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.*;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.EliteCourseAppPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseRegisterDO;
import com.xt.hsk.module.edu.manager.elitecourse.dto.CourseAndRegisterDto;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 课程登记 Service 接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface EliteCourseRegisterService extends IService<EliteCourseRegisterDO> {
    PageResult<EliteCourseRegisterDO> selectPage(@Valid EliteCourseRegisterPageReqVO pageReqVO);

    /**
     * 分页获取课程用户
     */
    PageResult<EliteCourseUserRespVO> getCourseUserPage(EliteCourseStudyPageReqVO pageReqVO);

    /**
     * 课程用户总数
     */
    Long countCourseUser(EliteCourseStudyPageReqVO pageReqVO);

    /**
     * 分页获取课程注册用户
     */
    PageResult<EliteCourseRegisterUserRespVO> getCourseRegisterUserPage(EliteCourseRegisterUserPageReqVO pageReqVO);

    /**
     * 课程注册用户总数
     */
    Long countCourseRegisterUser(EliteCourseRegisterUserPageReqVO pageReqVO);

    /**
     * 获取用户课程注册数
     *
     * @param userId
     * @return
     */
    Long getUserCourseRegisterCount(Long userId);

    PageResult<CourseAndRegisterDto> getUserCourseRegister(EliteCourseAppPageReqVO pageVO);
}