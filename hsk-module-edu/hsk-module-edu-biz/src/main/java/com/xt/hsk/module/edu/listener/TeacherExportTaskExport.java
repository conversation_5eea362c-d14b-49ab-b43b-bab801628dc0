package com.xt.hsk.module.edu.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.translate.core.TranslateUtils;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherPageReqVO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherRespVO;
import com.xt.hsk.module.edu.manager.teacher.TeacherManager;
import com.xt.hsk.module.edu.service.teacher.TeacherService;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 讲师导出任务导出器
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Component
@Slf4j
public class TeacherExportTaskExport extends BaseEasyExcelExport<TeacherRespVO> {

    @Resource
    private TeacherService teacherService;

    @Resource
    private TeacherManager teacherManager;


    @Override
    protected List<List<String>> getExcelHead() {
        // 直接在这里定义表头，方便开发人员查看和维护
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("讲师名称"));
        head.add(Collections.singletonList("区号"));
        head.add(Collections.singletonList("手机号"));
        head.add(Collections.singletonList("课程"));
        head.add(Collections.singletonList("序号"));
        head.add(Collections.singletonList("展示状态"));
        head.add(Collections.singletonList("最近更新人"));
        head.add(Collections.singletonList("创建时间"));
        head.add(Collections.singletonList("最后更新时间"));
        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        // 使用配置好的 ObjectMapper 将 Map 转换为 TeacherPageReqVO 对象，忽略taskName字段
        TeacherPageReqVO dto = new TeacherPageReqVO();
        BeanUtil.copyProperties(conditions, dto);
        // 如果有选中的ID，返回选中的数量
        if (CollUtil.isNotEmpty(dto.getIds())) {
            return (long) dto.getIds().size();
        }

        return teacherService.dataTotalCount(dto);
    }

    /**
     * 构建数据列表
     *
     * @param resultList     结果列表
     * @param queryCondition 查询条件
     * @param pageNo         页码
     * @param pageSize       页面大小
     */
    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition,
        Long pageNo, Long pageSize) {

        TeacherPageReqVO dto = new TeacherPageReqVO();
        BeanUtil.copyProperties(queryCondition, dto);
            dto.setPageNo(Math.toIntExact(pageNo));
            dto.setPageSize(Math.toIntExact(pageSize));

            log.info("开始查询讲师数据，页码：{}，页面大小：{}", pageNo, pageSize);

            PageResult<TeacherRespVO> voPage = teacherManager.getTeacherVoPage(dto);
            log.info("查询到讲师分页数据，总数：{}", voPage.getTotal());

            List<TeacherRespVO> teacherRespVOList = voPage.getList();
            log.info("获取到讲师列表，数量：{}",
                teacherRespVOList != null ? teacherRespVOList.size() : 0);

            if (CollUtil.isEmpty(teacherRespVOList)) {
                log.info("讲师列表为空，直接返回");
                return;
            }

            // 手动翻译
            TranslateUtils.translate(teacherRespVOList);

            for (TeacherRespVO teacherRespVO : teacherRespVOList) {
                List<String> row = new ArrayList<>();

                // 讲师名称
                row.add(
                    teacherRespVO.getTeacherNameCn() != null ? teacherRespVO.getTeacherNameCn()
                        : "");

                // 手机区号
                row.add(
                    teacherRespVO.getCountryCode() != null ? teacherRespVO.getCountryCode() : "");
                // 手机号
                row.add(teacherRespVO.getMobile() != null ? teacherRespVO.getMobile() : "");

                // 课程
                row.add(
                    teacherRespVO.getCourseCount() != null ? teacherRespVO.getCourseCount()
                        .toString() : "0");

                // 排序序号
                row.add(teacherRespVO.getSort() != null ? teacherRespVO.getSort().toString() : "");

                // 展示状态
                row.add(Boolean.TRUE.equals(teacherRespVO.getDisplayStatus()) ? "显示" : "隐藏");

                // 最近更新人（这里会显示翻译后的用户名）
                row.add(
                    teacherRespVO.getUpdaterName() != null ? teacherRespVO.getUpdaterName() : "");

                // 创建时间
                row.add(teacherRespVO.getCreateTime() != null ?
                    DateUtil.format(teacherRespVO.getCreateTime(), "yyyy-MM-dd HH:mm:ss") : "");

                // 更新时间
                row.add(teacherRespVO.getUpdateTime() != null ?
                    DateUtil.format(teacherRespVO.getUpdateTime(), "yyyy-MM-dd HH:mm:ss") : "");

                resultList.add(row);
            }
            log.info("讲师导出，当前页：{}，每页条数：{}，总条数：{}", pageNo, pageSize,
                voPage.getTotal());
    }
} 