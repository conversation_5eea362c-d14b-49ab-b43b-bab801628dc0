package com.xt.hsk.module.edu.listener.elitecourse.export;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.listener.BaseExportTask;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 精品课程导出任务
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Slf4j
@Component("eliteCourseExportTask")
public class EliteCourseExportTask extends BaseExportTask<EliteCourseExportTaskExport> {

    @Resource
    private EliteCourseExportTaskExport eliteCourseExportTaskExport;

    @Override
    protected EliteCourseExportTaskExport getExporter() {
        return eliteCourseExportTaskExport;
    }

    @Override
    protected String getFileName() {
        return "精品课数据";
    }

    @Override
    protected Map<String, Object> buildQueryParams(String params) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            ExportTaskParams taskParams = objectMapper.readValue(params, ExportTaskParams.class);

            // 返回查询参数，任务名称已经在创建任务时使用，这里不需要处理
            return objectMapper.convertValue(taskParams.getQueryParams(),
                    new TypeReference<Map<String, Object>>() {
                    });
        } catch (Exception e) {
            log.error("解析导出参数失败: {}", e.getMessage(), e);
            return Map.of();
        }
    }
}
