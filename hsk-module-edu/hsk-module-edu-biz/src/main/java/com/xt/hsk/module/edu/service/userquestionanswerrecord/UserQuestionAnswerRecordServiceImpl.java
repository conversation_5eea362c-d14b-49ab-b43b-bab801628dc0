package com.xt.hsk.module.edu.service.userquestionanswerrecord;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.api.question.QuestionAnswerRecordApi;
import com.xt.hsk.module.edu.controller.admin.userquestionanswerrecord.vo.UserQuestionAnswerRecordPageReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionSearchReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionStatisticsRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.Valid;


import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerrecord.UserQuestionAnswerRecordDO;

import com.xt.hsk.module.edu.dal.mysql.userquestionanswerrecord.UserQuestionAnswerRecordMapper;

import java.util.List;


/**
 * 用户题目作答记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserQuestionAnswerRecordServiceImpl extends ServiceImpl<UserQuestionAnswerRecordMapper, UserQuestionAnswerRecordDO> implements UserQuestionAnswerRecordService, QuestionAnswerRecordApi {

    @Resource
    private UserQuestionAnswerRecordMapper userQuestionAnswerRecordMapper;

    @Override
    public PageResult<UserQuestionAnswerRecordDO> selectPage(UserQuestionAnswerRecordPageReqVO pageReqVO) {

        return userQuestionAnswerRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<QuestionTypeCountRespVO> getUserUnitSortQuestionTypeCount(QuestionSearchReqVO reqVO) {
        return userQuestionAnswerRecordMapper.getUserUnitSortQuestionTypeCount(reqVO);
    }

    @Override
    public Long getUserPracticeQuestionCount(Long userId) {
        return userQuestionAnswerRecordMapper.getUserPracticeQuestionCount(userId);
    }
}