package com.xt.hsk.module.edu.service.unit;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitTextBookVO;
import com.xt.hsk.module.edu.dal.dataobject.unit.UnitDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import jakarta.validation.Valid;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitPageReqVO;

import java.util.List;

/**
 * 单元 Service 接口
 *
 * <AUTHOR>
 */
public interface UnitService extends IService<UnitDO> {
    PageResult<UnitDO> selectPage(@Valid UnitPageReqVO pageReqVO);

    List<UnitDO> getByChapterId(Long id);

    List<UnitDO> getByChapterIdAndSubject(Long chapterId, Integer subject);

    List<UnitTextBookVO> queryUnitUnitData(String unitNameCn, String unitNameEn, String unitNameOt,Integer code);
}