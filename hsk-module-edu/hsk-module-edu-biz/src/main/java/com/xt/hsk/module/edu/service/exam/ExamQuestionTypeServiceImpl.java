package com.xt.hsk.module.edu.service.exam;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamQuestionTypeDO;
import com.xt.hsk.module.edu.dal.mysql.exam.ExamQuestionTypeMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * 模考题型 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Service
public class ExamQuestionTypeServiceImpl extends ServiceImpl<ExamQuestionTypeMapper, ExamQuestionTypeDO> implements ExamQuestionTypeService {

    @Resource
    private ExamQuestionTypeMapper examQuestionTypeMapper;

    @Override
    public PageResult<ExamQuestionTypeDO> selectPage(ExamQuestionTypePageReqVO pageReqVO) {

        return examQuestionTypeMapper.selectPage(pageReqVO);
    }

}