package com.xt.hsk.module.edu.service.interactivecourse;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseCoursewareDO;
import com.xt.hsk.module.edu.dal.mysql.interactivecourse.InteractiveCourseCoursewareMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 互动课课件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InteractiveCourseCoursewareServiceImpl extends
    ServiceImpl<InteractiveCourseCoursewareMapper, InteractiveCourseCoursewareDO>
    implements InteractiveCourseCoursewareService {

    @Resource
    private InteractiveCourseCoursewareMapper coursewareMapper;

} 