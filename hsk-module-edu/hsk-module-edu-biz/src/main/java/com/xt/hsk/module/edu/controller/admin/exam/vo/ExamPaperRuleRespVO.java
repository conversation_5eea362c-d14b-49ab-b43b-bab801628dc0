package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.module.edu.enums.exam.ExamPaperRuleStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 模考组卷规则 resp vo
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
public class ExamPaperRuleRespVO {

    /**
     * 组卷规则ID
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * HSK等级
     *
     * @see HskEnum
     */
    private Integer hskLevel;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer examType;

    /**
     * 阅读考试时长 (秒)
     */
    private Integer readingDuration;

    /**
     * 书写考试时长 (秒)
     */
    private Integer writingDuration;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 状态 1启用 0禁用
     *
     * @see ExamPaperRuleStatusEnum
     */
    private Integer status;

    /**
     * 听力单元部分
     */
    private List<ExamPaperRuleQuestionTypeRespVO> listeningSubjectList;

    /**
     * 阅读单元部分
     */
    private List<ExamPaperRuleQuestionTypeRespVO> readingSubjectList;

    /**
     * 书写单元部分
     */
    private List<ExamPaperRuleQuestionTypeRespVO> writingSubjectList;

}