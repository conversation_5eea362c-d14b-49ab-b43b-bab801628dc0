package com.xt.hsk.module.edu.service.question.questiondetail;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetail.QuestionDetailDO;
import com.xt.hsk.module.edu.dal.mysql.questiondetail.QuestionDetailMapper;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 题目详情 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionDetailServiceImpl extends ServiceImpl<QuestionDetailMapper, QuestionDetailDO> implements QuestionDetailService {

    @Resource
    private QuestionDetailMapper questionDetailMapper;

    @Override
    public PageResult<QuestionDetailDO> selectPage(QuestionDetailPageReqVO pageReqVO) {

        return questionDetailMapper.selectPage(pageReqVO);
    }

    @Override
    public List<QuestionDetailDO> selectListByQuestionId(Long questionId) {
        return questionDetailMapper.selectListByQuestionId(questionId);
    }

}