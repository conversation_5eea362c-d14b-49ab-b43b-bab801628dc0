package com.xt.hsk.module.edu.listener.exam.export;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.listener.BaseExportTask;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 用户模考记录导出任务
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Slf4j
@Component("userExamRecordExportTask")
public class UserExamRecordExportTask extends BaseExportTask<UserExamRecordExportTaskExport> {

    @Resource
    private UserExamRecordExportTaskExport userExamRecordExportTaskExport;

    @Override
    protected UserExamRecordExportTaskExport getExporter() {
        return userExamRecordExportTaskExport;
    }

    @Override
    protected String getFileName() {
        return "用户模考记录数据";
    }

    @Override
    protected Map<String, Object> buildQueryParams(String params) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            ExportTaskParams taskParams = objectMapper.readValue(params, ExportTaskParams.class);

            // 返回查询参数，任务名称已经在创建任务时使用，这里不需要处理
            return objectMapper.convertValue(taskParams.getQueryParams(),
                    new TypeReference<Map<String, Object>>() {
                    });
        } catch (Exception e) {
            log.error("解析导出参数失败: {}", e.getMessage(), e);
            return Map.of();
        }
    }
} 