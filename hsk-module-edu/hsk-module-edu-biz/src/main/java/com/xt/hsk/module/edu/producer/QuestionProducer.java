package com.xt.hsk.module.edu.producer;

import com.alibaba.fastjson.JSON;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseRecordBizTypeEnum;
import com.xt.hsk.module.edu.event.interactivecourse.PracticeCompletedEvent;
import com.xt.hsk.module.edu.event.interactivecourse.PracticeStartedEvent;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * 题目生产者
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
@Slf4j
@Component
public class QuestionProducer {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 发送互动课练习开始事件
     *
     * @param userId         用户ID
     * @param unitId         互动课单元ID
     * @param questionIdList 题目ID列表
     * @param practiceId     练习记录ID
     */
    public void sendPracticeStartedEvent(Long userId, Long unitId, List<Long> questionIdList,
        Long practiceId) {
        if (unitId == null) {
            log.debug("互动课单元ID为空，跳过发送练习开始事件: userId={}, questionIdList={}", userId,
                questionIdList);
            return;
        }

        log.info("发送互动课练习开始事件: {}",
            JSON.toJSONString(new Object[]{userId, unitId, questionIdList, practiceId}));

        PracticeStartedEvent startedEvent = new PracticeStartedEvent(
            this, userId, unitId, InteractiveCourseRecordBizTypeEnum.QUESTION_PRACTICE_RECORD,
            practiceId, null, questionIdList);
        applicationContext.publishEvent(startedEvent);
    }

    /**
     * 发送互动课练习完成事件
     *
     * @param userId     用户ID
     * @param unitId     互动课单元ID
     * @param questionIdList 练习记录ID
     * @param accuracy   正确率
     */
    public void sendPracticeCompletedEvent(Long userId, Long unitId, List<Long> questionIdList, BigDecimal accuracy,Long practiceId) {
        if (unitId == null) {
            log.debug("互动课单元ID为空，跳过发送练习完成事件: userId={}, questionIdList={}", userId, questionIdList);
            return;
        }

        log.info("发送互动课练习完成事件: {}", JSON.toJSONString(new Object[]{userId, unitId, questionIdList, accuracy}));

        PracticeCompletedEvent completedEvent = new PracticeCompletedEvent(
            this, userId, unitId, InteractiveCourseRecordBizTypeEnum.QUESTION_PRACTICE_RECORD,
            accuracy, false, practiceId);
        applicationContext.publishEvent(completedEvent);
    }
}
