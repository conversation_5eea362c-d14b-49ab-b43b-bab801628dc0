package com.xt.hsk.module.edu.service.word;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordMeaningPageReqVO;
import com.xt.hsk.module.edu.controller.app.word.vo.AppWordSearchVo;
import com.xt.hsk.module.edu.dal.dataobject.word.WordMeaningDO;
import com.xt.hsk.module.edu.dal.mysql.word.WordMeaningMapper;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 词语多释义表（冗余word） Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WordMeaningServiceImpl extends ServiceImpl<WordMeaningMapper, WordMeaningDO> implements WordMeaningService {

    @Resource
    private WordMeaningMapper wordMeaningMapper;

    @Override
    public PageResult<WordMeaningDO> selectPage(WordMeaningPageReqVO pageReqVO) {

        return wordMeaningMapper.selectPage(pageReqVO);
    }

    @Override
    public void removeByWordId(Long wordId) {
        LambdaUpdateWrapper<WordMeaningDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(WordMeaningDO::getWordId, wordId);
        wordMeaningMapper.delete(queryWrapper);
    }


    @Override
    public List<WordMeaningDO> getByWordId(Long wordId) {
        LambdaUpdateWrapper<WordMeaningDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(WordMeaningDO::getWordId, wordId);
        queryWrapper.orderByAsc(WordMeaningDO::getSort);
        return wordMeaningMapper.selectList(queryWrapper);
    }

    @Override
    public PageResult<WordMeaningDO> searchPage(AppWordSearchVo appWordSearchVo) {

        return wordMeaningMapper.searchPage(appWordSearchVo);
    }

    /**
     * 根据词汇ID列表批量获取含义
     * 对于每个wordId，只返回按sort排序的第一个含义
     *
     * @param wordIds 词汇ID列表
     * @return 含义列表，key为wordId，value为对应的WordMeaningDO对象
     */
    @Override
    public Map<Long, WordMeaningDO> getByWordIds(List<Long> wordIds) {
        if (wordIds == null || wordIds.isEmpty()) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<WordMeaningDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WordMeaningDO::getWordId, wordIds);
        queryWrapper.select(WordMeaningDO::getWordId, WordMeaningDO::getTranslationEn, 
                            WordMeaningDO::getTranslationOt, WordMeaningDO::getInterpretation);
        queryWrapper.orderByAsc(WordMeaningDO::getSort);
        
        // 按wordId分组
        Map<Long, List<WordMeaningDO>> wordMeaningListMap = wordMeaningMapper.selectList(queryWrapper).stream()
            .collect(Collectors.groupingBy(WordMeaningDO::getWordId));
        
        // 获取结果：对每个wordId，只取第一个含义（按sort排序后的）
        return wordMeaningListMap.entrySet()
            .stream()
            // 过滤掉没有含义的wordId
            .filter(entry -> CollUtil.isNotEmpty(entry.getValue()))
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().get(0)));
    }
}