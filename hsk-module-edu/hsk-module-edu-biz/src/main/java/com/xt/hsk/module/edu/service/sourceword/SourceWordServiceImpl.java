package com.xt.hsk.module.edu.service.sourceword;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordDO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordMeaningDO;
import com.xt.hsk.module.edu.dal.mysql.sourceword.SourceWordMapper;
import com.xt.hsk.module.edu.dal.mysql.sourceword.SourceWordMeaningMapper;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;


/**
 * 汉语词典基础数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class SourceWordServiceImpl extends ServiceImpl<SourceWordMapper, SourceWordDO> implements SourceWordService {

    @Autowired
    private SourceWordMapper wordMapper;
    @Autowired
    private SourceWordMeaningMapper wordMeaningMapper;

    @Override
    public PageResult<SourceWordDO> selectPage(WordPageReqVO pageReqVO) {

        return wordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SourceWordMeaningDO> getWordsMeaningsByWordIds(List<Long> wordIds) {
        if (wordIds == null || wordIds.isEmpty()) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SourceWordMeaningDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SourceWordMeaningDO::getWordId, wordIds);
        return wordMeaningMapper.selectList(queryWrapper);
    }

    @Override
    public List<SourceWordDO> getWordByType(String type) {
        LambdaQueryWrapper<SourceWordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SourceWordDO::getType, type);
        return wordMapper.selectList(queryWrapper);
    }


    @Override
    public List<SourceWordDO> getWordByName(String name) {
        LambdaQueryWrapper<SourceWordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SourceWordDO::getWord, name);
        queryWrapper.last("limit 10");
        return wordMapper.selectList(queryWrapper);
    }

}