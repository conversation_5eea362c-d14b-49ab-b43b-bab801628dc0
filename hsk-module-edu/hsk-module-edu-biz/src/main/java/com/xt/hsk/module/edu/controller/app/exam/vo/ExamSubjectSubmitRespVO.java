package com.xt.hsk.module.edu.controller.app.exam.vo;

import lombok.Data;

/**
 * 模考科目提交 resp vo
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Data
public class ExamSubjectSubmitRespVO {

    /**
     * 模考记录ID
     */
    private Long examRecordId;

    /**
     * 科目分数
     */
    private Integer subjectScore;

    /**
     * 科目正确题数
     */
    private Integer correctCount;

    /**
     * 科目总题数
     */
    private Integer totalCount;

    /**
     * 更新后的总分
     */
    private Integer totalScore;

    /**
     * 是否所有科目都已完成
     */
    private Boolean allSubjectsCompleted;

    /**
     * 最终听力得分
     */
    private Integer listeningScore;

    /**
     * 最终阅读得分
     */
    private Integer readingScore;

    /**
     * 最终书写得分
     */
    private Integer writingScore;
}