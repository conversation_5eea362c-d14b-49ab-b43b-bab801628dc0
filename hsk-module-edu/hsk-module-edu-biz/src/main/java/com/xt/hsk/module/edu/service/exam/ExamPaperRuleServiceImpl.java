package com.xt.hsk.module.edu.service.exam;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRulePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDO;
import com.xt.hsk.module.edu.dal.mysql.exam.ExamPaperRuleMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_PAPER_RULE_NOT_EXISTS;


/**
 * 模考组卷规则 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Service
public class ExamPaperRuleServiceImpl extends ServiceImpl<ExamPaperRuleMapper, ExamPaperRuleDO> implements ExamPaperRuleService {

    @Resource
    private ExamPaperRuleMapper examPaperRuleMapper;

    @Override
    public PageResult<ExamPaperRuleDO> selectPage(ExamPaperRulePageReqVO pageReqVO) {

        return examPaperRuleMapper.selectPage(pageReqVO);
    }

    /**
     * 根据id获取模考组卷规则DO
     *
     * @param id 模考组卷规则ID
     * @return 模考组卷规则DO
     */
    @Override
    public ExamPaperRuleDO getPaperRule(Long id) {
        ExamPaperRuleDO examPaperRule = getById(id);
        if (examPaperRule == null) {
            throw exception(EXAM_PAPER_RULE_NOT_EXISTS);
        }
        return examPaperRule;
    }

}