package com.xt.hsk.module.edu.consumer.interactivecourse;

import com.alibaba.fastjson.JSON;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseStatusEnum;
import com.xt.hsk.module.edu.event.interactivecourse.PracticeCompletedEvent;
import com.xt.hsk.module.edu.event.interactivecourse.PracticeStartedEvent;
import com.xt.hsk.module.edu.service.interactivecourse.UserInteractiveCourseRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 互动课练习事件侦听器
 *
 * <AUTHOR>
 * @since 2025/07/10
 */
@Slf4j
@Component
public class InteractiveCourseRecordEventListener {
    @Resource
    private UserInteractiveCourseRecordService userInteractiveCourseRecordService;

    /**
     * 开始练习事件
     *
     * @param event 事件
     */
    @Async
    @EventListener(PracticeStartedEvent.class)
    @Transactional(rollbackFor = Exception.class)
    public void handlePracticeStarted(PracticeStartedEvent event) {
        log.info("处理练习开始事件：{}", JSON.toJSONString(event));
        userInteractiveCourseRecordService.createBizRecord(
            event.getUserId(), event.getUnitId(),
            event.getBizType(), InteractiveCourseStatusEnum.IN_PROGRESS, event.getBizId(),event.getGameId(),event.getQuestionIds());
    }

    /**
     * 结束练习事件
     *
     * @param event 事件
     */
    @Async
    @EventListener(PracticeCompletedEvent.class)
    @Transactional(rollbackFor = Exception.class)
    public void handlePracticeCompleted(PracticeCompletedEvent event) {
        log.info("处理练习完成事件：{}", JSON.toJSONString(event));

        // 直接使用互动课状态枚举值
        Integer aiCorrectionStatus = Boolean.TRUE.equals(event.getHasAICorrection()) ? 1 : 0;

        userInteractiveCourseRecordService.updateRecordStatusAndDetails(
            event.getUserId(),
            event.getUnitId(),
            event.getBizType(),
            event.getBizId(),
            aiCorrectionStatus,
            event.getAccuracy());
    }
}
