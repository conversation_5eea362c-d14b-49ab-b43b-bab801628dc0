package com.xt.hsk.module.edu.controller.app.elitecourse.vo;

import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingStatusEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * APP - 精品课详情 Response VO
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Data
public class EliteCourseDetailRespVO {

    /**
     * 精品课ID
     */
    private Long id;

    /**
     * 课程名称-中文
     */
    private String courseName;

    /**
     * 课程封面大图URL
     */
    private String coverUrlLarge;

    /**
     * 课程封面小图URL
     */
    private String coverUrlSmall;

    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;

    /**
     * 课时数
     */
    private Integer classHourCount;

    /**
     * 课程详情内容
     */
    private String courseDetail;

    /**
     * 学习有效期类型 1：长期有效 2：按截止日期 3：按天数
     */
    private Integer learningValidityPeriod;

    /**
     * 截至日期
     */
    private LocalDateTime deadline;

    /**
     * 有效天数
     */
    private Integer effectiveDays;

    /**
     * 划线价格
     */
    private BigDecimal originalPrice;
    /**
     * 售卖价格
     */
    private BigDecimal sellingPrice;

    /**
     * 报名人数
     */
    private Integer enrollmentCount;

    /**
     * 上架状态 1：上架 2：下架 3：待上架
     *
     * @see EliteCourseListingStatusEnum
     */
    private Integer listingStatus;

    /**
     * 讲师列表
     */
    private List<TeacherVO> teachers;

    /**
     * 课程大纲信息列表
     */
    private List<ChapterVO> chapters;

    /**
     * 推荐课程列表
     */
    private List<RecommendedCourseVO> recommendedCourses;

    /**
     * 章节信息 VO
     */
    @Data
    public static class ChapterVO {

        /**
         * 章节ID
         */
        private Long id;

        /**
         * 章节名称-中文
         */
        private String chapterName;

        /**
         * 排序序号
         */
        private Integer sort;

        /**
         * 课时列表
         */
        private List<ClassHourVO> classHours;
    }

    /**
     * 课时信息 VO
     */
    @Data
    public static class ClassHourVO {

        /**
         * 课时ID
         */
        private Long id;

        /**
         * 课时名称
         */
        private String classHourName;

        /**
         * 课时类型 1：直播课 2：录播课
         */
        private Integer classHourType;

        /**
         * 排序序号
         */
        private Integer sort;

        /**
         * 课程时长 单位(秒)
         */
        private Integer duration;
        /**
         * 视频时长转化
         */
        private String durationStr;
        /**
         * .
         * 用户学习时长
         */
        private Integer userStudyLength;
        /**
         * 视频url
         */
        private String videoUrl;
    }

    /**
     * 讲师信息 VO
     */
    @Data
    public static class TeacherVO {

        /**
         * 讲师ID
         */
        private Long id;

        /**
         * 讲师名称-中文
         */
        private String teacherName;

        /**
         * 讲师头像
         */
        private String avatar;

        /**
         * 讲师介绍
         */
        private String teacherIntro;

        /**
         * 营销语
         */
        private String marketingSlogan;

        /**
         * 排序序号
         */
        private Integer sort;
    }

    /**
     * 推荐课程信息 VO
     */
    @Data
    public static class RecommendedCourseVO {

        /**
         * 课程ID
         */
        private Long id;

        /**
         * 课程名称
         */
        private String courseName;

        /**
         * 课程名称-中文
         */
        private String courseNameCn;

        /**
         * 课程名称-英文
         */
        private String courseNameEn;

        /**
         * 课程名称-其他
         */
        private String courseNameOt;

        /**
         * 课程封面小图URL
         */
        private String coverUrlSmall;

        /**
         * 课时数
         */
        private Integer classHourCount;

        /**
         * 售卖价格(人民币)
         */
        private BigDecimal sellingPriceCn;
        /**
         * 售价
         */
        private BigDecimal sellingPrice;

        /**
         * 售卖价格(美元)
         */
        private BigDecimal sellingPriceEn;

        /**
         * 售卖价格(越南盾)
         */
        private BigDecimal sellingPriceOt;

        /**
         * 报名人数
         */
        private Integer enrollmentCount;

        /**
         * 销售基数
         */
        private Integer salesBase;
    }
} 