package com.xt.hsk.module.edu.controller.app.interactivecourse.vo;

import com.xt.hsk.module.edu.enums.interactivecourse.UnitQuestionSourceTypeEnum;
import java.util.List;
import lombok.Data;

/**
 * 下一个单元信息响应VO
 *
 * <AUTHOR>
 * @since 2025/01/16
 */
@Data
public class NextUnitInfoRespVO {

    /**
     * 下一个单元ID
     */
    private Long nextUnitId;

    /**
     * 单元类型 1-视频 2-专项练习 3-真题练习
     * @see UnitQuestionSourceTypeEnum
     */
    private Integer questionSource;

    /**
     * 视频ID
     */
    private Long videoInfoId;

    /**
     * 专项练习ID（当unitType=2时有值）
     */
    private Long specialExerciseId;

    /**
     * 专项练习类型 1-单词连连看 2-笔画书写 3-连词成句 4-卡拉ok
     */
    private Integer specialPracticeType;

    /**
     * 学习状态 0-未开始 1-正在进行 2-已完成
     */
    private Integer learningStatus;

    /**
     * 真题练习题目ID列表（当unitType=3时有值，只包含未禁用的题目）
     */
    private List<Long> questionIds;

    /**
     * 上次练习记录信息
     */
    private LastRecordInfoVO lastPracticeRecordInfo;
}
