package com.xt.hsk.module.edu.controller.app.question.vo;

import java.util.List;
import java.util.Set;
import lombok.Data;

@Data
public class QuestionSearchReqVO {
    /**
     * hsk等级
     */
    private Integer hskLevel;
    /**
     * 科目 1 听力 2 阅读 4书写
     */
    private Integer subject;
    /**
     * 单元排序
     */
    private Integer unitSort;
    /**
     * 教材id
     */
    private Long textbookId;
    /**
     * 章节id
     */
    private Long chapterId;
    /**
     * 题型id
     */
    private Long typeId;
    /**
     * userId
     */
    private Long userId;
    /**
     * 练习情况 1 进行中 2 已完成 3 未开始
     */
    private List<Integer> status;
    /**
     * 练习类型 1 考场真题 2 模拟题
     */
    private List<Integer> textBookTypes;
    /**
     * 作答类型 1 重新作答 2 继续作答 3 新练习
     */
    private Integer answerType;
    /**
     * 练习记录id
     */
    private Long practiceRecordId;
    /**
     * 练习模式：1-单独练习 2-全真模考 3-30分钟模考 4-15分钟模考 5 互动课
     */
    private Integer practiceMode;
    /**
     * 练习题ids
     */
    private List<Long> questionIds;
    /**
     * 练习详情ids
     */
    private List<Long> questionDetailIds;
    /**
     * 练习题id
     */
    private Long questionId;
    /**
     * 题目明细id
     */
    private Long questionDetailId;

    /**
     * // 互动课单元ID
     */
    private Long interactiveCourseUnitId;

}
