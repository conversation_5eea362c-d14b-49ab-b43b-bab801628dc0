package com.xt.hsk.module.edu.service.interactivecourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseBaseInfoRespVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitQuoteVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRefCountDto;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseDO;
import java.util.Collection;
import java.util.List;

/**
 * 互动课 Service 接口
 *
 * <AUTHOR>
 */
public interface InteractiveCourseService extends IService<InteractiveCourseDO> {


    /**
     * 分页查询接口
     *
     * @param pageReqVO page req vo
     * @return {@code PageResult<InteractiveCourseDO> }
     */
    PageResult<InteractiveCourseDO> getInteractiveCoursePage(InteractiveCoursePageReqVO pageReqVO);

    /**
     * 查询总条目
     *
     * @param queryCondition 查询条件
     * @return 总条目
     */
    Long dataTotalCount(InteractiveCoursePageReqVO queryCondition);

    /**
     * 是否引用
     *
     * @param wordId 字 ID
     * @return boolean
     */
    boolean isQuoteWord(Long wordId);

    /**
     * 根据资源ID列表获取互动课基本信息列表
     *
     * @param resourceIdList 资源ID列表
     * @return 互动课基本信息列表
     */
    List<InteractiveCourseBaseInfoRespVO> listByResourceIdList(List<Long> resourceIdList);

    /**
     * 根据互动课程名称获取资源ID列表
     *
     * @param courseName 资源 ID 列表
     * @return 资源ID列表
     */
    List<Long> listResourceIdByCourseName(String courseName);

    /**
     * 获取互动课程的引用信息
     *
     * @param wordId 字词 ID
     * @return 互动课程的引用信息
     */
    List<InteractiveCourseUnitQuoteVO> getInteractiveCourseQuoteByWordId(Long wordId);

    /**
     * 获取hsk等级下状态为展示的课程数量
     *
     * @param hskLevel HSK 等级
     * @return 总数
     */
    Long getDisplayCountByHskLevel(Integer hskLevel);

    /**
     * 查询用户已学课程进度为100的课程数量
     * @param userId 用户ID
     * @param hskLevel hsk等级
     * @return 课程数量
     */
    Long getCompletedCourseCount(Long userId, Integer hskLevel);

    /**
     * 根据hsk等级查询目前APP端展示的互动课信息
     *
     * @param hskLevel HSK级别
     * @return 课程ID列表
     */
    List<Long> getDisplayInteractiveCourseListByHskLevel(Integer hskLevel);

    /**
     * 获取互动课程在当前等级中的排序位置
     *
     * @param hskLevel HSK等级
     * @param courseId 课程ID
     * @return 排序位置
     */
    Integer getCoursePositionInLevel(Integer hskLevel, Long courseId);

    /**
     * APP端专用分页查询（支持最近学习课程置顶）
     *
     * @param pageReqVO APP端分页请求参数
     * @param userId 用户ID（可为null，未登录用户）
     * @return 分页结果
     */
    PageResult<InteractiveCourseDO> getInteractiveCoursePageForApp(com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCoursePageReqVO pageReqVO, Long userId);

    /**
     * 查询题目在互动课程中的引用数
     *
     * @param questionIds 问题 ID
     * @return 引用次数
     */
    long countQuestionQuote(List<Long> questionIds);

    /**
     * 根据问题 ID 查询题目在互动课程中的引用数
     *
     * @param questionIds 问题 ID
     * @return 结果
     */
    List<QuestionRefCountDto> getInteractiveCourseQuestionQuoteCount(Collection<Long> questionIds);

    /**
     * 根据hsk等级查询第一门课程的信息(查询展示的课程 根据排序值 取最小值的课程)
     *
     * @param hskLevel HSK级别
     * @return 第一个课程 信息
     */
    InteractiveCourseDO getFirstCourseByHskLevel(Integer hskLevel);
}