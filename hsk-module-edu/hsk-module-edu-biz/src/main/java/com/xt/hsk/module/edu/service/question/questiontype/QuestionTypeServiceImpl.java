package com.xt.hsk.module.edu.service.question.questiontype;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiontype.QuestionTypeDO;
import com.xt.hsk.module.edu.dal.mysql.questiontype.QuestionTypeMapper;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 题型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionTypeServiceImpl extends ServiceImpl<QuestionTypeMapper, QuestionTypeDO> implements QuestionTypeService {

    @Resource
    private QuestionTypeMapper questionTypeMapper;

    @Override
    public PageResult<QuestionTypeDO> selectPage(QuestionTypePageReqVO pageReqVO) {

        return questionTypeMapper.selectPage(pageReqVO);
    }

    @Override
    public List<QuestionTypeDO> getQuestionTypeList(QuestionTypePageReqVO pageReqVO) {
        LambdaQueryWrapper<QuestionTypeDO> queryWrapper = new LambdaQueryWrapper<>();
        if (pageReqVO.getSubject() != null) {
            queryWrapper.eq(QuestionTypeDO::getSubject, pageReqVO.getSubject());
        }
        if (pageReqVO.getHskLevel() != null) {
            Integer hskLevel = pageReqVO.getHskLevel();
            queryWrapper.apply("hsk_level & " + hskLevel + " = " + hskLevel);
        }
        return questionTypeMapper.selectList(queryWrapper);
    }

}