package com.xt.hsk.module.edu.service.textbook;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.TextbookPageReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.Valid;


import com.xt.hsk.module.edu.dal.dataobject.textbook.TextbookDO;

import com.xt.hsk.module.edu.dal.mysql.textbook.TextbookMapper;

import java.util.List;


/**
 * 教材 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TextbookServiceImpl extends ServiceImpl<TextbookMapper, TextbookDO> implements TextbookService {

    @Resource
    private TextbookMapper textbookMapper;

    @Override
    public PageResult<TextbookDO> selectPage(TextbookPageReqVO pageReqVO) {

        return textbookMapper.selectPage(pageReqVO);
    }

    @Override
    public List<TextbookDO> getByHskLevel(Integer hskLevel) {
        LambdaQueryWrapper<TextbookDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TextbookDO::getHskLevel, hskLevel);
        queryWrapper.eq(TextbookDO::getIsShow, IsShowEnum.SHOW.getCode());
        return textbookMapper.selectList(queryWrapper);
    }

}