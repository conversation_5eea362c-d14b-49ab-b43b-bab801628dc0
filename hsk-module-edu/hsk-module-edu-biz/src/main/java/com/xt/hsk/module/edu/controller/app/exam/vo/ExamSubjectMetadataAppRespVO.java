package com.xt.hsk.module.edu.controller.app.exam.vo;

import com.xt.hsk.framework.common.enums.SubjectEnum;
import lombok.Data;

import java.util.List;

/**
 * 模考科目元信息 req vo
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class ExamSubjectMetadataAppRespVO {

    /**
     * 科目
     *
     * @see SubjectEnum
     */
    private Integer subject;

    /**
     * 科目
     */
    private String subjectDesc;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 总时间（分）
     */
    private Integer totalDuration;

    /**
     * 考试详细信息单元列表
     */
    private List<ExamUnitMetadataAppRespVO> examUnitMetadataList;

}