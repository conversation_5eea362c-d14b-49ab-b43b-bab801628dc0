package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.module.edu.enums.exam.ExamPaperRuleStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 模考组卷规则 resp vo
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
public class ExamPaperRulePageRespVO implements VO {

    /**
     * 组卷规则ID
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * HSK等级
     *
     * @see HskEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = HskEnum.class, ref = "hskLevelDesc")
    private Integer hskLevel;

    /**
     * HSK等级描述
     */
    private String hskLevelDesc;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = ExamTypeEnum.class, ref = "examTypeDesc")
    private Integer examType;

    /**
     * 模考类型描述
     */
    private String examTypeDesc;

    /**
     * 听力单元部分
     */
    private List<String> listeningUnitList;

    /**
     * 阅读单元部分
     */
    private List<String> readingUnitList;

    /**
     * 阅读考试时长 (秒)
     */
    private Integer readingDuration;

    /**
     * 阅读考试时长 (秒)
     */
    private String readingDurationStr;

    /**
     * 书写单元部分
     */
    private List<String> writingUnitList;

    /**
     * 书写考试时长 (秒)
     */
    private Integer writingDuration;

    /**
     * 书写考试时长 (秒)
     */
    private String writingDurationStr;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 状态 1启用 0禁用
     *
     * @see ExamPaperRuleStatusEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = ExamPaperRuleStatusEnum.class, ref = "statusDesc")
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 创建者id
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "creatorName")
    private String creator;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最近更新时间
     */
    private LocalDateTime updateTime;

}