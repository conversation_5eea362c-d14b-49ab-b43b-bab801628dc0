package com.xt.hsk.module.edu.service.elitecourse;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyRecordPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseStudyRecordDO;
import com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteCourseStudyRecordMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 精品课程学习记录 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Service
@Validated
public class EliteCourseStudyRecordServiceImpl extends ServiceImpl<EliteCourseStudyRecordMapper, EliteCourseStudyRecordDO> implements EliteCourseStudyRecordService {

    @Resource
    private EliteCourseStudyRecordMapper eliteCourseStudyRecordMapper;

    @Override
    public PageResult<EliteCourseStudyRecordDO> selectPage(EliteCourseStudyRecordPageReqVO pageReqVO) {

        return eliteCourseStudyRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<EliteCourseStudyRecordDO> getUserMaxStudyRecord(List<Long> hourIds, long userId) {
        return eliteCourseStudyRecordMapper.getUserMaxStudyRecord(hourIds, userId);
    }

}