package com.xt.hsk.module.edu.service.chapter;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterPageReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterRespVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.xt.hsk.module.edu.dal.dataobject.chapter.ChapterDO;

import com.xt.hsk.module.edu.dal.mysql.chapter.ChapterMapper;

import java.util.List;


/**
 * 课程大纲 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ChapterServiceImpl extends ServiceImpl<ChapterMapper, ChapterDO> implements ChapterService {

    @Resource
    private ChapterMapper chapterMapper;

    @Override
    public PageResult<ChapterDO> selectPage(ChapterPageReqVO pageReqVO) {

        return chapterMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ChapterDO> getByTextbookId(Long id) {

        return chapterMapper.getByTextbookId(id);
    }

    @Override
    public PageResult<ChapterRespVO> selectChapterRespVOPage(ChapterPageReqVO pageReqVO) {
        IPage<ChapterRespVO> pageResult = chapterMapper.selectChapterRespVOPage(new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize()), pageReqVO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

}