package com.xt.hsk.module.edu.service.sourceword;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordDO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordMeaningDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 汉语词典基础数据 Service 接口
 *
 * <AUTHOR>
 */
public interface SourceWordService extends IService<SourceWordDO> {
    PageResult<SourceWordDO> selectPage(@Valid WordPageReqVO pageReqVO);

    List<SourceWordMeaningDO> getWordsMeaningsByWordIds(List<Long> wordIds);
    List<SourceWordDO> getWordByType(String type);


    List<SourceWordDO> getWordByName(String name);
}