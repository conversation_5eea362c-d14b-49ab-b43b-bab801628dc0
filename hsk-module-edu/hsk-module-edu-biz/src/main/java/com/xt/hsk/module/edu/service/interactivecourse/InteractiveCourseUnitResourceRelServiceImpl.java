package com.xt.hsk.module.edu.service.interactivecourse;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitResourceRelDO;
import com.xt.hsk.module.edu.dal.mysql.interactivecourse.InteractiveCourseUnitResourceRelMapper;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 互动课单元资源关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InteractiveCourseUnitResourceRelServiceImpl extends
    ServiceImpl<InteractiveCourseUnitResourceRelMapper, InteractiveCourseUnitResourceRelDO>
    implements InteractiveCourseUnitResourceRelService {

    @Resource
    private InteractiveCourseUnitResourceRelMapper unitResourceRelMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<InteractiveCourseUnitResourceRelDO> batchCreateUnitResourceRel(Long unitId,
        Integer resourceType, List<Long> resourceIds) {
        // 删除原有关联
        deleteUnitResourceRelByUnitIdAndType(unitId, resourceType);

        // 创建新关联
        List<InteractiveCourseUnitResourceRelDO> relList = new ArrayList<>();
        for (int i = 0; i < resourceIds.size(); i++) {
            InteractiveCourseUnitResourceRelDO rel = new InteractiveCourseUnitResourceRelDO();
            rel.setUnitId(unitId);
            rel.setResourceType(resourceType);
            rel.setResourceId(resourceIds.get(i));
            rel.setSort(i + 1); // 排序从1开始
            unitResourceRelMapper.insert(rel);
            relList.add(rel);
        }
        return relList;
    }

    @Override
    public void deleteUnitResourceRelByUnitIdAndType(Long unitId, Integer resourceType) {
        unitResourceRelMapper.delete(new LambdaQueryWrapperX<InteractiveCourseUnitResourceRelDO>()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType, resourceType));
    }
} 