package com.xt.hsk.module.edu.service.elitecourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyRecordPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseStudyRecordDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 精品课程学习记录 Service 接口
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
public interface EliteCourseStudyRecordService extends IService<EliteCourseStudyRecordDO> {
    PageResult<EliteCourseStudyRecordDO> selectPage(@Valid EliteCourseStudyRecordPageReqVO pageReqVO);

    /**
     * 获取用户在指定课时下的最大学习记录
     *
     * @param hourIds
     * @param loginIdAsLong
     * @return
     */
    List<EliteCourseStudyRecordDO> getUserMaxStudyRecord(List<Long> hourIds, long loginIdAsLong);
}