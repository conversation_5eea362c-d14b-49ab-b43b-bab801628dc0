package com.xt.hsk.module.edu.service.word;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordExamplePageReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.xt.hsk.module.edu.dal.dataobject.word.WordExampleDO;

import com.xt.hsk.module.edu.dal.mysql.word.WordExampleMapper;

import java.util.List;


/**
 * 释义关联例句 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WordExampleServiceImpl extends ServiceImpl<WordExampleMapper, WordExampleDO> implements WordExampleService {

    @Resource
    private WordExampleMapper wordExampleMapper;

    @Override
    public PageResult<WordExampleDO> selectPage(WordExamplePageReqVO pageReqVO) {

        return wordExampleMapper.selectPage(pageReqVO);
    }

    @Override
    public void removeByWordId(Long wordId) {
        LambdaUpdateWrapper<WordExampleDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(WordExampleDO::getWordId, wordId);
        wordExampleMapper.delete(queryWrapper);
    }

    @Override
    public List<WordExampleDO> getByMeaningIds(List<Long> meaningIds) {
        LambdaQueryWrapper<WordExampleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WordExampleDO::getMeaningId, meaningIds);
        queryWrapper.orderByAsc(WordExampleDO::getSort);
        return wordExampleMapper.selectList(queryWrapper);
    }

}