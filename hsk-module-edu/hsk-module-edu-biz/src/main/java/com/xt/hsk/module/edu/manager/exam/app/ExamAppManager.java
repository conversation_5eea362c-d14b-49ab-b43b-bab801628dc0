package com.xt.hsk.module.edu.manager.exam.app;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.QuestionTypeEnum;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.BasicEnumUtil;
import com.xt.hsk.module.edu.controller.app.exam.vo.*;
import com.xt.hsk.module.edu.controller.app.question.vo.CallAiCorrectionReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDetailDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDetailVersionDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.UserExamRecordDO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetail.QuestionDetailDO;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerdata.UserQuestionAnswerDataDO;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerrecord.UserQuestionAnswerRecordDO;
import com.xt.hsk.module.edu.enums.exam.*;
import com.xt.hsk.module.edu.service.exam.ExamDetailService;
import com.xt.hsk.module.edu.service.exam.ExamDetailVersionService;
import com.xt.hsk.module.edu.service.exam.ExamService;
import com.xt.hsk.module.edu.service.exam.UserExamRecordService;
import com.xt.hsk.module.edu.service.question.QuestionService;
import com.xt.hsk.module.edu.service.question.questiondetail.QuestionDetailService;
import com.xt.hsk.module.edu.service.userquestionanswerrecord.UserQuestionAnswerRecordManager;
import com.xt.hsk.module.edu.service.userquestionanswerrecord.UserQuestionAnswerRecordService;
import com.xt.hsk.module.edu.service.userquestionanswerrecord.userquestionanswerdata.UserQuestionAnswerDataService;
import com.xt.hsk.module.thirdparty.enums.AiCorrectBizTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.*;

/**
 * 模考 app Manager
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@Component
public class ExamAppManager {

    @Resource
    private ExamService examService;

    @Resource
    private ExamDetailService examDetailService;

    @Resource
    private UserExamRecordService userExamRecordService;

    @Resource
    private QuestionService questionService;

    @Resource
    private QuestionDetailService questionDetailService;

    @Resource
    private UserQuestionAnswerRecordService userQuestionAnswerRecordService;

    @Resource
    private UserQuestionAnswerDataService userQuestionAnswerDataService;

    @Resource
    private UserQuestionAnswerRecordManager userQuestionAnswerRecordManager;

    @Resource
    private ExamDetailVersionService examDetailVersionService;

    /**
     * 分页获取模考
     */
    public PageResult<ExamAppPageRespVO> getAppExamPage(ExamAppPageReqVO pageReqVO) {

        ExamTypeEnum examTypeEnum = ExamTypeEnum.getByCode(pageReqVO.getType());
        if (HskEnum.getByCode(pageReqVO.getHskLevel()) == null || examTypeEnum == null) {
            return PageResult.empty();
        }

        if (StpUtil.isLogin()) {
            pageReqVO.setUserId(StpUtil.getLoginIdAsLong());
        }

        PageResult<ExamAppPageRespVO> page = examService.getAppExamPage(pageReqVO);

        List<ExamAppPageRespVO> pageList = page.getList();

        if (CollUtil.isEmpty(pageList)) {
            return PageResult.empty();
        }

        // 设置模考科目进度
        setExamSectionsProgress(pageList, examTypeEnum);

        return page;

    }

    private void setExamSectionsProgress(List<ExamAppPageRespVO> voList, ExamTypeEnum examTypeEnum) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        long userId = StpUtil.getLoginIdAsLong();

        if (ExamTypeEnum.FULL_REAL.equals(examTypeEnum)) {
            for (ExamAppPageRespVO respVO : voList) {
                ExamSectionsProgressReqVO reqVO = new ExamSectionsProgressReqVO();
                reqVO.setExamId(respVO.getExamId());
                ExamSectionsProgressRespVO examSectionsProgress = getExamSectionsProgress(reqVO);
                if(examSectionsProgress != null){
                    respVO.setExamSectionsProgressList(examSectionsProgress.getExamSectionsProgressList());
                }
            }


            // List<Long> examIdList = voList.stream()
            //         .map(ExamAppPageRespVO::getExamId)
            //         .filter(Objects::nonNull)
            //         .distinct()
            //         .toList();
            //
            // if (CollUtil.isEmpty(examIdList)) {
            //     return;
            // }
            //
            // List<UserExamRecordDO> userExamRecordList = userExamRecordService.lambdaQuery()
            //         .eq(UserExamRecordDO::getExamId, examIdList)
            //         .eq(UserExamRecordDO::getUserId, userId)
            //         .eq(UserExamRecordDO::getIsNewest, 1)
            //         .orderByAsc(UserExamRecordDO::getExamSections)
            //         .list();
            //
            // if (CollUtil.isEmpty(userExamRecordList)) {
            //     return;
            // }
            // Map<Long, List<UserExamRecordDO>> map = userExamRecordList.stream()
            //         .collect(Collectors.groupingBy(UserExamRecordDO::getExamId));
            //
            //
            // for (ExamAppPageRespVO respVO : voList) {
            //     List<UserExamRecordDO> recordList = map.getOrDefault(respVO.getId(), Collections.emptyList());
            //
            //     List<ExamSectionsDetailProgressRespVO> detailProgressRespVOList = new ArrayList<>();
            //
            //     for (UserExamRecordDO userExamRecordDO : recordList) {
            //         ExamSectionsDetailProgressRespVO progressRespVO = new ExamSectionsDetailProgressRespVO();
            //         progressRespVO.setExamId(userExamRecordDO.getExamId());
            //         progressRespVO.setExamRecordId(userExamRecordDO.getId());
            //         progressRespVO.setPracticeStatus(userExamRecordDO.getPracticeStatus());
            //         progressRespVO.setCorrectionStatus(userExamRecordDO.getCorrectionStatus());
            //         progressRespVO.setTotalScore(userExamRecordDO.getTotalScore());
            //         progressRespVO.setProgress(userExamRecordDO.getProgress().intValue());
            //         progressRespVO.setExamSections(userExamRecordDO.getExamSections());
            //         progressRespVO.setActualScore(userExamRecordDO.getActualScore());
            //         detailProgressRespVOList.add(progressRespVO);
            //     }
            //     respVO.setExamSectionsProgressList(detailProgressRespVOList);
            // }
        }
    }

    public ExamMetadataRespVO getAppExamMetadata(ExamAppReqVO pageReqVO) {

        ExamDO exam = examService.getById(pageReqVO.getExamId());

        if (exam == null || !ExamPublishStatusEnum.PUBLISHED.getCode().equals(exam.getPublishStatus())) {
            throw exception(EXAM_NOT_EXISTS_CANNOT_PARTICIPATE);
        }

        ExamMetadataRespVO vo = new ExamMetadataRespVO();
        vo.setExamId(exam.getId());
        vo.setHskLevel(exam.getHskLevel());
        vo.setName(exam.getName());
        vo.setType(exam.getType());
        vo.setCoverUrl(exam.getCoverUrl());

        List<ExamDetailDO> examDetailList = examDetailService.lambdaQuery()
                .eq(ExamDetailDO::getExamId, pageReqVO.getExamId())
                .orderByAsc(ExamDetailDO::getSubject, ExamDetailDO::getUnit)
                .list();

        Map<Integer, Map<Integer, List<ExamDetailDO>>> subjectMap = examDetailList.stream()
                .collect(Collectors.groupingBy(
                        ExamDetailDO::getSubject,
                        Collectors.groupingBy(ExamDetailDO::getUnit)
                ));

        List<ExamSubjectMetadataAppRespVO> subjectVOList = new ArrayList<>();
        for (Map.Entry<Integer, Map<Integer, List<ExamDetailDO>>> subjectEntry : subjectMap.entrySet()) {
            SubjectEnum subjectEnum = SubjectEnum.getByCode(subjectEntry.getKey());
            if (subjectEnum == null) {
                continue;
            }

            ExamSubjectMetadataAppRespVO subjectVO = new ExamSubjectMetadataAppRespVO();
            subjectVO.setSubject(subjectEnum.getCode());
            subjectVO.setSubjectDesc(subjectEnum.getDesc());
            if (SubjectEnum.LISTENING.equals(subjectEnum)) {
                subjectVO.setTotalDuration(exam.getListeningDuration());
            } else if (SubjectEnum.READING.equals(subjectEnum)) {
                subjectVO.setTotalDuration(exam.getReadingDuration());
            } else if (SubjectEnum.WRITING.equals(subjectEnum)) {
                subjectVO.setTotalDuration(exam.getWritingDuration());
            }

            int questionCount = 0;

            Map<Integer, List<ExamDetailDO>> unitList = subjectEntry.getValue();

            List<ExamUnitMetadataAppRespVO> unitVOList = new ArrayList<>();
            for (Map.Entry<Integer, List<ExamDetailDO>> unitEntry : unitList.entrySet()) {
                ExamQuestionTypeUnitEnum unitEnum = ExamQuestionTypeUnitEnum.getByCode(unitEntry.getKey());
                if (unitEnum == null) {
                    continue;
                }

                int size = unitEntry.getValue().size();
                ExamUnitMetadataAppRespVO unitRespVO = new ExamUnitMetadataAppRespVO();
                unitRespVO.setUnit(unitEnum.getCode());
                unitRespVO.setUnitDesc(unitEnum.getDesc());
                unitRespVO.setQuestionCount(size);

                questionCount += size;

                unitVOList.add(unitRespVO);
            }

            subjectVO.setExamUnitMetadataList(unitVOList);
            subjectVO.setQuestionCount(questionCount);

            subjectVOList.add(subjectVO);

        }

        vo.setExamSubjectMetadataList(subjectVOList);
        vo.setTotalQuestionCount(
                ObjectUtil.defaultIfNull(exam.getListeningDuration(), 0)
                        + ObjectUtil.defaultIfNull(exam.getReadingDuration(), 0)
                        + ObjectUtil.defaultIfNull(exam.getWritingDuration(), 0)
        );

        return vo;
    }

    /**
     * 获取模考科目信息
     */
    public ExamSubjectMetadataAppRespVO getAppExamSubjectInfo(ExamSubjectMetadataReqVO reqVO) {

        SubjectEnum subjectEnum = SubjectEnum.getByCode(reqVO.getSubject());
        if (subjectEnum == null) {
            return new ExamSubjectMetadataAppRespVO();
        }

        ExamDO exam = examService.getById(reqVO.getExamId());

        if (exam == null || !ExamPublishStatusEnum.PUBLISHED.getCode().equals(exam.getPublishStatus())) {
            throw exception(EXAM_NOT_EXISTS_CANNOT_PARTICIPATE);
        }

        List<ExamDetailDO> examDetailList = examDetailService.lambdaQuery()
                .eq(ExamDetailDO::getExamId, reqVO.getExamId())
                .eq(ExamDetailDO::getSubject, reqVO.getSubject())
                .orderByAsc(ExamDetailDO::getUnit)
                .list();

        if (CollUtil.isEmpty(examDetailList)) {
            throw exception(EXAM_SUBJECT_NOT_EXISTS, reqVO.getSubject());
        }

        ExamSubjectMetadataAppRespVO subjectVO = new ExamSubjectMetadataAppRespVO();
        subjectVO.setSubject(subjectEnum.getCode());
        subjectVO.setSubjectDesc(subjectEnum.getDesc());

        // 设置科目总时长
        if (SubjectEnum.LISTENING.equals(subjectEnum)) {
            subjectVO.setTotalDuration(exam.getListeningDuration());
        } else if (SubjectEnum.READING.equals(subjectEnum)) {
            subjectVO.setTotalDuration(exam.getReadingDuration());
        } else if (SubjectEnum.WRITING.equals(subjectEnum)) {
            subjectVO.setTotalDuration(exam.getWritingDuration());
        }

        // 按单元分组
        Map<Integer, List<ExamDetailDO>> unitMap = examDetailList.stream()
                .collect(Collectors.groupingBy(ExamDetailDO::getUnit));

        List<ExamUnitMetadataAppRespVO> unitVOList = new ArrayList<>();
        int questionCount = 0;

        for (Map.Entry<Integer, List<ExamDetailDO>> unitEntry : unitMap.entrySet()) {
            ExamQuestionTypeUnitEnum unitEnum = ExamQuestionTypeUnitEnum.getByCode(unitEntry.getKey());
            if (unitEnum == null) {
                continue;
            }

            int size = unitEntry.getValue().size();
            ExamUnitMetadataAppRespVO unitRespVO = new ExamUnitMetadataAppRespVO();
            unitRespVO.setUnit(unitEnum.getCode());
            unitRespVO.setUnitDesc(unitEnum.getDesc());
            unitRespVO.setQuestionCount(size);

            questionCount += size;
            unitVOList.add(unitRespVO);
        }

        subjectVO.setExamUnitMetadataList(unitVOList);
        subjectVO.setQuestionCount(questionCount);

        return subjectVO;
    }

    /**
     * 开始模考
     */
    @Transactional(rollbackFor = Exception.class)
    public UserExamRecordAppRespVO startExam(ExamAppReqVO reqVO) {

        ExamDO exam = examService.getById(reqVO.getExamId());
        if (exam == null) {
            throw exception(EXAM_NOT_EXISTS_CANNOT_PARTICIPATE);
        }

        Long userId = StpUtil.getLoginIdAsLong();

        // 如果是继续练习模式，处理继续练习逻辑
        if (ExamAnswerModeEnum.CONTINUE.getCode().equals(reqVO.getAnswerType())) {
            return handleContinueMode(userId, reqVO.getExamRecordId());
        }

        // 创建新的练习记录
        Long recordId = createNewRecord(exam, userId, reqVO.getExamSections());
        return new UserExamRecordAppRespVO().setRecordId(recordId);
    }

    /**
     * 获取模考答题卡
     */
    public List<ExamAnswerCardRespVO> getExamAnswerCard(ExamAnswerCardReqVO reqVO) {
        Long userId = StpUtil.getLoginIdAsLong();

        // 获取模考详情
        ExamDO exam = examService.getById(reqVO.getExamId());
        if (exam == null || !ExamPublishStatusEnum.PUBLISHED.getCode().equals(exam.getPublishStatus())) {
            throw exception(EXAM_NOT_EXISTS_CANNOT_PARTICIPATE);
        }

        UserExamRecordDO userExamRecordDO = userExamRecordService.getById(reqVO.getExamRecordId());
        if (userExamRecordDO == null || !Objects.equals(userExamRecordDO.getUserId(), userId)) {
            throw exception(EXAM_RECORD_NOT_EXISTS);
        }

        if (ExamRecordPracticeStatusEnum.COMPLETED.getCode().equals(userExamRecordDO.getPracticeStatus())) {
            throw exception(EXAM_RECORD_COMPLETED);
        }

        // 获取模考详情，按科目和单元排序
        List<ExamDetailVersionDO> examDetails = examDetailVersionService.lambdaQuery()
                .eq(ExamDetailVersionDO::getExamId, reqVO.getExamId())
                .eq(ExamDetailVersionDO::getVersion, userExamRecordDO.getExamDetailVersion())
                .orderByAsc(ExamDetailVersionDO::getSubject, ExamDetailVersionDO::getUnit)
                .list();

        if (CollUtil.isEmpty(examDetails)) {
            return Collections.emptyList();
        }

        Map<Integer, Map<Integer, List<Long>>> examAnswerCardData = getExamAnswerCardData(reqVO.getExamRecordId(), userId);

        Map<Integer, List<ExamDetailVersionDO>> subjectMap = examDetails.stream()
                .collect(Collectors.groupingBy(ExamDetailVersionDO::getSubject));

        List<ExamAnswerCardRespVO> voList = new ArrayList<>();

        int sort = 1;

        for (Map.Entry<Integer, List<ExamDetailVersionDO>> entry : subjectMap.entrySet()) {
            Integer subject = entry.getKey();
            List<ExamDetailVersionDO> examDetailList = entry.getValue();

            ExamAnswerCardRespVO cardRespVO = new ExamAnswerCardRespVO();
            cardRespVO.setSubject(subject);
            SubjectEnum subjectEnum = SubjectEnum.getByCode(subject);
            cardRespVO.setSubjectDesc(subjectEnum == null ? null : subjectEnum.getDesc());


            Map<Integer, List<Long>> subjectDataMap = examAnswerCardData.getOrDefault(subject, new HashMap<>());

            List<ExamAnswerCardUnitRespVO> unitList = new ArrayList<>();

            for (ExamDetailVersionDO examDetail : examDetailList) {
                ExamAnswerCardUnitRespVO unitRespVO = new ExamAnswerCardUnitRespVO();
                unitRespVO.setUnit(examDetail.getUnit());
                unitRespVO.setUnitDesc(ExamQuestionTypeUnitEnum.getDescByCode(examDetail.getUnit()));

                List<Long> unitDataList = subjectDataMap.getOrDefault(examDetail.getUnit(), new ArrayList<>());

                List<ExamAnswerCardQuestionRespVO> cardQuestionList = new ArrayList<>();

                String questions = examDetail.getQuestions();
                // 解析questions字段中的题目信息
                if (CharSequenceUtil.isNotBlank(questions)) {
                    List<ExamDetailQuestionVO> detailQuestions = JSONUtil.toList(questions, ExamDetailQuestionVO.class);
                    // 按排序序号排序
                    detailQuestions.sort(Comparator.comparing(ExamDetailQuestionVO::getSort, Comparator.nullsLast(Integer::compareTo)));

                    for (ExamDetailQuestionVO detailQuestion : detailQuestions) {
                        List<Long> questionDetailIdList = detailQuestion.getQuestionDetailIdList();
                        if (CollUtil.isNotEmpty(questionDetailIdList)) {
                            for (Long questionDetailId : questionDetailIdList) {
                                ExamAnswerCardQuestionRespVO questionRespVO = new ExamAnswerCardQuestionRespVO();
                                questionRespVO.setQuestionId(detailQuestion.getQuestionId());
                                questionRespVO.setQuestionDetailId(questionDetailId);
                                questionRespVO.setSort(sort++);
                                Integer answerStatus = unitDataList.contains(questionDetailId) ? 2 : 1;
                                questionRespVO.setAnswerStatus(answerStatus);
                                cardQuestionList.add(questionRespVO);
                            }
                        }
                    }
                }

                unitRespVO.setQuestionList(cardQuestionList);
                unitList.add(unitRespVO);

            }
            cardRespVO.setUnitList(unitList);

            voList.add(cardRespVO);
        }

        return voList;
    }

    private Map<Integer, Map<Integer, List<Long>>> getExamAnswerCardData(Long examRecordId, Long userId) {
        List<UserQuestionAnswerRecordDO> answerRecordList = userQuestionAnswerRecordService.lambdaQuery()
                .select(UserQuestionAnswerRecordDO::getId,
                        UserQuestionAnswerRecordDO::getSubject,
                        UserQuestionAnswerRecordDO::getUnitSort)
                .eq(UserQuestionAnswerRecordDO::getPracticeId, examRecordId)
                .eq(UserQuestionAnswerRecordDO::getUserId, userId)
                .list();

        List<Long> recordIdList = answerRecordList.stream()
                .map(UserQuestionAnswerRecordDO::getId)
                .toList();

        if (CollUtil.isEmpty(recordIdList)) {
            return Collections.emptyMap();
        }

        Map<Long, List<UserQuestionAnswerDataDO>> answerDataMap = userQuestionAnswerDataService.lambdaQuery()
                .in(UserQuestionAnswerDataDO::getRecordId, recordIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(UserQuestionAnswerDataDO::getRecordId));


        List<ExamAnswerCardDataVO> cardDataVOList = new ArrayList<>();

        for (UserQuestionAnswerRecordDO recordDO : answerRecordList) {
            List<UserQuestionAnswerDataDO> answerDataDOList = answerDataMap.get(recordDO.getId());
            if (CollUtil.isEmpty(answerDataDOList)) {
                continue;
            }
            for (UserQuestionAnswerDataDO answerDataDO : answerDataDOList) {
                ExamAnswerCardDataVO cardDataVO = new ExamAnswerCardDataVO();
                cardDataVO.setRecordId(recordDO.getId());
                cardDataVO.setSubject(recordDO.getSubject());
                cardDataVO.setUnitSort(recordDO.getUnitSort());
                cardDataVO.setQuestionId(answerDataDO.getQuestionId());
                cardDataVO.setQuestionDetailId(answerDataDO.getQuestionDetailId());
                cardDataVO.setIsCorrect(answerDataDO.getIsCorrect());
                cardDataVO.setAiCorrectStatus(answerDataDO.getAiCorrectStatus());
                cardDataVOList.add(cardDataVO);
            }
        }

        return cardDataVOList.stream()
                .filter(vo -> Objects.nonNull(vo.getSubject()) && Objects.nonNull(vo.getUnitSort()))
                .collect(Collectors.groupingBy(
                        ExamAnswerCardDataVO::getSubject,
                        Collectors.groupingBy(
                                ExamAnswerCardDataVO::getUnitSort,
                                Collectors.mapping(
                                        ExamAnswerCardDataVO::getQuestionDetailId,
                                        Collectors.toList()
                                )
                        )
                ));
    }

    /**
     * 获取用户答题状态
     */
    private Map<Long, Integer> getUserAnswerStatus(Long userId, Long examRecordId, List<QuestionDetailDO> questionDetails) {
        Map<Long, Integer> answerStatusMap = new HashMap<>();

        if (examRecordId == null || CollUtil.isEmpty(questionDetails)) {
            // 如果没有考试记录或没有题目详情，默认都未答题
            questionDetails.forEach(detail -> answerStatusMap.put(detail.getId(), 1));
            return answerStatusMap;
        }

        // 查询用户答题记录
        List<UserQuestionAnswerRecordDO> answerRecords = userQuestionAnswerRecordService.lambdaQuery()
                .eq(UserQuestionAnswerRecordDO::getUserId, userId)
                .eq(UserQuestionAnswerRecordDO::getPracticeId, examRecordId)
                .eq(UserQuestionAnswerRecordDO::getPracticeMode, 2)
                .list();
        Set<Long> answeredQuestionIds = answerRecords.stream()
                .map(UserQuestionAnswerRecordDO::getQuestionId)
                .collect(Collectors.toSet());

        // 设置答题状态
        questionDetails.forEach(detail -> {
            // 如果题目详情对应的主题目ID在已答题目ID集合中，则标记为已答
            if (answeredQuestionIds.contains(detail.getQuestionId())) {
                // 已答
                answerStatusMap.put(detail.getId(), 2);
            } else {
                // 未答
                answerStatusMap.put(detail.getId(), 1);
            }
        });

        return answerStatusMap;
    }

    /**
     * 构建答题卡响应数据
     */
    private List<ExamAnswerCardRespVO> buildAnswerCardResponse(List<ExamDetailDO> examDetails,
                                                               List<QuestionDO> questions,
                                                               List<QuestionDetailDO> questionDetails,
                                                               Map<Long, Integer> answerStatusMap) {

        // 创建题目映射
        Map<Long, QuestionDO> questionMap = questions.stream()
                .collect(Collectors.toMap(QuestionDO::getId, Function.identity()));

        // 创建题目详情映射
        Map<Long, List<QuestionDetailDO>> questionDetailMap = questionDetails.stream()
                .collect(Collectors.groupingBy(QuestionDetailDO::getQuestionId));

        // 按科目分组examDetails
        Map<Integer, List<ExamDetailDO>> subjectExamDetailMap = examDetails.stream()
                .collect(Collectors.groupingBy(ExamDetailDO::getSubject, LinkedHashMap::new, Collectors.toList()));

        List<ExamAnswerCardRespVO> result = new ArrayList<>();

        for (Map.Entry<Integer, List<ExamDetailDO>> subjectEntry : subjectExamDetailMap.entrySet()) {
            Integer subject = subjectEntry.getKey();
            List<ExamDetailDO> subjectExamDetails = subjectEntry.getValue();

            SubjectEnum subjectEnum = SubjectEnum.getByCode(subject);
            if (subjectEnum == null) {
                continue;
            }

            ExamAnswerCardRespVO answerCard = new ExamAnswerCardRespVO();
            answerCard.setSubject(subject);
            answerCard.setSubjectDesc(subjectEnum.getDesc());

            // 按单元分组
            Map<Integer, List<ExamDetailDO>> unitExamDetailMap = subjectExamDetails.stream()
                    .collect(Collectors.groupingBy(ExamDetailDO::getUnit, LinkedHashMap::new, Collectors.toList()));

            List<ExamAnswerCardUnitRespVO> unitList = new ArrayList<>();
            int totalQuestions = 0;
            int answeredQuestions = 0;

            for (Map.Entry<Integer, List<ExamDetailDO>> unitEntry : unitExamDetailMap.entrySet()) {
                Integer unit = unitEntry.getKey();
                List<ExamDetailDO> unitExamDetails = unitEntry.getValue();

                ExamQuestionTypeUnitEnum unitEnum = ExamQuestionTypeUnitEnum.getByCode(unit);
                if (unitEnum == null) {
                    continue;
                }

                ExamAnswerCardUnitRespVO unitVo = new ExamAnswerCardUnitRespVO();
                unitVo.setUnit(unit);
                unitVo.setUnitDesc(unitEnum.getDesc());

                List<ExamAnswerCardQuestionRespVO> questionList = new ArrayList<>();

                // 处理单元内的所有题目
                for (ExamDetailDO examDetail : unitExamDetails) {
                    // 解析questions字段中的题目信息
                    if (CharSequenceUtil.isNotBlank(examDetail.getQuestions())) {
                        List<ExamDetailQuestionVO> detailQuestions = JSONUtil.toList(examDetail.getQuestions(), ExamDetailQuestionVO.class);

                        // 按排序序号排序
                        detailQuestions.sort(Comparator.comparing(ExamDetailQuestionVO::getSort, Comparator.nullsLast(Integer::compareTo)));

                        for (ExamDetailQuestionVO detailQuestion : detailQuestions) {
                            Long questionId = detailQuestion.getQuestionId();
                            QuestionDO question = questionMap.get(questionId);
                            if (question == null) {
                                continue;
                            }

                            List<QuestionDetailDO> details = questionDetailMap.get(question.getId());
                            if (details != null && !details.isEmpty()) {
                                // 对于材料题，每个子题目都作为一个答题项
                                // 按sort排序
                                details.sort(Comparator.comparing(QuestionDetailDO::getSort, Comparator.nullsLast(Integer::compareTo)));

                                for (QuestionDetailDO detail : details) {
                                    ExamAnswerCardQuestionRespVO questionStatus = new ExamAnswerCardQuestionRespVO();
                                    questionStatus.setQuestionId(detail.getId());
                                    questionStatus.setAnswerStatus(answerStatusMap.getOrDefault(detail.getId(), 1));
                                    questionList.add(questionStatus);

                                    totalQuestions++;
                                    if (questionStatus.getAnswerStatus() == 2) {
                                        answeredQuestions++;
                                    }
                                }
                            } else {
                                // 没有子题目的情况，直接使用主题目
                                ExamAnswerCardQuestionRespVO questionStatus = new ExamAnswerCardQuestionRespVO();
                                questionStatus.setQuestionId(question.getId());
                                questionStatus.setAnswerStatus(answerStatusMap.getOrDefault(question.getId(), 1));
                                questionList.add(questionStatus);

                                totalQuestions++;
                                if (questionStatus.getAnswerStatus() == 2) {
                                    answeredQuestions++;
                                }
                            }
                        }
                    }
                }

                unitVo.setQuestionList(questionList);
                unitList.add(unitVo);
            }

            // 计算科目练习状态
            if (answeredQuestions == 0) {
                // 未开始
                answerCard.setPracticeStatus(1);
            } else if (answeredQuestions == totalQuestions) {
                // 已完成
                answerCard.setPracticeStatus(3);
            } else {
                // 进行中
                answerCard.setPracticeStatus(2);
            }

            answerCard.setUnitList(unitList);
            result.add(answerCard);
        }

        return result;
    }

    /**
     * 处理考试记录的继续答题模式
     *
     * @param userId   用户 ID
     * @param recordId 记录 ID
     */
    private UserExamRecordAppRespVO handleContinueMode(Long userId, Long recordId) {
        UserExamRecordDO userExamRecord = userExamRecordService.getById(recordId);

        if (userExamRecord == null || !Objects.equals(userExamRecord.getUserId(), userId)) {
            throw exception(EXAM_RECORD_NOT_EXISTS);
        }

        if (ExamRecordPracticeStatusEnum.COMPLETED.getCode().equals(userExamRecord.getPracticeStatus())) {
            throw exception(EXAM_RECORD_COMPLETED);
        }
        return new UserExamRecordAppRespVO().setRecordId(userExamRecord.getId());

    }

    /**
     * 创建新的模考记录
     */
    private Long createNewRecord(ExamDO exam, Long userId, Integer examSections) {

        List<ExamDetailDO> examDetailList = examDetailService.lambdaQuery()
                .eq(ExamDetailDO::getExamId, exam.getId())
                .list();

        if (CollUtil.isEmpty(examDetailList)) {
            throw exception(EXAM_NOT_EXISTS_CANNOT_PARTICIPATE);
        }

        // 获取题目数量
        int questionNum = examDetailList.stream()
                .mapToInt(ExamDetailDO::getQuestionCount)
                .sum();

        userExamRecordService.lambdaUpdate()
                .eq(UserExamRecordDO::getUserId, userId)
                .eq(UserExamRecordDO::getExamId, exam.getId())
                .eq(UserExamRecordDO::getExamSections, examSections)
                .set(UserExamRecordDO::getIsNewest, 0)
                .update();

        // 创建新的练习记录
        UserExamRecordDO recordSummary = new UserExamRecordDO();
        recordSummary.setUserId(userId);
        recordSummary.setExamId(exam.getId());
        recordSummary.setHskLevel(exam.getHskLevel());
        recordSummary.setExamType(exam.getType());
        recordSummary.setExamSections(examSections);
        recordSummary.setTotalScore(exam.getTotalScore());
        recordSummary.setActualScore(0);
        recordSummary.setListeningScore(0);
        recordSummary.setReadingScore(0);
        recordSummary.setWritingScore(0);
        recordSummary.setQuestionNum(questionNum);
        recordSummary.setAnswerNum(0);
        recordSummary.setCorrectNum(0);
        recordSummary.setWrongNum(0);
        recordSummary.setUnansweredNum(0);
        recordSummary.setAnswerTime(0);
        recordSummary.setStartTime(LocalDateTime.now());
        recordSummary.setCorrectionStatus(ExamCorrectionStatusEnum.IN_PROGRESS.getCode());
        recordSummary.setIsNewest(1);
        recordSummary.setPracticeStatus(1);
        recordSummary.setProgress(BigDecimal.ZERO);
        // 听力没有剩余时间
        recordSummary.setListeningRemainingTime(1);
        recordSummary.setReadingRemainingTime(exam.getReadingDuration());
        recordSummary.setWritingRemainingTime(exam.getReadingDuration());
        recordSummary.setExamDetailVersion(exam.getExamDetailVersion());

        userExamRecordService.save(recordSummary);

        return recordSummary.getId();
    }


    /**
     * 获取模考报告答题卡
     */
    public List<ExamReportAnswerCardRespVO> getExamReportAnswerCard(ExamReportAnswerCardReqVO reqVO) {
        Long userId = StpUtil.getLoginIdAsLong();

        // 获取模考详情
        ExamDO exam = examService.getById(reqVO.getExamId());
        if (exam == null || !ExamPublishStatusEnum.PUBLISHED.getCode().equals(exam.getPublishStatus())) {
            throw exception(EXAM_NOT_EXISTS_CANNOT_PARTICIPATE);
        }

        // 获取用户模考记录
        UserExamRecordDO examRecord = userExamRecordService.getById(reqVO.getExamRecordId());
        if (examRecord == null || !examRecord.getUserId().equals(userId)) {
            throw exception(EXAM_RECORD_NOT_EXISTS);
        }

        // 获取模考详情，按科目和单元排序
        List<ExamDetailDO> examDetails = examDetailService.lambdaQuery()
                .eq(ExamDetailDO::getExamId, reqVO.getExamId())
                .orderByAsc(ExamDetailDO::getSubject, ExamDetailDO::getUnit)
                .list();

        if (CollUtil.isEmpty(examDetails)) {
            return Collections.emptyList();
        }

        // 从ExamDetailDO的questions字段中解析出题目ID列表
        List<Long> questionIds = examDetails.stream()
                .filter(e -> CharSequenceUtil.isNotBlank(e.getQuestions()))
                .map(e -> JSONUtil.toList(e.getQuestions(), ExamDetailQuestionVO.class))
                .filter(CollUtil::isNotEmpty)
                .flatMap(List::stream)
                .map(ExamDetailQuestionVO::getQuestionId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(questionIds)) {
            return Collections.emptyList();
        }

        // 获取题目详情
        List<QuestionDO> questions = questionService.listByIds(questionIds);

        // 获取题目下的子题目（材料题下的多个小题）
        List<QuestionDetailDO> questionDetails = questionDetailService.lambdaQuery()
                .in(QuestionDetailDO::getQuestionId, questionIds)
                .list();

        // 获取用户答题记录和答题数据
        Map<Long, UserQuestionAnswerDataDO> answerDataMap = getUserAnswerDataMap(userId, reqVO.getExamRecordId(), questionIds);

        // 按科目和单元组织数据
        return buildReportAnswerCardResponse(examDetails, questions, questionDetails, answerDataMap, examRecord);
    }

    /**
     * 获取用户答题数据映射
     */
    private Map<Long, UserQuestionAnswerDataDO> getUserAnswerDataMap(Long userId, Long examRecordId, List<Long> questionIds) {
        // 首先获取用户答题记录
        List<UserQuestionAnswerRecordDO> answerRecords = userQuestionAnswerRecordService.lambdaQuery()
                .eq(UserQuestionAnswerRecordDO::getUserId, userId)
                .eq(UserQuestionAnswerRecordDO::getPracticeId, examRecordId)
                .eq(UserQuestionAnswerRecordDO::getPracticeMode, 2)
                .in(UserQuestionAnswerRecordDO::getQuestionId, questionIds)
                .list();

        if (CollUtil.isEmpty(answerRecords)) {
            return Collections.emptyMap();
        }

        // 获取答题记录ID列表
        List<Long> recordIds = answerRecords.stream()
                .map(UserQuestionAnswerRecordDO::getId)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(recordIds)) {
            return Collections.emptyMap();
        }

        // 获取答题数据
        List<UserQuestionAnswerDataDO> answerDataList = userQuestionAnswerDataService.lambdaQuery()
                .in(UserQuestionAnswerDataDO::getRecordId, recordIds)
                .list();

        // 按questionDetailId分组，方便后续查询
        return answerDataList.stream()
                .collect(Collectors.toMap(
                        UserQuestionAnswerDataDO::getQuestionDetailId,
                        Function.identity(),
                        (v1, v2) -> v1)
                );
    }

    /**
     * 构建模考报告答题卡响应数据
     */
    private List<ExamReportAnswerCardRespVO> buildReportAnswerCardResponse(List<ExamDetailDO> examDetails,
                                                                           List<QuestionDO> questions,
                                                                           List<QuestionDetailDO> questionDetails,
                                                                           Map<Long, UserQuestionAnswerDataDO> answerDataMap,
                                                                           UserExamRecordDO examRecord) {

        Map<Long, QuestionDO> questionMap = questions.stream()
                .collect(Collectors.toMap(QuestionDO::getId, Function.identity()));

        Map<Long, List<QuestionDetailDO>> questionDetailMap = questionDetails.stream()
                .collect(Collectors.groupingBy(QuestionDetailDO::getQuestionId));

        return examDetails.stream()
                .collect(Collectors.groupingBy(ExamDetailDO::getSubject))
                .entrySet().stream()
                .map(subjectEntry -> {
                    Integer subject = subjectEntry.getKey();
                    List<ExamDetailDO> subjectDetails = subjectEntry.getValue();

                    ExamReportAnswerCardRespVO subjectResp = new ExamReportAnswerCardRespVO();
                    subjectResp.setSubject(subject);
                    subjectResp.setSubjectDesc(BasicEnumUtil.getDescByCode(SubjectEnum.class, subject));
                    subjectResp.setPracticeStatus(3);
                    subjectResp.setTotalScore(100);

                    // 从模考记录中获取科目得分
                    int subjectScore = getSubjectScoreFromRecord(subject, examRecord);
                    subjectResp.setScore(subjectScore);

                    // 构建单元列表
                    List<ExamReportAnswerCardUnitRespVO> unitList = subjectDetails.stream()
                            .map(detail -> {
                                ExamReportAnswerCardUnitRespVO unitResp = new ExamReportAnswerCardUnitRespVO();
                                unitResp.setUnit(detail.getUnit());
                                unitResp.setUnitDesc(ExamQuestionTypeUnitEnum.getDescByCode(detail.getUnit()));

                                // 获取题型名称（从questionNames字段）
                                unitResp.setQuestionTypeNames(detail.getQuestionNames());

                                // 构建题目列表
                                List<ExamReportAnswerCardQuestionRespVO> questionList = buildUnitQuestions(
                                        detail, questionDetailMap, answerDataMap, questionMap);
                                unitResp.setQuestionList(questionList);

                                return unitResp;
                            })
                            .collect(Collectors.toList());

                    subjectResp.setUnitList(unitList);
                    return subjectResp;
                })
                .collect(Collectors.toList());
    }

    /**
     * 从模考记录中获取科目得分
     */
    private int getSubjectScoreFromRecord(Integer subject, UserExamRecordDO examRecord) {
        if (SubjectEnum.LISTENING.getCode().equals(subject)) {
            return examRecord.getListeningScore() != null ? examRecord.getListeningScore() : 0;
        } else if (SubjectEnum.READING.getCode().equals(subject)) {
            return examRecord.getReadingScore() != null ? examRecord.getReadingScore() : 0;
        } else if (SubjectEnum.WRITING.getCode().equals(subject)) {
            return examRecord.getWritingScore() != null ? examRecord.getWritingScore() : 0;
        }
        return 0;
    }

    /**
     * 构建单元题目列表
     */
    private List<ExamReportAnswerCardQuestionRespVO> buildUnitQuestions(ExamDetailDO detail,
                                                                        Map<Long, List<QuestionDetailDO>> questionDetailMap,
                                                                        Map<Long, UserQuestionAnswerDataDO> answerDataMap,
                                                                        Map<Long, QuestionDO> questionMap) {
        if (CharSequenceUtil.isBlank(detail.getQuestions())) {
            return Collections.emptyList();
        }

        List<ExamDetailQuestionVO> questionVOs = JSONUtil.toList(detail.getQuestions(), ExamDetailQuestionVO.class);
        List<ExamReportAnswerCardQuestionRespVO> result = new ArrayList<>();

        for (ExamDetailQuestionVO questionVO : questionVOs) {
            List<QuestionDetailDO> details = questionDetailMap.get(questionVO.getQuestionId());
            if (CollUtil.isEmpty(details)) {
                continue;
            }

            QuestionDO question = questionMap.get(questionVO.getQuestionId());
            boolean isWritingQuestion = isWritingQuestion(question);

            for (QuestionDetailDO questionDetail : details) {
                ExamReportAnswerCardQuestionRespVO questionResp = new ExamReportAnswerCardQuestionRespVO();
                questionResp.setQuestionId(questionDetail.getId());

                UserQuestionAnswerDataDO answerData = answerDataMap.get(questionDetail.getId());
                if (answerData == null) {
                    // 未答
                    questionResp.setAnswerStatus(1);
                } else if (isWritingQuestion) {
                    // 书写题：根据AI批改状态判断
                    if (answerData.getAiCorrectStatus() != null && answerData.getAiCorrectStatus() == 1) {
                        // 已批改，但书写题不显示对错，显示为已答
                        // 已答
                        questionResp.setAnswerStatus(2);
                    } else {
                        // 未批改
                        questionResp.setAnswerStatus(5);
                    }
                } else {
                    // 其他题型：答对或答错
                    if (answerData.getIsCorrect() != null) {
                        questionResp.setAnswerStatus(answerData.getIsCorrect() ? 3 : 4);
                    } else {
                        // 已答但未判定
                        questionResp.setAnswerStatus(2);
                    }
                }

                result.add(questionResp);
            }
        }

        return result;
    }

    /**
     * 判断是否为书写题
     */
    private boolean isWritingQuestion(QuestionDO question) {
        if (question == null) {
            return false;
        }
        // 根据科目判断是否为书写题
        return SubjectEnum.WRITING.getCode().equals(question.getSubject());
    }

    /**
     * 提交科目答案并计算分数
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitExamSubject(ExamSubjectSubmitReqVO reqVO) {
        Long userId = StpUtil.getLoginIdAsLong();

        log.info("提交科目答案,用户id:{}, 模考记录ID: {}, 科目: {}", userId, reqVO.getExamRecordId(), reqVO.getSubject());

        // 获取模考记录
        UserExamRecordDO examRecord = userExamRecordService.getById(reqVO.getExamRecordId());
        if (examRecord == null || !examRecord.getUserId().equals(userId)) {
            throw exception(EXAM_RECORD_NOT_EXISTS);
        }

        // 获取当前科目的所有题目详情
        List<QuestionDetailDO> questionDetails = getSubjectQuestionDetails(examRecord.getExamId(), reqVO.getSubject());
        if (CollUtil.isEmpty(questionDetails)) {
            log.warn("科目 {} 没有找到题目", reqVO.getSubject());
            return;
        }

        // 提取所有题目ID
        List<Long> questionIds = questionDetails.stream()
                .map(QuestionDetailDO::getQuestionId)
                .collect(Collectors.toList());

        // 获取用户作答记录
        List<UserQuestionAnswerRecordDO> answerRecords = userQuestionAnswerRecordService.lambdaQuery()
                .eq(UserQuestionAnswerRecordDO::getUserId, examRecord.getUserId())
                .eq(UserQuestionAnswerRecordDO::getPracticeId, reqVO.getExamRecordId())
                .in(UserQuestionAnswerRecordDO::getQuestionId, questionIds).list();

        if (SubjectEnum.WRITING.getCode().equals(reqVO.getSubject())) {
            if (CollUtil.isEmpty(answerRecords)) {
                return;
            }

            CompletableFuture.runAsync(() -> {
                log.info("用户{}提交答案,开始调用AI批改", userId);
                for (UserQuestionAnswerRecordDO recordDO : answerRecords) {

                    Long recordId = recordDO.getId();

                    log.info("用户{}提交答案,开始调用AI批改,记录id{}批改中", userId, recordId);

                    try {
                        UserQuestionAnswerRecordDO answerRecordDO = userQuestionAnswerRecordService.lambdaQuery()
                                .eq(UserQuestionAnswerRecordDO::getUserId, userId)
                                .eq(UserQuestionAnswerRecordDO::getId, recordId)
                                .one();
                        if (answerRecordDO == null) {
                            log.warn("用户{}提交答案,记录id{}不存在,跳过", userId, recordId);
                            continue;
                        }

                        List<QuestionTypeEnum> writingQuestionTypeList = QuestionTypeEnum.getWritingQuestionTypeList();
                        QuestionTypeEnum questionTypeEnum = QuestionTypeEnum.getByCode(answerRecordDO.getQuestionTypeId());
                        if (!writingQuestionTypeList.contains(questionTypeEnum)) {
                            log.info("用户{}提交答案,记录id{},题型ID:{},非AI批改题,跳过", userId, recordId, answerRecordDO.getQuestionTypeId());
                            continue;
                        }

                        CallAiCorrectionReqVO correctionReqVO = new CallAiCorrectionReqVO();
                        correctionReqVO.setRecordId(recordId);
                        correctionReqVO.setUserId(userId);
                        correctionReqVO.setBizType(AiCorrectBizTypeEnum.MOCK_EXAM.getCode());
                        correctionReqVO.setBizId(examRecord.getId());
                        Integer result = userQuestionAnswerRecordManager.questionCallAiCorrection(correctionReqVO);

                        // 1 已达上限 2 批改中 3批改成功  4批改失败
                        if (result == 1) {
                            log.error("用户{}提交答案,记录id{},AI批改失败,已达上限", userId, recordId);
                        } else if (result == 2) {
                            log.info("用户{}提交答案,记录id{},AI批改中,正在处理中", userId, recordId);
                        } else if (result == 3) {
                            log.info("用户{}提交答案,记录id{},AI批改成功", userId, recordId);
                        } else if (result == 4) {
                            log.error("用户{}提交答案,记录id{},AI批改失败", userId, recordId);
                        } else {
                            log.error("用户{}提交答案,记录id{},未知错误", userId, recordId);
                        }
                    } catch (Exception e) {
                        log.error("用户{}提交答案,记录id{},AI批改失败,失败原因:{}", userId, recordId, e.getMessage(), e);
                    }

                }
            });

            userExamRecordService.lambdaUpdate()
                    .eq(UserExamRecordDO::getId, examRecord.getId())
                    .set(UserExamRecordDO::getCorrectionStatus, ExamCorrectionStatusEnum.PENDING_REVIEW.getCode())
                    .set(UserExamRecordDO::getWritingRemainingTime, 0)
                    .set(UserExamRecordDO::getUpdateTime, LocalDateTime.now())
                    .update();

            return;
        }


        // 获取用户作答数据
        List<Long> recordIds = answerRecords.stream()
                .map(UserQuestionAnswerRecordDO::getId)
                .collect(Collectors.toList());

        List<UserQuestionAnswerDataDO> answerDataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(recordIds)) {
            answerDataList = userQuestionAnswerDataService.lambdaQuery()
                    .in(UserQuestionAnswerDataDO::getRecordId, recordIds)
                    .list();
        }

        // 计算题目实际数量
        int totalQuestionCount = calculateActualQuestionCount(questionDetails);

        // 计算正确题目数量
        int correctCount = calculateCorrectCount(answerDataList, questionDetails);

        // 根据科目计算分数
        int subjectScore = calculateSubjectScore(reqVO.getSubject(), examRecord.getHskLevel(), examRecord.getExamType(), correctCount, totalQuestionCount);

        // 更新用户模考记录
        updateExamRecordScore(examRecord, reqVO.getSubject(), subjectScore, correctCount, totalQuestionCount);

        log.info("科目 {} 计分完成，得分: {}, 正确: {}/{}", reqVO.getSubject(), subjectScore, correctCount, totalQuestionCount);
    }

    /**
     * 计算题目实际数量
     */
    private int calculateActualQuestionCount(List<QuestionDetailDO> questionDetails) {
        // 按题目ID分组
        Map<Long, List<QuestionDetailDO>> questionGroupMap = questionDetails.stream()
                .collect(Collectors.groupingBy(QuestionDetailDO::getQuestionId));

        // 每个题目下有x个详情，实际数量是1*x道题
        return questionGroupMap.values().stream()
                .mapToInt(List::size)
                .sum();
    }

    /**
     * 计算正确题目数量
     */
    private int calculateCorrectCount(List<UserQuestionAnswerDataDO> answerDataList, List<QuestionDetailDO> questionDetails) {
        if (CollUtil.isEmpty(answerDataList)) {
            return 0;
        }

        // 按题目详情ID分组作答数据
        Map<Long, UserQuestionAnswerDataDO> answerDataMap = answerDataList.stream()
                .collect(Collectors.toMap(
                        UserQuestionAnswerDataDO::getQuestionDetailId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        int correctCount = 0;
        for (QuestionDetailDO questionDetail : questionDetails) {
            UserQuestionAnswerDataDO answerData = answerDataMap.get(questionDetail.getId());
            if (answerData != null && Boolean.TRUE.equals(answerData.getIsCorrect())) {
                correctCount++;
            }
        }

        return correctCount;
    }

    /**
     * 根据科目计算分数
     *
     * @param subject      科目
     * @param hskLevel     hsk等级
     * @param examType     模考类型
     * @param correctCount 正确题数
     * @param totalCount   总题数
     * @return int 分数
     */
    private int calculateSubjectScore(Integer subject, Integer hskLevel, Integer examType, int correctCount, int totalCount) {
        if (totalCount == 0) {
            return 0;
        }

        // 听力/阅读的科目分数
        if (SubjectEnum.LISTENING.getCode().equals(subject) || SubjectEnum.READING.getCode().equals(subject)) {
            // 正确题目数 × (100 ÷ 总题数)
            double unitScore = NumberUtil.div(100, totalCount, 10);
            double rawScore = NumberUtil.mul(correctCount, unitScore);

            // 四舍五入取整
            return NumberUtil.round(rawScore, 0, RoundingMode.HALF_UP).intValue();
        }

        if (SubjectEnum.WRITING.getCode().equals(subject)) {
            return calculateWritingScore(correctCount, totalCount);
        }

        return 0;
    }

    /**
     * 书写科目算分方法（预留）
     */
    private int calculateWritingScore(int correctCount, int totalCount) {
        // TODO: 书写得分算法
        // 这里先使用和听力阅读相同的算法
        return 0;
    }

    /**
     * 更新用户模考记录分数
     */
    private void updateExamRecordScore(UserExamRecordDO examRecord, Integer subject, int subjectScore, int correctCount, int totalQuestionCount) {
        UserExamRecordDO updateRecord = new UserExamRecordDO();
        updateRecord.setId(examRecord.getId());

        // 更新对应科目的得分
        if (subject == 1) {
            // 听力
            updateRecord.setListeningScore(subjectScore);
        } else if (subject == 2) {
            // 阅读
            updateRecord.setReadingScore(subjectScore);
        } else if (subject == 4) {
            // 书写
            updateRecord.setWritingScore(subjectScore);
        }

        // 重新计算总分：总分 = 听力 + 阅读 + 书写
        UserExamRecordDO currentRecord = userExamRecordService.getById(examRecord.getId());
        int totalScore = 0;
        if (currentRecord.getListeningScore() != null) {
            totalScore += currentRecord.getListeningScore();
        }
        if (currentRecord.getReadingScore() != null) {
            totalScore += currentRecord.getReadingScore();
        }
        if (currentRecord.getWritingScore() != null) {
            totalScore += currentRecord.getWritingScore();
        }

        // 如果是当前提交的科目，使用新计算的分数
        if (subject == 1 && updateRecord.getListeningScore() != null) {
            totalScore = totalScore - ObjectUtil.defaultIfNull(currentRecord.getListeningScore(), 0) + subjectScore;
        } else if (subject == 2 && updateRecord.getReadingScore() != null) {
            totalScore = totalScore - ObjectUtil.defaultIfNull(currentRecord.getReadingScore(), 0) + subjectScore;
        } else if (subject == 4 && updateRecord.getWritingScore() != null) {
            totalScore = totalScore - ObjectUtil.defaultIfNull(currentRecord.getWritingScore(), 0) + subjectScore;
        }

        updateRecord.setActualScore(totalScore);

        // 更新正确题数和答题数
        updateRecord.setCorrectNum(ObjectUtil.defaultIfNull(currentRecord.getCorrectNum(), 0) + correctCount);
        updateRecord.setAnswerNum(ObjectUtil.defaultIfNull(currentRecord.getAnswerNum(), 0) + totalQuestionCount);

        userExamRecordService.updateById(updateRecord);
    }

    /**
     * 获取科目的所有题目详情
     */
    private List<QuestionDetailDO> getSubjectQuestionDetails(Long examId, Integer subject) {
        List<Long> questionIds = examDetailService.lambdaQuery()
                .eq(ExamDetailDO::getExamId, examId)
                .eq(ExamDetailDO::getSubject, subject)
                .list()
                .stream()
                .filter(e -> CharSequenceUtil.isNotBlank(e.getQuestions()))
                .map(e -> JSONUtil.toList(e.getQuestions(), ExamDetailQuestionVO.class))
                .filter(CollUtil::isNotEmpty)
                .flatMap(List::stream)
                .map(ExamDetailQuestionVO::getQuestionId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(questionIds)) {
            return Collections.emptyList();
        }

        return questionDetailService.lambdaQuery()
                .in(QuestionDetailDO::getQuestionId, questionIds)
                .list();
    }

    /**
     * 获取试听音频
     */
    public String getPreviewAudio() {
        return "";
    }

    /**
     * 获取模考分科目进度信息
     *
     * @param reqVO 请求参数
     * @return 模考分科目进度响应对象
     */
    public ExamSectionsProgressRespVO getExamSectionsProgress(ExamSectionsProgressReqVO reqVO) {

        Long examId = reqVO.getExamId();
        Long userId = StpUtil.getLoginIdAsLong();

        // 验证模考是否存在且为全真模考类型
        ExamDO examDO = examService.getById(examId);
        if (examDO == null
                || !ExamTypeEnum.FULL_REAL.getCode().equals(examDO.getType())
                || !ExamPublishStatusEnum.PUBLISHED.getCode().equals(examDO.getPublishStatus())) {
            throw exception(EXAM_NOT_EXISTS);
        }

        // 获取模考科目
        List<Integer> examSectionsList = getExamSectionsByExamId(examId);

        // 获取用户在指定模考下的最新练习记录
        Map<Integer, UserExamRecordDO> sectionRecordMap = getUserExamRecordsByExamId(userId, examId);

        // 构建响应对象
        ExamSectionsProgressRespVO progressRespVO = new ExamSectionsProgressRespVO();
        progressRespVO.setExamId(examId);

        List<ExamSectionsDetailProgressRespVO> examSectionsProgressList = examSectionsList
                .stream()
                .map(examSections -> buildSectionProgress(sectionRecordMap.get(examSections), examSections, examId))
                .toList();
        progressRespVO.setExamSectionsProgressList(examSectionsProgressList);

        return progressRespVO;
    }

    /**
     * 获取模考包含的科目列表
     */
    private List<Integer> getExamSectionsByExamId(Long examId) {
        List<Integer> subjectList = examDetailService.lambdaQuery()
                .select(ExamDetailDO::getSubject)
                .eq(ExamDetailDO::getExamId, examId)
                .orderByAsc(ExamDetailDO::getSubject)
                .list()
                .stream()
                .map(ExamDetailDO::getSubject)
                .filter(Objects::nonNull)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        subjectList.add(0, ExamSubjectSectionsEnum.FULL.getCode());

        return subjectList;
    }

    /**
     * 获取用户在指定模考下的最新练习记录
     */
    private Map<Integer, UserExamRecordDO> getUserExamRecordsByExamId(Long userId, Long examId) {
        return userExamRecordService.lambdaQuery()
                .eq(UserExamRecordDO::getUserId, userId)
                .eq(UserExamRecordDO::getExamId, examId)
                .eq(UserExamRecordDO::getIsNewest, 1)
                .orderByDesc(UserExamRecordDO::getId)
                .list()
                .stream()
                .collect(Collectors.toMap(
                        UserExamRecordDO::getExamSections,
                        Function.identity(),
                        (k1, k2) -> k2
                ));
    }

    /**
     * 构建单个科目的进度信息
     *
     * @param userExamRecord 用户模考记录
     * @param examSections   模考科目
     * @param examId         模考ID
     * @return 科目进度详情对象
     */
    private ExamSectionsDetailProgressRespVO buildSectionProgress(UserExamRecordDO userExamRecord, Integer examSections, Long examId) {
        // 如果没有练习记录，返回空状态
        if (userExamRecord == null) {
            return ExamSectionsDetailProgressRespVO.empty(examSections);
        }

        ExamSectionsDetailProgressRespVO progressDetail = new ExamSectionsDetailProgressRespVO();
        progressDetail.setExamId(examId);
        progressDetail.setExamRecordId(userExamRecord.getId());
        progressDetail.setTotalScore(userExamRecord.getTotalScore());
        progressDetail.setActualScore(userExamRecord.getActualScore());
        progressDetail.setProgress(userExamRecord.getProgress() != null ? userExamRecord.getProgress().intValue() : 0);
        progressDetail.setCorrectionStatus(userExamRecord.getCorrectionStatus());
        progressDetail.setPracticeStatus(userExamRecord.getPracticeStatus());
        progressDetail.setExamSections(examSections);

        return progressDetail;
    }

    /**
     * 获取我的模考记录
     *
     * @param reqVO
     * @return
     */
    public PageResult<UserExamRecordAppRespVO> getMyExamRecord(ExamAppPageReqVO reqVO) {
        reqVO.setUserId(StpUtil.getLoginIdAsLong());
        // 获取用户的模考记录
        PageResult<UserExamRecordDO> pageResult = userExamRecordService.getMyExamRecordPage(reqVO);
        if (pageResult.getList() == null || pageResult.getList().isEmpty()) {
            return PageResult.empty();
        }
        List<Long> examIds = pageResult.getList().stream().map(UserExamRecordDO::getExamId).toList();
        List<ExamDO> examDOS = examService.listByIds(examIds);
        Map<Long, ExamDO> examDOMap = examDOS.stream().collect(Collectors.toMap(ExamDO::getId, Function.identity()));
        List<UserExamRecordAppRespVO> voList = new ArrayList<>();
        pageResult.getList().forEach(recordDO -> {
            UserExamRecordAppRespVO vo = new UserExamRecordAppRespVO();
            ExamDO orDefault = examDOMap.getOrDefault(vo.getExamId(), null);
            if (orDefault != null) {
                vo.setExamName(orDefault.getName());
            }
            vo.setRecordId(recordDO.getId());
            vo.setExamId(recordDO.getExamId());
            vo.setHskLevel(recordDO.getHskLevel());
            vo.setExamType(recordDO.getExamType());
            vo.setExamTypeDesc(ExamTypeEnum.getDescByCode(recordDO.getExamType()));
            vo.setExamSections(recordDO.getExamSections());
            vo.setExamSectionsDesc(ExamSubjectSectionsEnum.getDescByCode(recordDO.getExamSections()));
            vo.setTotalScore(recordDO.getTotalScore());
            vo.setActualScore(recordDO.getActualScore());
            vo.setEndTime(recordDO.getEndTime());
            vo.setCorrectionStatus(recordDO.getCorrectionStatus());
            vo.setPracticeStatus(recordDO.getPracticeStatus());
            vo.setProgress(recordDO.getProgress());

            voList.add(vo);
        });
        return new PageResult<>(voList, pageResult.getTotal());
    }
}