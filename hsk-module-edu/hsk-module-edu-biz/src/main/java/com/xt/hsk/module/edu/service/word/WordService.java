package com.xt.hsk.module.edu.service.word;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.edu.dal.dataobject.word.WordDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.dal.dataobject.word.WordMeaningDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordTagDO;
import jakarta.validation.Valid;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordPageReqVO;

import java.util.List;

/**
 * 汉语词典基础数据 Service 接口
 *
 * <AUTHOR>
 */
public interface WordService extends IService<WordDO> {
    PageResult<WordDO> selectPage(@Valid WordPageReqVO pageReqVO);

    List<WordMeaningDO> getWordsMeaningsByWordIds(List<Long> wordIds);

    List<WordTagDO> getWordsTagsByWordIds(List<Long> wordIds);

    Long dataTotalCount(WordPageReqVO dto);

    List<WordDO> getWordByName(String name);
}