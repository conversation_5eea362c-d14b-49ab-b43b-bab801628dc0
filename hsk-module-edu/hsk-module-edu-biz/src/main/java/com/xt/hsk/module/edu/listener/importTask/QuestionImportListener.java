package com.xt.hsk.module.edu.listener.importTask;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.QUESTION_IMPORT_ERROR;

import cn.hutool.core.collection.CollectionUtil;
import cn.idev.excel.context.AnalysisContext;
import com.alibaba.fastjson.JSONObject;
import com.anji.captcha.util.StringUtils;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.module.edu.controller.admin.question.vo.OptionContentVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitTextBookVO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionExcelDO;
import com.xt.hsk.module.edu.service.question.QuestionManager;
import com.xt.hsk.module.edu.service.unit.UnitService;
import com.xt.hsk.module.infra.listener.BaseAnalysisEventListener;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class QuestionImportListener extends BaseAnalysisEventListener<QuestionExcelDO> {


    private final QuestionManager questionManager;

    private final UnitService unitService;

    private final Map<String, Long> questionTypeMap;

    private  QuestionSaveReqVO questionSaveReqVO;

    int sort=1;


    private final ConcurrentHashMap<String, UnitTextBookVO> dataCache = new ConcurrentHashMap<>();


    public QuestionImportListener(UnitService unitService, Map<String, Long> questionTypeMap, QuestionManager questionManager) {
        this.unitService = unitService;
        this.questionTypeMap = questionTypeMap;
        this.questionManager = questionManager;
    }

    @Override
    public void invoke(QuestionExcelDO questionExcelDO, AnalysisContext context) {
        // 行号(从1开始，去掉表头就是2)
        int rowIndex = context.readRowHolder().getRowIndex() + 1;
        try {
            // 通过名称查询数据
            String textbookNameCn = questionExcelDO.getTextbookNameCn();
            String chapterNameCn = questionExcelDO.getChapterNameCn();
            String unitNameCn = questionExcelDO.getUnitNameCn();
            // 判断是否是上一篇材料
            if (StringUtils.isNotBlank(textbookNameCn)){
                String typeNameCn = questionExcelDO.getTypeNameCn();
                String subjectName = questionExcelDO.getSubjectName();
                String typeKey= subjectName+"_"+typeNameCn;
                SubjectEnum subjectEnum = SubjectEnum.getByDesc(subjectName);
                if (subjectEnum == null) {
                    throw new ServiceException(QUESTION_IMPORT_ERROR.getCode(),"不存在对应的学科数据");
                }
                if (subjectName == null) {
                    throw new ServiceException(QUESTION_IMPORT_ERROR.getCode(),"不存在对应的学科数据");
                }
                Long questionTypeId = questionTypeMap.get(typeKey);
                if (questionTypeId == null){
                    throw new ServiceException(QUESTION_IMPORT_ERROR.getCode(),"不存在对应的题型数据");
                }
                sort=1;
                if (questionSaveReqVO!=null){
                    VALID_COUNT++;
                    questionManager.createQuestion(questionSaveReqVO);
                    log.info("处理第{}行数据,数据为{}", rowIndex, JSONObject.toJSONString(questionSaveReqVO));

                }
                // 证明是个新材料
                questionSaveReqVO=new QuestionSaveReqVO();
                // 先插入上一篇材料数据
                // 新材料
                // 获取题型id

                UnitTextBookVO unitDataWithCache = getUnitDataWithCache(unitNameCn, chapterNameCn, textbookNameCn,subjectName);
                // 获取单元id
                Long unitId = unitDataWithCache.getId();
                Long chapterId = unitDataWithCache.getChapterId();
                Long textbookId = unitDataWithCache.getTextbookId();
                Integer hskLevel = questionExcelDO.getHskLevel();
                if (hskLevel == null || hskLevel>7){
                    throw new ServiceException(QUESTION_IMPORT_ERROR.getCode(),"请填写HSK等级");
                }
                if (hskLevel==0){
                    questionSaveReqVO.setHskLevel(hskLevel);
                }else {
                    questionSaveReqVO.setHskLevel(1 << (hskLevel - 1));
                }
                questionSaveReqVO.setTextbookId(textbookId);
                questionSaveReqVO.setChapterId(chapterId);
                questionSaveReqVO.setUnitId(unitId);
                questionSaveReqVO.setTypeId(questionTypeId);
                questionSaveReqVO.setSubject(subjectEnum.code);
                questionSaveReqVO.setMaterialAudio(questionExcelDO.getMaterialAudio());
                questionSaveReqVO.setMaterialImage(questionExcelDO.getMaterialImage());
                questionSaveReqVO.setMaterialContent(questionExcelDO.getMaterialContent());
                // 拼接材料的options数据
                // 拼接成这样 [{"key": "A", "content": "材料选项A"}, {"key": "B", "content": "材料选项B"}, {"key": "C", "content": "材料选项C"}, {"key": "D", "content": "材料选项D"}]
                questionSaveReqVO.setOptionsContent( convertToJson(questionExcelDO));
                List<QuestionDetailSaveReqVO> questionDetails=new ArrayList<>();
                questionSaveReqVO.setQuestionDetails(questionDetails);
            }
            if (questionSaveReqVO==null){
                throw new ServiceException(QUESTION_IMPORT_ERROR.getCode(),"材料创建失败，小题不导入");
            }
            // 创建详情
            List<QuestionDetailSaveReqVO> details = questionSaveReqVO.getQuestionDetails();
            QuestionDetailSaveReqVO detailSaveReqVO = new QuestionDetailSaveReqVO();
            details.add(detailSaveReqVO);
            detailSaveReqVO.setAnswer(questionExcelDO.getAnswer());
            detailSaveReqVO.setAttachmentAudio(questionExcelDO.getAttachmentAudio());
            detailSaveReqVO.setAttachmentImage(questionExcelDO.getAttachmentImage());
            detailSaveReqVO.setAttachmentContent(questionExcelDO.getAttachmentContent());
            detailSaveReqVO.setOptionsContent(convertDeteilToJson(questionExcelDO));
            detailSaveReqVO.setExplainTextCn(questionExcelDO.getExplainTextCn());
            detailSaveReqVO.setExplainTextEn(questionExcelDO.getExplainTextEn());
            detailSaveReqVO.setExplainTextOt(questionExcelDO.getExplainTextOt());
            detailSaveReqVO.setExplainAudio(questionExcelDO.getExplainAudio());
            detailSaveReqVO.setExplainVideo(questionExcelDO.getExplainVideo());
            detailSaveReqVO.setSort(sort++);

        } catch (Exception e) {
            questionSaveReqVO=null;
            // 记录未预期的异常
            log.error("处理第{}行数据时发生异常", rowIndex, e);
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, e.getMessage()));
            INVALID_COUNT++;
            SUCCESS = false;
        }
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 处理最后一行
        if (questionSaveReqVO!=null){
            VALID_COUNT++;
            questionManager.createQuestion(questionSaveReqVO);
        }


    }

    /**
     * 带缓存的查询方法：先查缓存，不存在时查数据库并缓存结果
     */
    public UnitTextBookVO getUnitDataWithCache(String unitNameCn, String chapterNameCn, String textbookNameCn,String subjectName) {
        // 1. 生成缓存键（参数组合，需处理 null 避免 NPE）
        String cacheKey = generateCacheKey(unitNameCn, chapterNameCn, textbookNameCn, subjectName);

        // 2. 检查缓存是否存在
        UnitTextBookVO cachedData = dataCache.get(cacheKey);
        if (cachedData != null) {
            return cachedData;
        }
        Integer code = SubjectEnum.getByDesc(subjectName).getCode();
        // 3. 缓存不存在时查询数据库
        List<UnitTextBookVO> unitTextBookVOS = unitService.queryUnitUnitData(unitNameCn, chapterNameCn, textbookNameCn,code);
        if(CollectionUtil.isEmpty(unitTextBookVOS)){
            throw new ServiceException(QUESTION_IMPORT_ERROR.getCode(),"不存在对应的章节单元数据");
        }
        if (unitTextBookVOS.size() >1){
            throw new ServiceException(QUESTION_IMPORT_ERROR.getCode(),"存在多个相同名教材单元章节数据");
        }
        // 4. 将结果存入缓存（注意：若数据库返回 null，是否缓存需根据业务决定）
        UnitTextBookVO unitTextBookVO = unitTextBookVOS.get(0);
        dataCache.put(cacheKey, unitTextBookVO);
        return unitTextBookVO;
    }


    /**
     * 生成缓存键（拼接参数，处理 null 为空字符串）
     */
    private String generateCacheKey(String... params) {
        StringBuilder keyBuilder = new StringBuilder();
        for (String param : params) {
            keyBuilder.append(param != null ? param : "").append("_");
        }
        return keyBuilder.toString().trim(); // 示例："语文_Chinese_其他_"
    }

    public static List<OptionContentVO> convertToJson(QuestionExcelDO questionExcelDO){
        List<OptionContentVO> optionList = new ArrayList<>();

        // 处理选项 A（必须存在，示例中未过滤）
        addIfNotEmpty(optionList, "A", questionExcelDO.getMaterialOptionA());
        addIfNotEmpty(optionList, "B", questionExcelDO.getMaterialOptionB());
        addIfNotEmpty(optionList, "C", questionExcelDO.getMaterialOptionC());
        addIfNotEmpty(optionList, "D", questionExcelDO.getMaterialOptionD());
        addIfNotEmpty(optionList, "E", questionExcelDO.getMaterialOptionE());  // E 可选
        return optionList;
    }

    public static List<OptionContentVO> convertDeteilToJson(QuestionExcelDO questionExcelDO){
        List<OptionContentVO> optionList = new ArrayList<>();
        // 处理选项 A（必须存在，示例中未过滤）
        addIfNotEmpty(optionList, "A", questionExcelDO.getDetailOptionA());
        addIfNotEmpty(optionList, "B", questionExcelDO.getDetailOptionB());
        addIfNotEmpty(optionList, "C", questionExcelDO.getDetailOptionC());
        addIfNotEmpty(optionList, "D", questionExcelDO.getDetailOptionD());
        addIfNotEmpty(optionList, "E", questionExcelDO.getDetailOptionE());
        addIfNotEmpty(optionList, "F", questionExcelDO.getDetailOptionF());
        addIfNotEmpty(optionList, "G", questionExcelDO.getDetailOptionG());
        return optionList;
        // E 可选
    }

    private static void addIfNotEmpty(List<OptionContentVO> list, String key, String content) {
        if (content != null && !content.trim().isEmpty()) {
            OptionContentVO optionContentVO = new OptionContentVO();
            optionContentVO.setKey(key);
            optionContentVO.setContent(content);
            list.add(optionContentVO);
        }
    }
}
