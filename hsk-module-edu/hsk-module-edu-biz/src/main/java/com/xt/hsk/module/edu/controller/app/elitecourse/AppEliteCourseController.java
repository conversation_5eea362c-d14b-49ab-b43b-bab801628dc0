package com.xt.hsk.module.edu.controller.app.elitecourse;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fhs.core.trans.anno.TransMethodResult;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.*;
import com.xt.hsk.module.edu.manager.elitecourse.EliteCourseAppManager;
import com.xt.hsk.module.edu.manager.elitecourse.EliteCourseCategoryAppManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * APP - 精品课 Controller
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@RestController
@RequestMapping("/edu/elite-course")
public class AppEliteCourseController {

    @Resource
    private EliteCourseAppManager eliteCourseAppManager;

    @Resource
    private EliteCourseCategoryAppManager eliteCourseCategoryAppManager;

    /**
     * 获取精品课分类列表
     *
     * @param hskLevel HSK等级，可选参数
     * @return 精品课分类列表
     */
    @SaIgnore
    @GetMapping("/category/list")
    public CommonResult<List<EliteCourseCategoryAppRespVO>> getEliteCourseCategoryList(
        @RequestParam(value = "hskLevel", required = false) Integer hskLevel) {
        List<EliteCourseCategoryAppRespVO> categories = eliteCourseCategoryAppManager.getShowCategories(
            hskLevel);
        return CommonResult.success(categories);
    }

    /**
     * 获取按HSK等级分组的精品课分类列表
     *
     * @return HSK等级分类列表
     */
    @SaIgnore
    @TransMethodResult
    @GetMapping("/hsk/category/list")
    public CommonResult<List<HskLevelCategoryVO>> getHskLevelCategoryList() {
        // 获取HSK等级分类列表
        List<HskLevelCategoryVO> hskLevelCategories = eliteCourseCategoryAppManager.getHskLevelCategories();
        return CommonResult.success(hskLevelCategories);
    }

    /**
     * 获取精品课分页列表
     *
     * @param pageVO 分页请求参数
     * @return 精品课分页信息
     */
    @SaIgnore
    @PostMapping("/page")
    public CommonResult<PageResult<EliteCourseAppRespVO>> getEliteCoursePage(
        @Valid @RequestBody EliteCourseAppPageReqVO pageVO) {
        // 获取课程分页
        PageResult<EliteCourseAppRespVO> pageResult = eliteCourseAppManager.getEliteCoursePage(pageVO);
        return CommonResult.success(pageResult);
    }

    /**
     * 获取精品课详情
     *
     * @param id 精品课ID
     * @return 精品课详情
     */
    @SaIgnore
    @TransMethodResult
    @GetMapping("/detail/{id}")
    public CommonResult<EliteCourseDetailRespVO> getEliteCourseDetail(@PathVariable("id") Long id) {
        // 获取课程详情
        EliteCourseDetailRespVO detail = eliteCourseAppManager.getEliteCourseDetail(id);
        return CommonResult.success(detail);
    }

    /**
     * 个人中心我的课程
     */
    @PostMapping("/v1/myCourse")
    public CommonResult<PageResult<UserCenterCourseVo>> getMyCourse(@RequestBody EliteCourseAppPageReqVO pageVO) {
        return CommonResult.success(eliteCourseAppManager.getMyCourse(pageVO));
    }

    /**
     * 个人中心获取章节信息
     */
    @PostMapping("/v1/getCourseChapterInfo")
    public CommonResult<List<EliteCourseDetailRespVO.ChapterVO>> getCourseChapterInfo(@RequestBody EliteCourseAppPageReqVO pageVO) {
        return CommonResult.success(eliteCourseAppManager.getCourseChapterInfo(pageVO.getCourseId()));
    }

    /**
     * 个人中心获取学习记录
     */
    @PostMapping("/v1/getStudyRecord")
    public CommonResult<AppEliteCourseStudyRecordRespVO> getStudyRecord(@RequestBody EliteCourseAppPageReqVO pageVO) {
        return CommonResult.success(eliteCourseAppManager.getStudyRecord(pageVO));
    }

    /**
     * 保存用户作答记录
     */
    @PostMapping("/v1/saveStudyRecord")
    public CommonResult<Long> saveStudyRecord(@RequestBody AppEliteCourseStudyRecordSaveReqVO reqVO) {
        return CommonResult.success(eliteCourseAppManager.saveStudyRecord(reqVO));
    }
} 