package com.xt.hsk.module.edu.service.course;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCourseRespVO;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.EliteCourseDetailRespVO.RecommendedCourseVO;
import com.xt.hsk.module.edu.dal.dataobject.course.RecommendedCourseDO;
import com.xt.hsk.module.edu.dal.mysql.course.RecommendedCourseMapper;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 课程推荐 Service 实现类
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Service
@Validated
public class RecommendedCourseServiceImpl extends
    ServiceImpl<RecommendedCourseMapper, RecommendedCourseDO> implements RecommendedCourseService {

    @Resource
    private RecommendedCourseMapper recommendedCourseMapper;

    @Override
    public PageResult<RecommendedCourseDO> getRecommendedCoursePage(
        RecommendedCoursePageReqVO pageReqVO) {
        return recommendedCourseMapper.selectPage(pageReqVO);
    }

    @Override
    public Integer getMaxSortByHskLevel(Integer hskLevel) {
        Integer maxSort = recommendedCourseMapper.selectMaxSortByHskLevel(hskLevel);
        return maxSort != null ? maxSort : 0;
    }

    @Override
    public List<RecommendedCourseVO> selectRecommendedCoursesByHskLevel(Integer hskLevel,
        Long notCourseId) {
        return recommendedCourseMapper.selectRecommendedCoursesByHskLevel(hskLevel, notCourseId);
    }

    @Override
    public PageResult<RecommendedCourseRespVO> selectJoinPage(RecommendedCoursePageReqVO reqVO) {
        return recommendedCourseMapper.selectJoinPage(reqVO);
    }
    
    @Override
    public void updateSortInRange(Integer hskLevel, Integer startSort, Integer endSort, Integer increment) {
        recommendedCourseMapper.updateSortInRange(hskLevel, startSort, endSort, increment);
    }
} 