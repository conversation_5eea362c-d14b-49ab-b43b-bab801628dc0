package com.xt.hsk.module.edu.service.elitecourse;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRegisterUserPageReqVO;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.EliteCourseAppPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseTeacherDO;
import com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteCourseMapper;
import com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteCourseRegisterMapper;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingMethodEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.ELITE_COURSE_NOT_EXISTS;

/**
 * 精品课 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Slf4j
@Service
public class EliteCourseServiceImpl extends ServiceImpl<EliteCourseMapper, EliteCourseDO> implements EliteCourseService {

    @Resource
    private EliteCourseMapper eliteCourseMapper;

    @Resource
    private EliteCourseRegisterMapper eliteCourseRegisterMapper;

    @Override
    public PageResult<EliteCourseDO> selectPage(EliteCoursePageReqVO pageReqVO) {

        // 讲师课程分页
        if (pageReqVO.getTeacherId() != null) {
            return getCourseByTeacher(pageReqVO);
        }

        return eliteCourseMapper.selectPage(pageReqVO);
    }

    /**
     * 数据总数
     *
     * @param pageReqVO 分页查询
     * @return 数据总数
     */
    @Override
    public Long dataTotalCount(EliteCoursePageReqVO pageReqVO) {
        if (pageReqVO == null) {
            return eliteCourseMapper.selectCount();
        }

        // 讲师课程数量
        if (pageReqVO.getTeacherId() != null) {
            return countCourseByTeacher(pageReqVO);
        }

        LambdaQueryWrapperX<EliteCourseDO> query = new LambdaQueryWrapperX<EliteCourseDO>();

        // 如果有选中的ID，优先使用ID列表查询，忽略其他条件
        if (CollUtil.isNotEmpty(pageReqVO.getIds())) {
            query.in(EliteCourseDO::getId, pageReqVO.getIds());
        } else {
            // 没有选中ID时，使用其他查询条件
            query.likeIfPresent(EliteCourseDO::getCourseNameCn, pageReqVO.getCourseNameCn())
                    .eqIfPresent(EliteCourseDO::getHskLevel, pageReqVO.getHskLevel())
                    .eqIfPresent(EliteCourseDO::getPrimaryCategoryId, pageReqVO.getPrimaryCategoryId())
                    .eqIfPresent(EliteCourseDO::getListingStatus, pageReqVO.getListingStatus())
                    .eqIfPresent(EliteCourseDO::getIsShow, pageReqVO.getIsShow());
        }

        return eliteCourseMapper.selectCount(query);
    }

    @Override
    public void updateCourseRegisterCount(Long courseId) {
        EliteCourseRegisterUserPageReqVO reqVO = new EliteCourseRegisterUserPageReqVO();
        reqVO.setCourseId(courseId);
        Long counted = eliteCourseRegisterMapper.countCourseRegisterUser(reqVO);
        this.lambdaUpdate()
            .set(EliteCourseDO::getEnrollmentCount, counted)
            .eq(EliteCourseDO::getId, courseId)
            .update();
    }

    @Override
    public EliteCourseDO getEliteCourse(Long id) {
        EliteCourseDO course = eliteCourseMapper.selectById(id);
        if (course == null) {
            throw exception(ELITE_COURSE_NOT_EXISTS);
        }
        return course;
    }

    @Override
    public PageResult<EliteCourseDO> getEliteCoursePage(EliteCourseAppPageReqVO pageReqVO) {
        // 如果有hsk等级 需要查询出对应的分类ID 根据分类ID过滤

        Page<EliteCourseDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        LambdaQueryWrapper<EliteCourseDO> queryWrapper = new LambdaQueryWrapper<EliteCourseDO>()
            .eq(pageReqVO.getCategoryId() != null, EliteCourseDO::getPrimaryCategoryId,
                pageReqVO.getCategoryId())
            // 已上架
            .eq(EliteCourseDO::getListingStatus, 1)
            // 展示
            .eq(EliteCourseDO::getIsShow, 1)
            .eq(pageReqVO.getHskLevel() != null, EliteCourseDO::getHskLevel,
                pageReqVO.getHskLevel())
            .orderByAsc(EliteCourseDO::getSort)
            .orderByDesc(EliteCourseDO::getId);
        Page<EliteCourseDO> selectPage = eliteCourseMapper.selectPage(page, queryWrapper);
        return new PageResult<>(selectPage.getRecords(), selectPage.getTotal());
    }


    /**
     * 根据教师id查询其关联的精品课程列表
     *
     * @param pageReqVO 分页参数
     * @return 分页结果
     */
    private PageResult<EliteCourseDO> getCourseByTeacher(EliteCoursePageReqVO pageReqVO) {
        MPJLambdaWrapper<EliteCourseDO> queryWrapper = buildCourseTeacherQueryWrapper(pageReqVO);
        return eliteCourseMapper.selectJoinPage(pageReqVO, EliteCourseDO.class, queryWrapper);
    }

    /**
     * 根据教师id统计其关联的精品课程数量
     *
     * @param pageReqVO 分页参数
     * @return 课程数量
     */
    private Long countCourseByTeacher(EliteCoursePageReqVO pageReqVO) {
        MPJLambdaWrapper<EliteCourseDO> queryWrapper = buildCourseTeacherQueryWrapper(pageReqVO);
        return eliteCourseMapper.selectJoinCount(queryWrapper);
    }

    /**
     * 构建用于根据教师id筛选精品课程的查询条件封装器
     *
     * @param pageReqVO 请求参数，包含教师ID和多个筛选字段
     * @return 查询条件封装器 MPJLambdaWrapper
     */
    private MPJLambdaWrapper<EliteCourseDO> buildCourseTeacherQueryWrapper(EliteCoursePageReqVO pageReqVO) {
        return new MPJLambdaWrapperX<EliteCourseDO>()
                .leftJoin(EliteCourseTeacherDO.class, EliteCourseTeacherDO::getEliteCourseId, EliteCourseDO::getId)
                .eq(EliteCourseTeacherDO::getTeacherId, pageReqVO.getTeacherId())
                .eq(EliteCourseTeacherDO::getDeleted, false)
                .eq(EliteCourseDO::getDeleted, false)
                .likeIfPresent(EliteCourseDO::getCourseNameCn, pageReqVO.getCourseNameCn())
                .eqIfPresent(EliteCourseDO::getHskLevel, pageReqVO.getHskLevel())
                .eqIfPresent(EliteCourseDO::getPrimaryCategoryId, pageReqVO.getPrimaryCategoryId())
                .eqIfPresent(EliteCourseDO::getListingStatus, pageReqVO.getListingStatus())
                .eqIfPresent(EliteCourseDO::getIsShow, pageReqVO.getIsShow())
            .orderByAsc(EliteCourseDO::getId);
    }

    /**
     * 定时任务上架精品课程
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer releaseEliteCourse() {
        int updateCount = 0;
        int batchSize = 500;
        Long lastId = null;
        while (true) {
            List<EliteCourseDO> courseList = lambdaQuery()
                    .eq(EliteCourseDO::getListingMethod, EliteCourseListingMethodEnum.SCHEDULED.getCode())
                    .eq(EliteCourseDO::getListingStatus, EliteCourseListingStatusEnum.PENDING.getCode())
                    .le(EliteCourseDO::getListingTime, LocalDateTime.now())
                    .gt(Objects.nonNull(lastId), EliteCourseDO::getId, lastId)
                    .orderByAsc(EliteCourseDO::getId)
                    .last("LIMIT " + batchSize)
                    .list();

            if (CollUtil.isEmpty(courseList)) {
                break;
            }

            updateCount += courseList.size();

            List<Long> idList = courseList.stream().map(EliteCourseDO::getId).toList();
            lambdaUpdate()
                    .set(EliteCourseDO::getListingStatus, EliteCourseListingStatusEnum.LISTED.getCode())
                    .in(EliteCourseDO::getId, idList)
                    .update();

            lastId = courseList.get(courseList.size() - 1).getId();
        }
        return updateCount;
    }



}