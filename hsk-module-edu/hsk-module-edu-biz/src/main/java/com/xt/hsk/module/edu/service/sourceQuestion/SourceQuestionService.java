package com.xt.hsk.module.edu.service.sourceQuestion;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.sourceQuestion.SourceQuestionDO;
import jakarta.validation.Valid;

/**
 * 题目 Service 接口
 *
 * <AUTHOR>
 */
public interface SourceQuestionService extends IService<SourceQuestionDO> {
    PageResult<SourceQuestionDO> selectPage(@Valid QuestionPageReqVO pageReqVO);
}