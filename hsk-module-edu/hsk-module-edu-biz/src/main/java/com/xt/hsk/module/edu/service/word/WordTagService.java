package com.xt.hsk.module.edu.service.word;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.tag.vo.TagPageReqVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordTagPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordTagDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 词语标签 Service 接口
 *
 * <AUTHOR>
 */
public interface WordTagService extends IService<WordTagDO> {
    PageResult<WordTagDO> selectPage(@Valid WordTagPageReqVO pageReqVO);

    void removeByWordId(Long wordId);

    List<WordTagDO> getByWordId(Long id);

    PageResult<WordTagDO> getWordTagPageByTagIds(TagPageReqVO tagPageReqVO);

    /**
     * 根据词汇ID列表批量获取标签
     *
     * @param wordIds 词汇ID列表
     * @return 标签列表
     */
    List<WordTagDO> getByWordIds(List<Long> wordIds);
}