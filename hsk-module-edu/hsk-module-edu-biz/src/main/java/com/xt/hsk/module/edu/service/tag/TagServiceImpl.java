package com.xt.hsk.module.edu.service.tag;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.tag.vo.TagPageReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.Valid;


import com.xt.hsk.module.edu.dal.dataobject.tag.TagDO;

import com.xt.hsk.module.edu.dal.mysql.tag.TagMapper;

import java.util.Collection;
import java.util.List;


/**
 * 标签 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TagServiceImpl extends ServiceImpl<TagMapper, TagDO> implements TagService {

    @Resource
    private TagMapper tagMapper;

    @Override
    public PageResult<TagDO> selectPage(TagPageReqVO pageReqVO) {

        return tagMapper.selectPage(pageReqVO);
    }

    @Override
    public Collection<String> getAllTagName() {

        return tagMapper.getAllTagName();
    }

}