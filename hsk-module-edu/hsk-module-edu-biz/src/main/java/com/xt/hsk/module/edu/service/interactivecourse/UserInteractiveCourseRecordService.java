package com.xt.hsk.module.edu.service.interactivecourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.UserInteractiveCourseRecordDO;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseRecordBizTypeEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseStatusEnum;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 互动课-用户学习记录 Service 接口
 *
 * <AUTHOR>
 * @since 2025/06/11
 */
public interface UserInteractiveCourseRecordService extends IService<UserInteractiveCourseRecordDO> {

    /**
     * 获取互动课-用户学习记录
     *
     * @param unitId      互动课-单元ID
     * @param userId      用户id
     * @param bizTypeEnum 业务类型
     * @return 互动课-用户学习记录
     */
    UserInteractiveCourseRecordDO getInteractiveCourseRecord(Long unitId, long userId, InteractiveCourseRecordBizTypeEnum bizTypeEnum);

    /**
     * 查询单元视频是否曾经看完
     */
    Boolean checkVideoCompletion(Long unitId, long userId,Integer version);

    /**
     * 创建业务关联记录
     *
     * @param userId                      用户 ID
     * @param unitId                      单元 ID
     * @param bizType                     业务类型
     * @param interactiveCourseStatusEnum 课程记录状态枚举
     * @param bizId                       业务 ID
     * @param gameId                      专项练习 ID 只有业务关联是专项练习时才会有
     * @param questionIds                 题目 ID list 只有业务关联是真题时才会有
     */
    void createBizRecord(Long userId,
        Long unitId,
        InteractiveCourseRecordBizTypeEnum bizType,
        InteractiveCourseStatusEnum interactiveCourseStatusEnum,
        Long bizId,
        Long gameId,
        List<Long> questionIds);

    /**
     * 创建视频记录
     *
     * @param userId          用户 ID
     * @param unitId          单元 ID
     * @param videoDuration   视频时长
     * @param viewingDuration 观看时长
     * @param saveOption      保存选项
     * @param videoProgress   视频进度
     */
    void createVideoRecord(Long userId, Long courseId, Long unitId, Integer videoDuration, Integer viewingDuration,
        @NotNull Integer saveOption, BigDecimal videoProgress);

    /**
     * 更新记录状态和详情
     *
     * @param userId             用户 ID
     * @param unitId             单元 ID
     * @param bizType            业务类型
     * @param bizId              业务ID
     * @param aiCorrectionStatus AI批改状态
     * @param accuracy           正确率
     */
    void updateRecordStatusAndDetails(Long userId, Long unitId, InteractiveCourseRecordBizTypeEnum bizType,
        Long bizId, Integer aiCorrectionStatus,
        BigDecimal accuracy);

    /**
     * 根据单元ID列表获取单元学习记录 取的是最新的记录
     *
     * @param userId  用户 ID
     * @param unitIds 设备 ID
     * @return Map<单元ID, 单元学习记录(最新一条)>
     */
    Map<Long, UserInteractiveCourseRecordDO> getLatestRecordByUnitIds(Long userId, List<Long> unitIds);

    /**
     * 根据课程ID取用户学习的单元数量
     * 只查状态为显示的单元
     * @param userId    用户 ID
     * @param courseIds 课程 ID
     * @return Map<课程ID, 进度为已完成的单元数量>
     */
    Map<Long, Long> getCompletedUnitCountByCourseIds(Long userId, List<Long> courseIds);

    /**
     * 批量检查用户是否曾经完成过视频观看（进度100%）
     * 用于视频类型单元的学习状态判断
     *
     * @param userId 用户ID
     * @param unitIds 单元ID列表
     * @return Map<单元ID, 最高完成的资源版本号>，如果从未完成则不包含该单元ID
     */
    Map<Long, Integer> batchGetVideoCompletionResourceVersion(Long userId, List<Long> unitIds);

    /**
     * 统计学习过指定课程任意单元的去重用户数
     *
     * @param courseId 课程ID
     * @param unitIds  单元ID列表
     * @return 去重用户数
     */
    Long countDistinctUsersByCourseAndUnits(Long courseId, List<Long> unitIds);
}

