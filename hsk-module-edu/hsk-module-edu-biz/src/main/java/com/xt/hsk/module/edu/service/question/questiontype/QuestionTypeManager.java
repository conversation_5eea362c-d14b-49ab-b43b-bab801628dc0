package com.xt.hsk.module.edu.service.question.questiontype;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.common.util.BinaryUtils;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypeCount;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypePageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypeRespVO;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypeSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypeTreeVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiontype.QuestionTypeDO;
import com.xt.hsk.module.edu.service.question.QuestionService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;


/**
 * 题型 Manager
 *
 * <AUTHOR>
 */
@Service
public class QuestionTypeManager {

    @Resource
    private QuestionTypeService questionTypeService;
    @Resource
    private QuestionService questionService;


    public Long createQuestionType(QuestionTypeSaveReqVO createReqVO) {
        // 插入
        // 校验重名问题
        LambdaQueryWrapper<QuestionTypeDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionTypeDO::getNameCn, createReqVO.getNameCn());
        if (questionTypeService.count(queryWrapper) > 0) {
            throw exception(500, "该题型已存在，请修改后再确认");
        }
        QuestionTypeDO questionType = BeanUtils.toBean(createReqVO, QuestionTypeDO.class);
        Integer hsklevel = BinaryUtils.getArrayNum(createReqVO.getHskLevels());
        questionType.setHskLevel(hsklevel);
        questionTypeService.save(questionType);

        // 返回
        return questionType.getId();
    }


    public void updateQuestionType(QuestionTypeSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionTypeExists(updateReqVO.getId());
        // 校验重名问题
        LambdaQueryWrapper<QuestionTypeDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionTypeDO::getNameCn, updateReqVO.getNameCn());
        queryWrapper.ne(QuestionTypeDO::getId, updateReqVO.getId());
        queryWrapper.eq(QuestionTypeDO::getSubject, updateReqVO.getSubject());
        if (questionTypeService.count(queryWrapper) > 0) {
            throw exception(500, "该题型已存在，请修改后再确认");
        }
        // 更新
        QuestionTypeDO updateObj = BeanUtils.toBean(updateReqVO, QuestionTypeDO.class);
        Integer hsklevel = BinaryUtils.getArrayNum(updateReqVO.getHskLevels());
        updateObj.setHskLevel(hsklevel);
        questionTypeService.updateById(updateObj);
    }


    public void deleteQuestionType(Long id) {
        // 校验存在
        validateQuestionTypeExists(id);
        // 删除
        questionTypeService.removeById(id);
    }

    private void validateQuestionTypeExists(Long id) {
        if (questionTypeService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public QuestionTypeRespVO getQuestionType(Long id) {
        QuestionTypeDO typeDO = questionTypeService.getById(id);
        QuestionTypeRespVO respVO = BeanUtils.toBean(typeDO, QuestionTypeRespVO.class);
        respVO.setHskLevels(BinaryUtils.getBinaryBitValues(typeDO.getHskLevel()));
        return respVO;
    }

    public List<QuestionTypeRespVO> getQuestionTypeList(@Valid QuestionTypePageReqVO pageReqVO) {
        List<QuestionTypeDO> questionTypeList = questionTypeService.getQuestionTypeList(pageReqVO);
        List<QuestionTypeRespVO> questionTypeRespVOS = BeanUtils.toBean(questionTypeList, QuestionTypeRespVO.class);
        Set<Long> questionTypeIds = questionTypeRespVOS.stream().map(QuestionTypeRespVO::getId).collect(Collectors.toSet());
        // 查询总题数
        List<QuestionTypeCount> questionsCount = questionService.countBySubjectAndQuestionTypeIds(pageReqVO.getSubject(), questionTypeIds);
        Map<Long, Integer> countMap = questionsCount.stream().collect(Collectors.toMap(QuestionTypeCount::getTypeId, QuestionTypeCount::getCount));
        for (QuestionTypeRespVO questionTypeRespVO : questionTypeRespVOS) {
            questionTypeRespVO.setQuestionNumber(countMap.get(questionTypeRespVO.getId()));
            questionTypeRespVO.setHskLevels(BinaryUtils.getBinaryBitValues(questionTypeRespVO.getHskLevel()));
        }

        return questionTypeRespVOS;
    }

    public List<QuestionTypeRespVO> getAllQuestionType() {
        LambdaQueryWrapper<QuestionTypeDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionTypeDO::getDeleted, false);
        queryWrapper.orderByAsc(QuestionTypeDO::getHskLevel);
        List<QuestionTypeDO> questionTypeList = questionTypeService.list(queryWrapper);

        return BeanUtils.toBean(questionTypeList, QuestionTypeRespVO.class);
    }

    /**
     * 获取题型的树形结构，按科目分组
     *
     * @return 题型树形结构列表
     */
    public List<QuestionTypeTreeVO> getQuestionTypeTree() {
        // 获取所有题型
        LambdaQueryWrapper<QuestionTypeDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionTypeDO::getDeleted, false);
        queryWrapper.orderByAsc(QuestionTypeDO::getSubject).orderByAsc(QuestionTypeDO::getId);
        List<QuestionTypeDO> questionTypes = questionTypeService.list(queryWrapper);

        // 按科目分组
        Map<Integer, List<QuestionTypeDO>> subjectTypeMap = questionTypes.stream()
                .filter(type -> type.getSubject() != null)
                .collect(Collectors.groupingBy(QuestionTypeDO::getSubject));

        // 构建树形结构
        List<QuestionTypeTreeVO> result = new ArrayList<>();
        for (SubjectEnum subject : SubjectEnum.values()) {
            QuestionTypeTreeVO treeVO = new QuestionTypeTreeVO();
            treeVO.setSubjectCode(subject.getCode());
            treeVO.setSubjectName(subject.getDesc());

            // 构建当前科目的题型列表
            List<QuestionTypeTreeVO.QuestionTypeItem> types = new ArrayList<>();
            List<QuestionTypeDO> subjectTypes = subjectTypeMap.getOrDefault(subject.getCode(), new ArrayList<>());

            for (QuestionTypeDO typeDO : subjectTypes) {
                QuestionTypeTreeVO.QuestionTypeItem item = new QuestionTypeTreeVO.QuestionTypeItem();
                item.setId(typeDO.getId());
                item.setName(typeDO.getNameCn());
                types.add(item);
            }

            treeVO.setTypes(types);
            result.add(treeVO);
        }

        return result;
    }
}