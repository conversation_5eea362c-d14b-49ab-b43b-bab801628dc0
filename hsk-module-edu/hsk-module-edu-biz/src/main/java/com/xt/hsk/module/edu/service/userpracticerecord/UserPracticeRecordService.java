package com.xt.hsk.module.edu.service.userpracticerecord;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.userpracticerecord.vo.UserPracticeRecordPageReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.AppUserPracticeRecordRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionSearchReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionStatisticsRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionRespVO;
import com.xt.hsk.module.edu.dal.dataobject.userpracticerecord.UserPracticeRecordDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 用户练习记录 Service 接口
 *
 * <AUTHOR>
 */
public interface UserPracticeRecordService extends IService<UserPracticeRecordDO> {
    PageResult<UserPracticeRecordDO> selectPage(@Valid UserPracticeRecordPageReqVO pageReqVO);

    QuestionStatisticsRespVO getQuestionNewestPracticeRecord(QuestionSearchReqVO reqVO);

    List<QuestionTypeCountRespVO> getUserUnitSortQuestionTypeCount(QuestionSearchReqVO reqVO);

    List<TextbookChapterQuestionRespVO> getUserTextbookChapterQuestions(@Valid QuestionSearchReqVO reqVO);

    AppUserPracticeRecordRespVO getUserNotFinishedPracticeRecord(QuestionSearchReqVO reqVO);

    /**
     * 获取用户练习中心的答题数据
     *
     * @param pageReqVO
     * @return
     */
    List<TextbookChapterQuestionRespVO> getUserPracticeTextbookChapterList(@Valid QuestionSearchReqVO pageReqVO);

    QuestionStatisticsRespVO getQuestionNewestHaveReportPracticeRecord(@Valid QuestionSearchReqVO pageReqVO);

    List<QuestionTypeCountRespVO> getHistoryUserUnitSortQuestionTypeCount(@Valid QuestionSearchReqVO pageReqVO);
}