package com.xt.hsk.module.edu.service.sourceword;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordExamplePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordExampleDO;
import jakarta.validation.Valid;

import java.util.List;


/**
 * 释义关联例句 Service 接口
 *
 * <AUTHOR>
 */
public interface SourceWordExampleService extends IService<SourceWordExampleDO> {
    PageResult<SourceWordExampleDO> selectPage(@Valid WordExamplePageReqVO pageReqVO);

    void removeByWordId(Long wordId);

    List<SourceWordExampleDO> getByWord(String word);
}