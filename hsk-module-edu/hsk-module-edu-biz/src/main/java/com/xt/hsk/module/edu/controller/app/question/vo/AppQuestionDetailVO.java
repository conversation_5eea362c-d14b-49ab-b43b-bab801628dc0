package com.xt.hsk.module.edu.controller.app.question.vo;

import com.xt.hsk.module.edu.controller.admin.question.vo.OptionContentVO;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class AppQuestionDetailVO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 题目ID
     */
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /**
     * 题干音频
     */
    private String attachmentAudio;
    /**
     * 题干音频
     */
    private String attachmentAudioContent;
    /**
     * 题干音频时长
     */
    private Integer attachmentAudioTime;

    /**
     * 题干图片
     */
    private String attachmentImage;

    /**
     * 题干内容
     */
    private String attachmentContent;

    /**
     * 参考答案
     */
    private String answer;
    /**
     * 参考答案文本
     */
    private String answerContent;

    /**
     * 题目选项 json
     */
    private String options;
    /**
     * 材料选项
     */
    private List<AppOptionContentVO> optionContents;


    /**
     * 版本号
     */
    private Integer version;

    /**
     * 文字题目解析
     */
    private String explainText;

    /**
     * 音频题目解析
     */
    private String explainAudio;

    /**
     * 视频题目解析
     */
    private String explainVideo;
    /**
     * 排序序号 也是题号
     */
    private Integer sort;

    /**
     * 用户作答数据
     */
    private AppUserQuestionAnswerData userQuestionAnswerData;
}
