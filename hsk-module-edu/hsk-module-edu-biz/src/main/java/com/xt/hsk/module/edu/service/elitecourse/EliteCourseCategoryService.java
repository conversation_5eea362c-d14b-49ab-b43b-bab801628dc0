package com.xt.hsk.module.edu.service.elitecourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseCategoryPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseCategoryDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 精品课-分类 Service 接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface EliteCourseCategoryService extends IService<EliteCourseCategoryDO> {

    PageResult<EliteCourseCategoryDO> selectPage(@Valid EliteCourseCategoryPageReqVO pageReqVO);

    /**
     * 获取展示的精品课分类列表（按sort排序） 仅返回存在上架且显示的精品课的分类
     *
     * @return 精品课分类列表
     */
    List<EliteCourseCategoryDO> getShowCategories();

    /**
     * 获取展示的精品课分类列表（按sort排序） 仅返回存在上架且显示的精品课的分类 可以按HSK等级筛选
     *
     * @param hskLevel HSK等级
     * @return 精品课分类列表
     */
    List<EliteCourseCategoryDO> getShowCategories(Integer hskLevel);

}