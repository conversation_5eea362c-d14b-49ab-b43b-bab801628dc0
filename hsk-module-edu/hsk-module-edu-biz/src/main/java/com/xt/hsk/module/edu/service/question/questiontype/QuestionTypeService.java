package com.xt.hsk.module.edu.service.question.questiontype;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiontype.QuestionTypeDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 题型 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionTypeService extends IService<QuestionTypeDO> {
    PageResult<QuestionTypeDO> selectPage(@Valid QuestionTypePageReqVO pageReqVO);

    List<QuestionTypeDO> getQuestionTypeList(QuestionTypePageReqVO pageReqVO);
}