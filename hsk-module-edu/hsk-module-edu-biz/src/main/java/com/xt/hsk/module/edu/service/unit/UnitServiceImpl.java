package com.xt.hsk.module.edu.service.unit;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitPageReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitTextBookVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.xt.hsk.module.edu.dal.dataobject.unit.UnitDO;

import com.xt.hsk.module.edu.dal.mysql.unit.UnitMapper;

import java.util.List;


/**
 * 单元 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UnitServiceImpl extends ServiceImpl<UnitMapper, UnitDO> implements UnitService {

    @Resource
    private UnitMapper unitMapper;

    @Override
    public PageResult<UnitDO> selectPage(UnitPageReqVO pageReqVO) {

        return unitMapper.selectPage(pageReqVO);
    }

    @Override
    public List<UnitDO> getByChapterId(Long chapterId) {
        LambdaQueryWrapper<UnitDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UnitDO::getChapterId, chapterId);
        return unitMapper.selectList(queryWrapper);
    }

    @Override
    public List<UnitDO> getByChapterIdAndSubject(Long chapterId, Integer subject) {

        LambdaQueryWrapper<UnitDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UnitDO::getChapterId, chapterId);
        queryWrapper.eq(UnitDO::getSubject, subject);
        return unitMapper.selectList(queryWrapper);
    }

    @Override
    public List<UnitTextBookVO> queryUnitUnitData(String unitNameCn, String chapterNameCn, String textBookNameCn,Integer code) {
        return unitMapper.queryUnitUnitData(unitNameCn, chapterNameCn, textBookNameCn,code);
    }

}