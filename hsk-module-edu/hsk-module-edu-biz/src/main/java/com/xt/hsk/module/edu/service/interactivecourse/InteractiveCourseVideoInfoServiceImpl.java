package com.xt.hsk.module.edu.service.interactivecourse;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseVideoInfoDO;
import com.xt.hsk.module.edu.dal.mysql.interactivecourse.InteractiveCourseVideoInfoMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 互动课视频信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InteractiveCourseVideoInfoServiceImpl extends
    ServiceImpl<InteractiveCourseVideoInfoMapper, InteractiveCourseVideoInfoDO>
    implements InteractiveCourseVideoInfoService {

    @Resource
    private InteractiveCourseVideoInfoMapper videoInfoMapper;

} 