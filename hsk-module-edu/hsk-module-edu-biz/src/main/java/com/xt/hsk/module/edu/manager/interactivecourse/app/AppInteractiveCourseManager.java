package com.xt.hsk.module.edu.manager.interactivecourse.app;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.number.NumberUtils;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseAppDetailVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseAppRespVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCoursePageReqVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCourseProgressVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCourseReqVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveProgressVO;
import com.xt.hsk.module.edu.convert.interactivecourse.InteractiveCourseConvert;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.UserInteractiveCourseRecordDO;
import com.xt.hsk.module.edu.enums.ErrorCodeConstants;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseService;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseStudyStatsService;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseUnitService;
import com.xt.hsk.module.edu.service.interactivecourse.UserInteractiveCourseRecordService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 交互式课程管理器
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Slf4j
@Component
public class AppInteractiveCourseManager {

    /**
     * 互动课
     */
    @Resource
    private InteractiveCourseService interactiveCourseService;

    /**
     * 互动课程单元服务
     */
    @Resource
    private InteractiveCourseUnitService interactiveCourseUnitService;

    /**
     * 互动课用户记录
     */
    @Resource
    private UserInteractiveCourseRecordService userInteractiveCourseRecordService;

    /**
     * 互动课学习统计服务
     */
    @Resource
    private InteractiveCourseStudyStatsService interactiveCourseStudyStatsService;

    /**
     * 互动课列表页面 根据hsk等级查询
     *
     * @param reqVO req vo
     * @return 互动课列表
     */
    public PageResult<InteractiveCourseAppRespVO> getInteractiveCourseVoPage(
        AppInteractiveCoursePageReqVO reqVO) {

        // 1. 查询课程数据（使用APP专用方法）
        Long userId = StpUtil.isLogin() ? StpUtil.getLoginIdAsLong() : null;
        PageResult<InteractiveCourseDO> interactiveCoursePage = interactiveCourseService.getInteractiveCoursePageForApp(
            reqVO, userId);
        
        // 2. 转换为前端响应VO
        List<InteractiveCourseAppRespVO> courseRespVOList = InteractiveCourseConvert.INSTANCE
            .doListToAppPageVOList(interactiveCoursePage.getList());
        if (courseRespVOList.isEmpty()) {
            return new PageResult<>(courseRespVOList, interactiveCoursePage.getTotal());
        }

        // 3. 填充单元数量信息
        fillUnitCountInfo(courseRespVOList);

        // 4. 如果用户已登录，填充学习进度信息
        if (StpUtil.isLogin()) {
            fillCompletedUnitCountInfo(courseRespVOList);
        }

        // 5. 标识最近学习的课程
        if (!courseRespVOList.isEmpty() && userId != null) {
            // 获取最近学习的课程ID
            Long recentCourseId = getRecentStudiedCourseId(userId, reqVO.getHskLevel());
            if (recentCourseId != null) {
                // 找到最近学习的课程并标识
                courseRespVOList.stream()
                    .filter(course -> course.getId().equals(recentCourseId))
                    .findFirst()
                    .ifPresent(course -> course.setIsRecentlyStudied(true));
            }
        }

        return new PageResult<>(courseRespVOList, interactiveCoursePage.getTotal());
    }



    /**
     * 填充课程单元数量信息
     *
     * @param courseRespVOList 课程列表
     */
    private void fillUnitCountInfo(List<InteractiveCourseAppRespVO> courseRespVOList) {
        List<Long> courseIds = courseRespVOList.stream()
            .map(InteractiveCourseAppRespVO::getId)
            .toList();
        Map<Long, Long> countByCourseIds = interactiveCourseUnitService.countByCourseIds(courseIds);
        courseRespVOList.forEach(courseRespVO ->
            courseRespVO.setUnitCount(countByCourseIds.getOrDefault(courseRespVO.getId(), 0L)));
    }

    /**
     * 填充用户已完成的单元数量信息
     *
     * @param courseRespVOList 课程列表
     */
    private void fillCompletedUnitCountInfo(List<InteractiveCourseAppRespVO> courseRespVOList) {
        List<Long> courseIds = courseRespVOList.stream()
            .map(InteractiveCourseAppRespVO::getId)
            .toList();
        Map<Long, Long> completedUnitCountByCourseIds = userInteractiveCourseRecordService.getCompletedUnitCountByCourseIds(
            StpUtil.getLoginIdAsLong(), courseIds);
        courseRespVOList.forEach(courseRespVO ->
            courseRespVO.setCompletedUnitCount(completedUnitCountByCourseIds.getOrDefault(courseRespVO.getId(), 0L)));
    }

    /**
     * 获取互动课详细信息
     *
     * @param reqVO req vo
     * @return {@code InteractiveCourseAppDetailVO }
     */
    public InteractiveCourseAppDetailVO getInteractiveCourseDetail(
        @Valid AppInteractiveCourseReqVO reqVO) {

        // 课程基本信息 主要是获取课程名称
        InteractiveCourseDO courseDO = validateInteractiveCourseStatus(
            reqVO.getCourseId());
        InteractiveCourseAppDetailVO courseRespVO = InteractiveCourseConvert.INSTANCE
            .doToAppDetailVO(courseDO);

        // 学习人数
        Integer studyCount = interactiveCourseStudyStatsService.getCourseStudyCount(
            courseDO.getId());
        courseRespVO.setStudyCount(NumberUtils.add(studyCount, courseDO.getLearningBase()));

        // 学习时长
        courseRespVO.setRecommendedDuration(
            interactiveCourseUnitService.getUnitDurationByCourseId(courseDO.getId()));

        // 课程位置
        Integer position = interactiveCourseService.getCoursePositionInLevel(courseDO.getHskLevel(),
            courseDO.getId());
        courseRespVO.setPosition(position);
        return courseRespVO;
    }

    /**
     * 校验互动课状态 如果互动课不存在或者被禁用，抛出异常
     *
     * @param courseId 互动课ID
     * @return 互动课
     */
    public InteractiveCourseDO validateInteractiveCourseStatus(Long courseId) {
        InteractiveCourseDO courseDO = interactiveCourseService.getById(courseId);
        if (courseDO == null) {
            throw exception(ErrorCodeConstants.INTERACTIVE_COURSE_NOT_EXISTS);
        }
        // 1展示 0不展示
        if (Objects.equals(courseDO.getDisplayStatus(), IsEnum.NO.getCode())) {
            throw exception(ErrorCodeConstants.INTERACTIVE_COURSE_NOT_ON_SHELF);
        }
        return courseDO;
    }

    /**
     * 获取互动课进度
     * 课程总数量 = hsk等级下,状态为显示的课程数量
     * 学习课程数量 = 学习进度为100的课程数量
     * 学习进度计算逻辑 = 进度表中最新的记录 状态为完成的单元数量 / 课程下状态为显示的单元数量
     * @param reqVO reqVO
     * @return 互动课进度
     */
    public AppInteractiveCourseProgressVO getInteractiveCourseProgress(
        @Valid AppInteractiveProgressVO reqVO) {

        // 总课程数量
        Long countByHskLevel = interactiveCourseService.getDisplayCountByHskLevel(
            reqVO.getHskLevel());

        // 已学习课程数量
        Long completedCourseCount = 0L;
        if (StpUtil.isLogin()) {
            completedCourseCount = interactiveCourseService.getCompletedCourseCount(
                StpUtil.getLoginIdAsLong(),
                reqVO.getHskLevel());
        }

        // 返回结果
        return AppInteractiveCourseProgressVO.builder()
            .hskLevel(reqVO.getHskLevel())
            .courseCount(countByHskLevel)
            .completedCourseCount(completedCourseCount)
            .build();
    }

    /**
     * 获取用户最近学习的课程ID
     */
    private Long getRecentStudiedCourseId(Long userId, Integer hskLevel) {
        // 获取当前等级下显示的课程ID列表
        List<Long> courseIdList = interactiveCourseService.getDisplayInteractiveCourseListByHskLevel(hskLevel);
        if (CollUtil.isEmpty(courseIdList)) {
            return null;
        }

        // 查询用户在当前等级下的最新学习记录
        UserInteractiveCourseRecordDO recordDO = userInteractiveCourseRecordService.lambdaQuery()
            .eq(UserInteractiveCourseRecordDO::getUserId, userId)
            .in(UserInteractiveCourseRecordDO::getCourseId, courseIdList)
            .orderByDesc(UserInteractiveCourseRecordDO::getId)
            .last("LIMIT 1")
            .one();

        return recordDO != null ? recordDO.getCourseId() : null;
    }
}
