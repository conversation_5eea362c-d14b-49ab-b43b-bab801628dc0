package com.xt.hsk.module.edu.service.word;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.tag.vo.TagPageReqVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordTagPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordTagDO;
import com.xt.hsk.module.edu.dal.mysql.word.WordTagMapper;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 词语标签 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WordTagServiceImpl extends ServiceImpl<WordTagMapper, WordTagDO> implements WordTagService {

    @Resource
    private WordTagMapper wordTagMapper;

    @Override
    public PageResult<WordTagDO> selectPage(WordTagPageReqVO pageReqVO) {

        return wordTagMapper.selectPage(pageReqVO);
    }

    @Override
    public void removeByWordId(Long wordId) {
        LambdaUpdateWrapper<WordTagDO> lambda = new LambdaUpdateWrapper<>();
        lambda.eq(WordTagDO::getWordId, wordId);
        wordTagMapper.delete(lambda);
    }

    @Override
    public List<WordTagDO> getByWordId(Long id) {
        LambdaQueryWrapper<WordTagDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WordTagDO::getWordId, id);
        return wordTagMapper.selectList(queryWrapper);
    }

    @Override
    public PageResult<WordTagDO> getWordTagPageByTagIds(TagPageReqVO tagPageReqVO) {
        return wordTagMapper.getWordTagPageByTagIds(tagPageReqVO);
    }

    @Override
    public List<WordTagDO> getByWordIds(List<Long> wordIds) {
        if (wordIds == null || wordIds.isEmpty()) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<WordTagDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WordTagDO::getWordId, wordIds);
        return wordTagMapper.selectList(queryWrapper);
    }
}