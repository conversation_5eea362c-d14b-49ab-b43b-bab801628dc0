package com.xt.hsk.module.edu.controller.app.exam.vo;

import lombok.Data;

import java.util.List;

/**
 * 模考报告答题卡响应 VO
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
public class ExamReportAnswerCardRespVO {

    /**
     * 科目代码
     */
    private Integer subject;

    /**
     * 科目描述
     */
    private String subjectDesc;

    /**
     * 练习状态（1-未开始，2-进行中，3-已完成）
     */
    private Integer practiceStatus;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 得分
     */
    private Integer score;

    /**
     * 单元列表
     */
    private List<ExamReportAnswerCardUnitRespVO> unitList;

}