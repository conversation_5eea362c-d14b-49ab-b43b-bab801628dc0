package com.xt.hsk.module.edu.service.interactivecourse;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.UserInteractiveCourseRecordDO;
import com.xt.hsk.module.edu.dal.mysql.interactivecourse.InteractiveCourseRecordMapper;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseRecordBizTypeEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseStatusEnum;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.validation.annotation.Validated;

/**
 * 互动课-用户学习记录 Service 实现类
 *
 * <AUTHOR>
 * @since 2025/06/11
 */
@Slf4j
@Service
@Validated
public class UserInteractiveCourseRecordServiceImpl extends
    ServiceImpl<InteractiveCourseRecordMapper, UserInteractiveCourseRecordDO> implements
    UserInteractiveCourseRecordService {

    @Resource
    private InteractiveCourseRecordMapper interactiveCourseRecordMapper;

    @Resource
    @Lazy
    private InteractiveCourseStudyStatsService courseStudyStatsService;

    @Resource
    private InteractiveCourseUnitService interactiveCourseUnitService;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 将之前的课程记录更新为非 最新
     *
     */
    private void updatePreviousRecordToNotLatest(@NotNull Long unitId,
        @NotNull Long userId,
        @NotNull Integer bizType) {
        this.lambdaUpdate()
            .eq(UserInteractiveCourseRecordDO::getUnitId, unitId)
            .eq(UserInteractiveCourseRecordDO::getUserId, userId)
            .eq(UserInteractiveCourseRecordDO::getBizType, bizType)
            .eq(UserInteractiveCourseRecordDO::getIsLatest, IsEnum.YES.getCode())
            .set(UserInteractiveCourseRecordDO::getIsLatest, IsEnum.NO.getCode())
            .update();
    }


    /**
     * 保存学习记录并更新统计
     * 1. 更新之前的记录为非最新
     * 2. 保存记录
     * 3. 更新统计
     *
     * @param recordDO 学习记录
     * @return 是否成功
     */
    public boolean saveRecordAndUpdateStats(UserInteractiveCourseRecordDO recordDO) {

        // 检查单元是否存在
        InteractiveCourseUnitDO unitDO = interactiveCourseUnitService.getById(recordDO.getUnitId());
        if (unitDO == null) {
            log.warn("互动课单元不存在: unitId = {}", recordDO.getUnitId());
            return false;
        }

        // 检查用户是否首次学习该单元（在保存记录之前检查）
        boolean isFirstTimeStudy = !lambdaQuery()
            .eq(UserInteractiveCourseRecordDO::getUserId, recordDO.getUserId())
            .eq(UserInteractiveCourseRecordDO::getUnitId, recordDO.getUnitId())
            .exists();

        // 更新之前的记录为非最新
        updatePreviousRecordToNotLatest(recordDO.getUnitId(), recordDO.getUserId(),
            recordDO.getBizType());

        // 保存记录
        recordDO.setResourceVersion(unitDO.getResourceVersion());
        recordDO.setCourseId(unitDO.getCourseId());
        boolean result = save(recordDO);

        // 更新统计（只有首次学习才更新）
        if (result) {
            courseStudyStatsService.updateStudyStats(recordDO.getCourseId(), recordDO.getUnitId(),
                recordDO.getUserId(), isFirstTimeStudy);
        }

        return result;
    }

    /**
     * 获取互动课单元的记录 只查询最新的记录
     *
     * @param unitId      单元 ID
     * @param userId      用户 ID
     * @param bizTypeEnum 业务类型 @see InteractiveCourseRecordBizTypeEnum
     * @return {@code InteractiveCourseRecordDO }
     */
    @Override
    public UserInteractiveCourseRecordDO getInteractiveCourseRecord(Long unitId, long userId,
        InteractiveCourseRecordBizTypeEnum bizTypeEnum) {
        return lambdaQuery().eq(UserInteractiveCourseRecordDO::getUnitId, unitId)
            .eq(UserInteractiveCourseRecordDO::getUserId, userId)
            .eq(UserInteractiveCourseRecordDO::getBizType, bizTypeEnum.getCode())
            .eq(UserInteractiveCourseRecordDO::getIsLatest, IsEnum.YES.getCode())
            .orderByDesc(UserInteractiveCourseRecordDO::getId)
            .last("LIMIT 1")
            .one();
    }

    @Override
    public Boolean checkVideoCompletion(Long unitId, long userId, Integer version) {
        return this.lambdaQuery()
            .eq(UserInteractiveCourseRecordDO::getUnitId, unitId)
            .eq(UserInteractiveCourseRecordDO::getUserId, userId)
            .eq(UserInteractiveCourseRecordDO::getResourceVersion, version)
            .eq(UserInteractiveCourseRecordDO::getBizType, InteractiveCourseRecordBizTypeEnum.VIDEO_VIEWING_RECORD.getCode())
            .eq(UserInteractiveCourseRecordDO::getStatus, InteractiveCourseStatusEnum.COMPLETED.getCode())
            .exists();
    }

    @Override
    public void createBizRecord(Long userId,
        Long unitId,
        InteractiveCourseRecordBizTypeEnum bizType,
        InteractiveCourseStatusEnum interactiveCourseStatusEnum,
        Long bizId,
        Long gameId,
        List<Long> questionIds) {

        transactionTemplate.execute(status -> {
            // 保存记录
            boolean result = this.saveRecordAndUpdateStats(UserInteractiveCourseRecordDO.builder()
                .userId(userId)
                .unitId(unitId)
                .bizType(bizType.getCode())
                .bizId(bizId)
                .status(interactiveCourseStatusEnum.getCode())
                .isLatest(IsEnum.YES.getCode())
                .gameId(gameId)
                .questionIds(CollUtil.isNotEmpty(questionIds) ?
                    questionIds.stream().map(String::valueOf).collect(Collectors.joining(",")) : null)
                .build());
            if (!result) {
                log.error("创建业务记录失败: userId = {}, unitId = {}, bizType = {}, interactiveCourseStatusEnum = {}, bizId = {}",
                    userId, unitId, bizType, interactiveCourseStatusEnum, bizId);
            }
            return null;
        });
    }

    @Override
    public void createVideoRecord(Long userId, Long courseId, Long unitId, Integer videoDuration,
        Integer viewingDuration, @NotNull Integer saveOption, BigDecimal videoProgress) {

        transactionTemplate.execute(transactionStatus -> {

            // 完成状态判断 完成率 = 100% 时 算完成 其他都算进行中
            InteractiveCourseStatusEnum status = videoProgress.compareTo(BigDecimal.valueOf(100)) == 0 ?
                InteractiveCourseStatusEnum.COMPLETED : InteractiveCourseStatusEnum.IN_PROGRESS;

            // 保存记录
            boolean result = this.saveRecordAndUpdateStats(UserInteractiveCourseRecordDO.builder()
                .userId(userId)
                .courseId(courseId)
                .unitId(unitId)
                .saveOption(saveOption)
                .videoDuration(videoDuration)
                .viewingDuration(viewingDuration)
                .videoProgress(videoProgress)
                .bizType(InteractiveCourseRecordBizTypeEnum.VIDEO_VIEWING_RECORD.getCode())
                .status(status.getCode())
                .isLatest(IsEnum.YES.getCode())
                .build());
            if (!result) {
                log.error("创建视频学习记录失败: userId = {}, courseId = {}, unitId = {}, videoDuration = {}, viewingDuration = {}, videoProgress = {}",
                    userId, courseId, unitId, videoDuration, viewingDuration, videoProgress);
            }
            return null;
        });
    }

    @Override
    public void updateRecordStatusAndDetails(Long userId , Long unitId,
        InteractiveCourseRecordBizTypeEnum bizType, Long bizId, Integer aiCorrectionStatus,
        BigDecimal accuracy) {

        UserInteractiveCourseRecordDO recordDO = interactiveCourseRecordMapper.selectOne(
            new LambdaQueryWrapper<UserInteractiveCourseRecordDO>()
                .eq(UserInteractiveCourseRecordDO::getUserId, userId)
                .eq(UserInteractiveCourseRecordDO::getUnitId, unitId)
                .eq(UserInteractiveCourseRecordDO::getBizType, bizType.getCode())
                .eq(UserInteractiveCourseRecordDO::getBizId, bizId)
                .eq(UserInteractiveCourseRecordDO::getIsLatest, IsEnum.YES.getCode()));
        if (recordDO == null) {
            // 查询不出记录 可能传参异常 记录日志
            log.error(
                "更新学习记录失败可能参数异常: userId = {}, unitId = {}, bizType = {}, bizId = {}",
                userId, unitId, bizType.getCode(), bizId);
            return;
        }

        if (InteractiveCourseStatusEnum.COMPLETED.getCode().equals(recordDO.getStatus())) {
            log.error(
                "更新学习记录状态失败,已经是完成状态, recordDO = {}", JSON.toJSONString(recordDO));
        }

        recordDO.setAiCorrectionStatus(aiCorrectionStatus);
        recordDO.setAccuracy(accuracy);
        recordDO.setStatus(InteractiveCourseStatusEnum.COMPLETED.getCode());
        boolean result = updateById(recordDO);
        if (!result) {
            log.error("更新学习记录失败: userId = {}, unitId = {}, bizType = {}, bizId = {}",
                userId, unitId, bizType.getCode(), bizId);
        }
    }

    @Override
    public Map<Long, UserInteractiveCourseRecordDO> getLatestRecordByUnitIds(Long userId,
        List<Long> unitIds) {
        List<UserInteractiveCourseRecordDO> recordDOList = lambdaQuery()
            .eq(UserInteractiveCourseRecordDO::getUserId, userId)
            .in(UserInteractiveCourseRecordDO::getUnitId, unitIds)
            .eq(UserInteractiveCourseRecordDO::getIsLatest, IsEnum.YES.getCode())
            .list();
        // 转成map如果有重复的 则取最新的
        return recordDOList.stream()
            .collect(Collectors.toMap(UserInteractiveCourseRecordDO::getUnitId, Function.identity(),
                (v1, v2) -> v2));
    }

    /**
     * 根据课程ID取用户学习的单元数量
     * 只查状态为显示的单元，且资源版本匹配的记录
     * @param userId    用户 ID
     * @param courseIds 课程 ID
     * @return Map<课程ID, 进度为已完成的单元数量>
     */
    @Override
    public Map<Long, Long> getCompletedUnitCountByCourseIds(Long userId, List<Long> courseIds) {
        if (courseIds == null || CollUtil.isEmpty(courseIds)) {
            return Map.of();
        }

        // 查询上架的单元，包含资源版本信息
        List<InteractiveCourseUnitDO> visibleUnits = interactiveCourseUnitService.lambdaQuery()
            .select(InteractiveCourseUnitDO::getId, InteractiveCourseUnitDO::getCourseId, InteractiveCourseUnitDO::getResourceVersion)
            .in(InteractiveCourseUnitDO::getCourseId, courseIds)
            .eq(InteractiveCourseUnitDO::getDisplayStatus, IsEnum.YES.getCode())
            .list();

        if (CollUtil.isEmpty(visibleUnits)) {
            return Map.of();
        }

        List<Long> visibleUnitIds = visibleUnits.stream()
            .map(InteractiveCourseUnitDO::getId)
            .toList();

        // 创建单元ID到资源版本的映射
        Map<Long, Integer> unitResourceVersionMap = visibleUnits.stream()
            .collect(Collectors.toMap(
                InteractiveCourseUnitDO::getId,
                InteractiveCourseUnitDO::getResourceVersion
            ));

        // 查询用户完成的学习记录，包含资源版本信息
        List<UserInteractiveCourseRecordDO> recordDOList = lambdaQuery()
            .select(UserInteractiveCourseRecordDO::getCourseId, UserInteractiveCourseRecordDO::getUnitId, UserInteractiveCourseRecordDO::getResourceVersion)
            .eq(UserInteractiveCourseRecordDO::getUserId, userId)
            .in(UserInteractiveCourseRecordDO::getUnitId, visibleUnitIds)
            .eq(UserInteractiveCourseRecordDO::getStatus, InteractiveCourseStatusEnum.COMPLETED.getCode())
            .eq(UserInteractiveCourseRecordDO::getIsLatest, IsEnum.YES.getCode())
            .list();

        if (CollUtil.isEmpty(recordDOList)) {
            return Map.of();
        }

        // 过滤出资源版本匹配的记录，然后分组统计
        return recordDOList.stream()
            .filter(record -> {
                Integer unitResourceVersion = unitResourceVersionMap.get(record.getUnitId());
                Integer recordResourceVersion = record.getResourceVersion();
                // 只统计资源版本匹配的记录
                return Objects.equals(unitResourceVersion, recordResourceVersion);
            })
            .collect(Collectors.groupingBy(UserInteractiveCourseRecordDO::getCourseId,
                Collectors.counting()));
    }

    @Override
    public Map<Long, Integer> batchGetVideoCompletionResourceVersion(Long userId, List<Long> unitIds) {
        if (CollUtil.isEmpty(unitIds)) {
            return Map.of();
        }

        // 使用自定义 SQL 在数据库层面进行聚合，避免查询大量数据
        List<UserInteractiveCourseRecordDO> completedRecords = interactiveCourseRecordMapper.selectMaxResourceVersionByUnitIds(userId, unitIds);

        // 转换为 Map<单元ID, 最高完成的资源版本号>
        return completedRecords.stream()
            .collect(Collectors.toMap(
                UserInteractiveCourseRecordDO::getUnitId,
                UserInteractiveCourseRecordDO::getResourceVersion
            ));
    }

    @Override
    public Long countDistinctUsersByCourseAndUnits(Long courseId, List<Long> unitIds) {
        if (CollUtil.isEmpty(unitIds)) {
            return 0L;
        }
        return interactiveCourseRecordMapper.countDistinctUsersByCourseAndUnits(courseId, unitIds);
    }

}