package com.xt.hsk.module.edu.service.chapter;

import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterPageReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;

import jakarta.validation.Valid;

import com.xt.hsk.module.edu.dal.dataobject.chapter.ChapterDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;


/**
 * 课程大纲 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ChapterManager {

    @Resource
    private ChapterService chapterService;


    public Long createChapter(ChapterSaveReqVO createReqVO) {
        // 插入
        ChapterDO chapter = BeanUtils.toBean(createReqVO, ChapterDO.class);
        chapterService.save(chapter);

        // 返回
        return chapter.getId();
    }


    public void updateChapter(ChapterSaveReqVO updateReqVO) {
        // 校验存在
        validateChapterExists(updateReqVO.getId());
        // 更新
        ChapterDO updateObj = BeanUtils.toBean(updateReqVO, ChapterDO.class);
        chapterService.updateById(updateObj);
    }


    public void deleteChapter(Long id) {
        // 校验存在
        validateChapterExists(id);
        // 删除
        chapterService.removeById(id);
    }

    private void validateChapterExists(Long id) {
        if (chapterService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public ChapterDO getChapter(Long id) {
        return chapterService.getById(id);
    }

    public PageResult<ChapterDO> getChapterPage(@Valid ChapterPageReqVO pageReqVO) {
        return chapterService.selectPage(pageReqVO);
    }

}