package com.xt.hsk.module.edu.manager.exam.admin;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_PAPER_RULE_NOT_EXISTS;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.EXAM_PAPER_RULE_NO_VALID_QUESTION_TYPE;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamDetailQuestionReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamDetailSubjectRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamDetailUnitRespVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailRespVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDetailDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDetailDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamQuestionTypeDO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetail.QuestionDetailDO;
import com.xt.hsk.module.edu.enums.exam.ExamPaperRuleStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import com.xt.hsk.module.edu.service.exam.ExamDetailService;
import com.xt.hsk.module.edu.service.exam.ExamPaperRuleDetailService;
import com.xt.hsk.module.edu.service.exam.ExamPaperRuleService;
import com.xt.hsk.module.edu.service.exam.ExamQuestionTypeService;
import com.xt.hsk.module.edu.service.exam.ExamService;
import com.xt.hsk.module.edu.service.question.QuestionService;
import com.xt.hsk.module.edu.service.question.questiondetail.QuestionDetailService;
import com.xt.hsk.module.edu.util.QuestionContentUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 模考组卷与详情管理类
 * <p>
 * 获取模考的科目与单元的题目详情
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@Component
public class ExamComposeManager {

    @Resource
    private ExamDetailService examDetailService;

    @Resource
    private ExamPaperRuleService examPaperRuleService;

    @Resource
    private ExamPaperRuleDetailService examPaperRuleDetailService;

    @Resource
    private ExamQuestionTypeService examQuestionTypeService;

    @Resource
    private QuestionService questionService;

    @Resource
    private QuestionDetailService questionDetailService;

    @Resource
    private ExamService examService;


    /**
     * 获取模考详情科目列表
     *
     * @param examId      模考id
     * @param paperRuleId 模考规则id
     * @return 模考详情科目列表
     */
    public List<ExamDetailSubjectRespVO> getExamDetailSubjectList(Long examId, Long paperRuleId) {
        ExamPaperRuleDO paperRuleList = examPaperRuleService.lambdaQuery()
                .eq(ExamPaperRuleDO::getId, paperRuleId)
                .eq(ExamPaperRuleDO::getStatus, ExamPaperRuleStatusEnum.ENABLED.getCode())
                .one();

        if (paperRuleList == null) {
            throw exception(EXAM_PAPER_RULE_NOT_EXISTS);
        }

        List<ExamPaperRuleDetailDO> paperRuleDetailList = examPaperRuleDetailService.lambdaQuery()
                .eq(ExamPaperRuleDetailDO::getPaperRuleId, paperRuleId)
                .orderByAsc(ExamPaperRuleDetailDO::getSubject, ExamPaperRuleDetailDO::getUnit)
                .list();

        if (CollUtil.isEmpty(paperRuleDetailList)) {
            throw exception(EXAM_PAPER_RULE_NO_VALID_QUESTION_TYPE);
        }

        List<Long> examQuestionTypeIdList = paperRuleDetailList.stream()
                .map(ExamPaperRuleDetailDO::getExamQuestionTypeId)
                .toList();

        if (CollUtil.isEmpty(examQuestionTypeIdList)) {
            return Collections.emptyList();
        }

        List<ExamQuestionTypeDO> examQuestionTypeList = examQuestionTypeService.listByIds(examQuestionTypeIdList);

        Map<Long, List<ExamQuestionTypeDO>> questionTypeMap = examQuestionTypeList.stream()
                .collect(Collectors.groupingBy(ExamQuestionTypeDO::getId));


        Map<Integer, List<ExamDetailDO>> examDetailMap = isExamUsingPaperRule(examId, paperRuleId) ? examDetailService.lambdaQuery()
                .eq(ExamDetailDO::getExamId, examId)
                .list()
                .stream()
                .collect(Collectors.groupingBy(ExamDetailDO::getSubject)) : Collections.emptyMap();


        Map<Integer, List<ExamPaperRuleDetailDO>> subjectMap = paperRuleDetailList.stream()
                .collect(Collectors.groupingBy(ExamPaperRuleDetailDO::getSubject));

        Map<Long, QuestionRespVO> list = isExamUsingPaperRule(examId, paperRuleId) ? getQuestionMap(examId) : Collections.emptyMap();


        List<ExamDetailSubjectRespVO> subjectRespVOList = new ArrayList<>();
        for (Map.Entry<Integer, List<ExamPaperRuleDetailDO>> entry : subjectMap.entrySet()) {
            List<ExamPaperRuleDetailDO> detailDOList = entry.getValue();

            List<ExamDetailDO> orDefault = examDetailMap.getOrDefault(entry.getKey(), new ArrayList<>());

            Map<Integer, ExamDetailDO> detailMap = orDefault.stream()
                    .collect(Collectors.toMap(
                            ExamDetailDO::getUnit,
                            Function.identity(),
                            (old, now) -> old
                    ));

            List<ExamDetailUnitRespVO> unitRespVOList = detailDOList.stream()
                    .map(detailDO -> {
                        ExamDetailUnitRespVO unitRespVO = new ExamDetailUnitRespVO();
                        unitRespVO.setUnit(detailDO.getUnit());
                        unitRespVO.setQuestionCount(detailDO.getQuestionCount());
                        unitRespVO.setExamQuestionTypeId(detailDO.getExamQuestionTypeId());

                        List<ExamQuestionTypeDO> questionTypeList = questionTypeMap.get(unitRespVO.getExamQuestionTypeId());

                        if (CollUtil.isNotEmpty(questionTypeList)) {
                            List<Long> questionTypeIdList = questionTypeList.stream()
                                    .filter(e -> CharSequenceUtil.isNotBlank(e.getQuestionTypeIds()))
                                    .map(e -> JSONUtil.toList(e.getQuestionTypeIds(), Long.class))
                                    .filter(CollUtil::isNotEmpty)
                                    .flatMap(List::stream)
                                    .distinct()
                                    .toList();

                            if (CollUtil.isNotEmpty(questionTypeIdList)) {
                                unitRespVO.setQuestionTypeIds(JSONUtil.toJsonStr(questionTypeIdList));
                                unitRespVO.setQuestionTypeIdList(questionTypeIdList);
                            }
                        }

                        ExamDetailDO examDetailDO = detailMap.get(detailDO.getUnit());
                        if (examDetailDO != null) {
                            unitRespVO.setExamDetailId(examDetailDO.getId());
                            String questions = examDetailDO.getQuestions();
                            List<ExamDetailQuestionReqVO> questionList = JSONUtil.toList(questions, ExamDetailQuestionReqVO.class);

                            questionList.forEach(question -> {
                                QuestionRespVO questionRespVO = list.get(question.getQuestionId());
                                if (questionRespVO != null) {
                                    BeanUtil.copyProperties(questionRespVO, question);
                                }
                            });

                            unitRespVO.setQuestionList(questionList);
                        }


                        return unitRespVO;
                    })
                    .toList();


            ExamDetailSubjectRespVO subjectRespVO = new ExamDetailSubjectRespVO();
            subjectRespVO.setSubject(entry.getKey());
            subjectRespVO.setExamDetailUnitList(unitRespVOList);

            subjectRespVOList.add(subjectRespVO);
        }
        return subjectRespVOList;

    }

    /**
     * 检查指定模考是否使用了指定的试卷规则
     *
     * @param examId      模考ID
     * @param paperRuleId 试卷规则ID
     * @return 如果模考使用了指定的试卷规则返回true，否则返回false
     */
    private boolean isExamUsingPaperRule(Long examId, Long paperRuleId) {
        // 参数校验
        if (examId == null || paperRuleId == null) {
            return false;
        }
        
        // 获取模考信息
        ExamDO exam = examService.getExam(examId);
        if (exam == null) {
            return false;
        }

        // 检查试卷规则是否匹配
        return Objects.equals(exam.getPaperRuleId(), paperRuleId);
    }

    private Map<Long, QuestionRespVO> getQuestionMap(Long examId) {

        if (examId == null) {
            return Collections.emptyMap();
        }

        List<ExamDetailDO> examDetailList = examDetailService.lambdaQuery()
                .eq(ExamDetailDO::getExamId, examId)
                .orderByAsc(ExamDetailDO::getSubject, ExamDetailDO::getUnit)
                .list();
        List<Long> questionsIdList = examDetailList.stream()
                .filter(e -> CharSequenceUtil.isNotBlank(e.getQuestions()))
                .map(e -> JSONUtil.toList(e.getQuestions(), ExamDetailQuestionReqVO.class))
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(ExamDetailQuestionReqVO::getQuestionId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        return getQuestionWithDetailByIdList(questionsIdList).stream()
                .collect(Collectors.toMap(QuestionRespVO::getId, Function.identity()));
    }

    /**
     * 按ID列表获取包含详细的题目信息列表
     *
     * @param questionsIdList 题目ID列表
     * @return 题目信息列表
     */
    private List<QuestionRespVO> getQuestionWithDetailByIdList(List<Long> questionsIdList) {


        if (CollUtil.isEmpty(questionsIdList)) {
            return Collections.emptyList();
        }

        List<QuestionRespVO> questionList = questionService.queryQuestionByIdList(questionsIdList);

        if (CollUtil.isEmpty(questionList)) {
            return Collections.emptyList();
        }

        List<QuestionDetailDO> questionDetailList = questionDetailService.lambdaQuery()
                .in(QuestionDetailDO::getQuestionId, questionsIdList)
                .list();

        List<QuestionDetailRespVO> respVOList = QuestionContentUtil.questionDetailConvert(questionDetailList);
        respVOList
                .forEach(questionDetailDO -> {
                    if (CharSequenceUtil.isNotBlank(questionDetailDO.getOptions())) {
                        questionDetailDO.setOptionsContent(QuestionContentUtil.optionConvert(questionDetailDO.getOptions()));
                        questionDetailDO.setOptions(null);
                    }
                });

        Map<Long, List<QuestionDetailRespVO>> questionDetailMap = respVOList.stream()
                .collect(Collectors.groupingBy(QuestionDetailRespVO::getQuestionId));

        questionList.forEach(questionRespVO -> {
            if (CharSequenceUtil.isNotBlank(questionRespVO.getOptions())) {
                questionRespVO.setOptionsContent(QuestionContentUtil.optionConvert(questionRespVO.getOptions()));
                questionRespVO.setOptions(null);
            }

            questionRespVO.setQuestionDetails(questionDetailMap.getOrDefault(questionRespVO.getId(), new ArrayList<>()));

        });

        return questionList;

    }

    /**
     * 组卷
     */
    public List<ExamDetailSubjectRespVO> randomCompose(Long paperRuleId) {
        ExamPaperRuleDO paperRule = examPaperRuleService.getPaperRule(paperRuleId);

        List<ExamPaperRuleDetailDO> examPaperRuleDetailList = examPaperRuleDetailService.lambdaQuery()
                .eq(ExamPaperRuleDetailDO::getPaperRuleId, paperRuleId)
                .orderByAsc(ExamPaperRuleDetailDO::getSubject, ExamPaperRuleDetailDO::getUnit)
                .list();


        List<Long> examQuestionTypeIdList = examPaperRuleDetailList.stream()
                .map(ExamPaperRuleDetailDO::getExamQuestionTypeId)
                .toList();

        if (CollUtil.isEmpty(examQuestionTypeIdList)) {
            return Collections.emptyList();
        }
        List<ExamQuestionTypeDO> examQuestionTypeList = examQuestionTypeService.listByIds(examQuestionTypeIdList);

        Map<Long, List<ExamQuestionTypeDO>> questionTypeMap = examQuestionTypeList.stream()
                .collect(Collectors.groupingBy(ExamQuestionTypeDO::getId));


        Map<Integer, List<ExamPaperRuleDetailDO>> subjectMap = examPaperRuleDetailList.stream()
                .collect(Collectors.groupingBy(ExamPaperRuleDetailDO::getSubject));

        List<ExamDetailSubjectRespVO> subjectRespVOList = new ArrayList<>();

        for (Map.Entry<Integer, List<ExamPaperRuleDetailDO>> entry : subjectMap.entrySet()) {
            List<ExamPaperRuleDetailDO> detailDOList = entry.getValue();

            Integer subject = entry.getKey();


            List<ExamDetailUnitRespVO> unitRespVOList = detailDOList.stream()
                    .map(detailDO -> {
                        ExamDetailUnitRespVO unitRespVO = new ExamDetailUnitRespVO();
                        unitRespVO.setUnit(detailDO.getUnit());
                        unitRespVO.setUnitDesc(ExamQuestionTypeUnitEnum.getDescByCode(detailDO.getUnit()));
                        unitRespVO.setQuestionCount(detailDO.getQuestionCount());
                        unitRespVO.setExamQuestionTypeId(detailDO.getExamQuestionTypeId());

                        return unitRespVO;
                    })
                    .toList();


            for (ExamDetailUnitRespVO unitRespVO : unitRespVOList) {
                List<ExamQuestionTypeDO> questionTypeList = questionTypeMap.get(unitRespVO.getExamQuestionTypeId());

                if (CollUtil.isEmpty(questionTypeList)) {
                    continue;
                }


                List<Long> questionTypeIdList = questionTypeList.stream()
                        .filter(e -> CharSequenceUtil.isNotBlank(e.getQuestionTypeIds()))
                        .map(e -> JSONUtil.toList(e.getQuestionTypeIds(), Long.class))
                        .filter(CollUtil::isNotEmpty)
                        .flatMap(List::stream)
                        .distinct()
                        .toList();

                if (CollUtil.isEmpty(questionTypeIdList)) {
                    continue;
                }


                unitRespVO.setQuestionTypeIds(JSONUtil.toJsonStr(questionTypeIdList));
                unitRespVO.setQuestionTypeIdList(questionTypeIdList);

                List<ExamDetailQuestionReqVO> questionList = getRandomQuestionList(
                        questionTypeIdList, unitRespVO.getQuestionCount(), paperRule.getHskLevel(), subject);

                if (CollUtil.isEmpty(questionList)) {
                    unitRespVO.setQuestionList(new ArrayList<>());
                    continue;
                }
                List<Long> questionIdList = questionList.stream()
                        .map(ExamDetailQuestionReqVO::getQuestionId)
                        .toList();

                if (CollUtil.isEmpty(questionIdList)) {
                    continue;
                }

                Map<Long, QuestionRespVO> questionRespVOList = getQuestionWithDetailByIdList(questionIdList).stream()
                        .collect(Collectors.toMap(QuestionRespVO::getId, Function.identity()));

                questionList.forEach(question -> {
                    QuestionRespVO questionRespVO = questionRespVOList.get(question.getQuestionId());
                    if (questionRespVO != null) {
                        BeanUtil.copyProperties(questionRespVO, question);
                    }
                });

                unitRespVO.setQuestionList(questionList);
            }


            ExamDetailSubjectRespVO subjectRespVO = new ExamDetailSubjectRespVO();
            subjectRespVO.setSubject(subject);
            SubjectEnum subjectEnum = SubjectEnum.getByCode(subject);
            if (subjectEnum != null) {
                subjectRespVO.setSubjectDesc(subjectEnum.getDesc());
            }
            subjectRespVO.setExamDetailUnitList(unitRespVOList);

            subjectRespVOList.add(subjectRespVO);
        }

        return subjectRespVOList;

    }

    /**
     * 获取随机题目列表
     *
     * @param questionTypeIdList 题型ID列表
     * @param questionCount      题目数量
     * @param hskLevel           HSK等级
     * @param subject            科目
     * @return 题目列表
     */
    private List<ExamDetailQuestionReqVO> getRandomQuestionList(List<Long> questionTypeIdList,
                                                                Integer questionCount,
                                                                Integer hskLevel,
                                                                Integer subject) {
        if (CollUtil.isEmpty(questionTypeIdList) || questionCount == null || questionCount <= 0) {
            return Collections.emptyList();
        }

        // 查询题库
        List<QuestionDO> questionList = questionService.lambdaQuery()
                .select(QuestionDO::getId, QuestionDO::getQuestionNum)
                .in(QuestionDO::getTypeId, questionTypeIdList)
                .eq(QuestionDO::getSubject, subject)
                .eq(QuestionDO::getHskLevel, hskLevel)
                .list();

        if (CollUtil.isEmpty(questionList)) {
            return Collections.emptyList();
        }


        List<QuestionDO> shuffledList = new ArrayList<>(questionList);
        Collections.shuffle(shuffledList);


        List<ExamDetailQuestionReqVO> resultList = new ArrayList<>();
        int totalQuestionCount = 0;
        int i = 1;

        for (QuestionDO question : shuffledList) {
            if (totalQuestionCount + question.getQuestionNum() > questionCount) {
                continue;
            }

            ExamDetailQuestionReqVO vo = new ExamDetailQuestionReqVO();
            vo.setQuestionId(question.getId());
            vo.setSort(i++);

            resultList.add(vo);
            totalQuestionCount += question.getQuestionNum();

            if (totalQuestionCount == questionCount) {
                break;
            }
        }

        return resultList;

    }

}