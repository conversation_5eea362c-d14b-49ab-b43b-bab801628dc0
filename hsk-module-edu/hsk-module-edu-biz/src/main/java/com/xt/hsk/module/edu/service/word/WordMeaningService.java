package com.xt.hsk.module.edu.service.word;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordMeaningPageReqVO;
import com.xt.hsk.module.edu.controller.app.word.vo.AppWordSearchVo;
import com.xt.hsk.module.edu.dal.dataobject.word.WordMeaningDO;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 词语多释义表（冗余word） Service 接口
 *
 * <AUTHOR>
 */
public interface WordMeaningService extends IService<WordMeaningDO> {
    PageResult<WordMeaningDO> selectPage(@Valid WordMeaningPageReqVO pageReqVO);

    void removeByWordId(Long wordId);

    List<WordMeaningDO> getByWordId(Long id);

    PageResult<WordMeaningDO> searchPage(AppWordSearchVo appWordSearchVo);

    /**
     * 根据词汇ID列表批量获取含义
     *
     * @param wordIds 词汇ID列表
     * @return Map<字词ID,第一个含义信息>
     */
    Map<Long, WordMeaningDO> getByWordIds(List<Long> wordIds);
}