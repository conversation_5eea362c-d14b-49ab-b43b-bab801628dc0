package com.xt.hsk.module.edu.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.translate.core.TranslateUtils;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordPageReqVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordRespVO;
import com.xt.hsk.module.edu.service.word.WordManager;
import com.xt.hsk.module.edu.service.word.WordService;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WordExportTaskExport extends
    BaseEasyExcelExport<WordRespVO> {
    @Resource
    private WordService wordService;

    @Resource
    private WordManager wordManager;

    @Override
    protected List<List<String>> getExcelHead() {
        // 直接在这里定义表头，方便开发人员查看和维护
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("ID"));
        head.add(Collections.singletonList("中文"));
        head.add(Collections.singletonList("HSK等级"));
        head.add(Collections.singletonList("拼音"));
        head.add(Collections.singletonList("汉越词"));
        head.add(Collections.singletonList("词性"));
        head.add(Collections.singletonList("标签"));
        head.add(Collections.singletonList("最近更新人"));
        head.add(Collections.singletonList("最近更新时间"));
        head.add(Collections.singletonList("收藏量"));
        head.add(Collections.singletonList("被引用次数"));
        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        // 使用配置好的 ObjectMapper 将 Map 转换为 InteractiveCoursePageReqVO 对象，忽略taskName字段
        WordPageReqVO dto = objectMapper.convertValue(conditions,
                WordPageReqVO.class);

        // 如果有选中的ID，返回选中的数量
        if (CollUtil.isNotEmpty(dto.getIds())) {
            return (long) dto.getIds().size();
        }

        return wordService.dataTotalCount(dto);
    }

    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition, Long pageNo, Long pageSize) {
        try {
            WordPageReqVO pageReqVO = objectMapper.convertValue(queryCondition, WordPageReqVO.class);
            pageReqVO.setPageNo(Math.toIntExact(pageNo));
            pageReqVO.setPageSize(Math.toIntExact(pageSize));

            PageResult<WordRespVO> wordPage = wordManager.getWordPage(pageReqVO);
            List<WordRespVO> wordRespVOList = wordPage.getList();
            if (CollUtil.isNotEmpty(wordRespVOList)) {
                // 手动触发翻译
                wordRespVOList = TranslateUtils.translate(wordRespVOList);

                for (WordRespVO wordRespVO : wordRespVOList) {
                    List<String> row = new ArrayList<>();
                    row.add(wordRespVO.getId().toString());
                    row.add(wordRespVO.getWord());
                    row.add(wordRespVO.getHskLevelsDesc());
                    row.add(wordRespVO.getPinyin());
                    row.add(wordRespVO.getIsSpecial() == 0 ? "-" : "汉越词");
                    row.add(wordRespVO.getKindsDesc() == null || wordRespVO.getKindsDesc().isEmpty() ? "-" : wordRespVO.getKindsDesc());
                    row.add(wordRespVO.getTagsDesc() == null || wordRespVO.getTagsDesc().isEmpty() ? "-" : wordRespVO.getTagsDesc());
                    row.add(wordRespVO.getUpdaterName());
                    row.add(wordRespVO.getCreateTime() != null ? DateUtil.format(wordRespVO.getUpdateTime(), "yyyy-MM-dd HH:mm:ss") : "");
                    row.add(wordRespVO.getCollectCount() != null ? wordRespVO.getCollectCount().toString() : "0");
                    row.add(wordRespVO.getQuoteCount() != null ? wordRespVO.getQuoteCount().toString() : "0");

                    resultList.add(row);
                }
            }
            log.info("字词库导出当前页：{}每页条数：{}总条数：{}", pageNo, pageSize,
                    wordPage.getTotal());
        } catch (Exception e) {
            log.error("构建字词库数据列表时出错页码：{}页面大小：{}，错误：{}", pageNo, pageSize,
                    e.getMessage(), e);
            throw new ServiceException(500, "导出字词库数据失败：" + e.getMessage());
        }

    }
}
