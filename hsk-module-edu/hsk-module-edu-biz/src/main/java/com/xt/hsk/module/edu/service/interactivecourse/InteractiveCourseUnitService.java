package com.xt.hsk.module.edu.service.interactivecourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitDO;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 互动课单元 Service 接口
 *
 * <AUTHOR>
 */
public interface InteractiveCourseUnitService extends IService<InteractiveCourseUnitDO> {

    /**
     * 获取互动课单元分页
     *
     * @param pageReqVO 分页查询参数
     * @return 分页结果
     */
    PageResult<InteractiveCourseUnitDO> getInteractiveCourseUnitPage(InteractiveCourseUnitPageReqVO pageReqVO);

    /**
     * 查询总条目
     *
     * @param queryCondition 查询条件
     * @return 总数量
     */
    Long dataTotalCount(InteractiveCourseUnitPageReqVO queryCondition);

    /**
     * 根据课程ID统计单元数量
     *
     * @param courseId 课程ID
     * @return 单元数量
     */
    Long countByCourseId(Long courseId);

    /**
     * 批量统计课程单元数量
     *
     * @param courseIds 课程ID列表
     * @return 课程ID -> 单元数量的映射
     */
    Map<Long, Long> countByCourseIds(Collection<Long> courseIds);

    /**
     * 按课程 ID 获取单元学习时间 只统计显示的单元学习时间
     *
     * @param courseId 课程ID
     * @return 推荐学习时长 单位秒
     */
    Integer getUnitDurationByCourseId(Long courseId);

    /**
     * 查询单元的内容类型
     * 如果是专项练习 就是专项练习类型 如果是真题就是题目类型 如果是本地视频 就是视频类型
     * @param unitIdList 单元ID
     * @return 结果
     */
    Map<Long, String> getUnitContentType(List<Long> unitIdList);

    /**
     * 获取下一节课程单元ID
     *
     * @param courseId 课程ID
     * @param currentSort 当前单元排序
     * @return 下一节课程单元ID，如果没有则返回null
     */
    Long getNextUnitId(Long courseId, Integer currentSort);

    /**
     * 查询单元关联的真题类型 ,关联的真题类型可能是多题 但是目前的逻辑只能新增同一个题型下的真题 所以可以直接get(0)
     * @param unitId 单元ID
     * @return 题目类型ID
     */
    Long getUnitQuestionTypeId(Long unitId);
}