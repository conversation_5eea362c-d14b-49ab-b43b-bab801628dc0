package com.xt.hsk.module.edu.service.exam;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleDetailPageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleQuestionTypeRespVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDetailDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 模考组卷规则明细 Service 接口
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
public interface ExamPaperRuleDetailService extends IService<ExamPaperRuleDetailDO> {
    PageResult<ExamPaperRuleDetailDO> selectPage(@Valid ExamPaperRuleDetailPageReqVO pageReqVO);

    /**
     * 获取模考组卷规则明细的题型列表
     *
     * @param paperRuleId 组卷规则ID
     * @return 模考组卷规则明细的题型列表
     */
    List<ExamPaperRuleQuestionTypeRespVO> getPaperRuleQuestionType(Long paperRuleId);

    /**
     * 设置题型名称信息
     *
     * @param paperRuleQuestionTypeList 题型列表
     */
    void setQuestionTypeNames(List<ExamPaperRuleQuestionTypeRespVO> paperRuleQuestionTypeList);
}