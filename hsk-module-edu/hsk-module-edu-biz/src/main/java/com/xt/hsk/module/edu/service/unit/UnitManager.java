package com.xt.hsk.module.edu.service.unit;

import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitPageReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;

import jakarta.validation.Valid;

import com.xt.hsk.module.edu.dal.dataobject.unit.UnitDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;


/**
 * 单元 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UnitManager {

    @Resource
    private UnitService unitService;


    public Long createUnit(UnitSaveReqVO createReqVO) {
        // 插入
        UnitDO unit = BeanUtils.toBean(createReqVO, UnitDO.class);
        unitService.save(unit);

        // 返回
        return unit.getId();
    }


    public void updateUnit(UnitSaveReqVO updateReqVO) {
        // 校验存在
        validateUnitExists(updateReqVO.getId());
        // 更新
        UnitDO updateObj = BeanUtils.toBean(updateReqVO, UnitDO.class);
        unitService.updateById(updateObj);
    }


    public void deleteUnit(Long id) {
        // 校验存在
        validateUnitExists(id);
        // 删除
        unitService.removeById(id);
    }

    private void validateUnitExists(Long id) {
        if (unitService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public UnitDO getUnit(Long id) {
        return unitService.getById(id);
    }

    public PageResult<UnitDO> getUnitPage(@Valid UnitPageReqVO pageReqVO) {
        return unitService.selectPage(pageReqVO);
    }

}