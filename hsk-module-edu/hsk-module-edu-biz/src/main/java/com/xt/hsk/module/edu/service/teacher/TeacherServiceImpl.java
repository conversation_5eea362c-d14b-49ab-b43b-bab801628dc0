package com.xt.hsk.module.edu.service.teacher;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.teacher.TeacherDO;
import com.xt.hsk.module.edu.dal.mysql.teacher.TeacherMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 讲师 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TeacherServiceImpl extends ServiceImpl<TeacherMapper, TeacherDO> implements
    TeacherService {

    @Resource
    private TeacherMapper teacherMapper;

    @Override
    public PageResult<TeacherDO> getTeacherPage(TeacherPageReqVO pageReqVO) {
        return teacherMapper.selectPage(pageReqVO);
    }

    @Override
    public TeacherDO getByCountryCodeAndMobile(String countryCode, String mobile) {
        return teacherMapper.selectByCountryCodeAndMobile(countryCode, mobile);
    }

    @Override
    public Long dataTotalCount(TeacherPageReqVO pageReqVO) {
        LambdaQueryWrapperX<TeacherDO> query = new LambdaQueryWrapperX<TeacherDO>();

        // 如果有选中的ID，优先使用ID列表查询，忽略其他条件
        if (CollUtil.isNotEmpty(pageReqVO.getIds())) {
            query.in(TeacherDO::getId, pageReqVO.getIds());
        } else {
            // 没有选中ID时，使用其他查询条件
            query.likeIfPresent(TeacherDO::getTeacherNameCn, pageReqVO.getTeacherNameCn())
                .likeIfPresent(TeacherDO::getMobile, pageReqVO.getMobile())
                .eqIfPresent(TeacherDO::getDisplayStatus, pageReqVO.getDisplayStatus());
        }

        return teacherMapper.selectCount(query);
    }
} 