package com.xt.hsk.module.edu.manager.interactivecourse.admin;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.LogRecordConstants.INTERACTIVE_COURSE_UPDATE_SORT_SUB_TYPE;
import static com.xt.hsk.module.edu.enums.LogRecordConstants.INTERACTIVE_COURSE_UPDATE_STATUS_SUB_TYPE;
import static com.xt.hsk.module.edu.enums.LogRecordConstants.INTERACTIVE_COURSE_UPDATE_STATUS_SUB_TYPE_SUCCESS;

import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.constants.RedisKeyPrefix;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseRespVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseSaveReqVO;
import com.xt.hsk.module.edu.convert.interactivecourse.InteractiveCourseConvert;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseDO;
import com.xt.hsk.module.edu.enums.ErrorCodeConstants;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseService;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseUnitService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Component;

/**
 * 交互课程管理器
 *
 * <AUTHOR>
 * @since 2025/05/23
 */
@Slf4j
@Component
public class InteractiveCourseManager {

    @Resource
    private InteractiveCourseService interactiveCourseService;

    @Resource
    private InteractiveCourseUnitService interactiveCourseUnitService;

    /**
     * 创建互动课
     * 新增时自动生成序号，不依赖前端传参，序号在 同一 "HSK等级 +课程类型"下不重复 最新创建的课程序号默认为 同一 "HSK等级 +课程类型"下最大序号 + 1
     * @param createReqVO create req vo
     * @return {@code Long }
     */
    @Caching(evict = {
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_LIST, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_COUNT, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_POSITION, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_FIRST, allEntries = true)
    })
    public Long createInteractiveCourse(InteractiveCourseSaveReqVO createReqVO) {
        // 插入
        InteractiveCourseDO interactiveCourse = InteractiveCourseConvert.INSTANCE.createReqVOToDO(createReqVO);

        // 查询 同一 "HSK等级 + 课程类型" 下最大序号
        InteractiveCourseDO maxSortCourse = interactiveCourseService.lambdaQuery()
            .eq(InteractiveCourseDO::getHskLevel, createReqVO.getHskLevel())
            .eq(InteractiveCourseDO::getType, createReqVO.getType())
            .orderByDesc(InteractiveCourseDO::getSort)
            .last("LIMIT 1")
            .one();
        interactiveCourse.setSort(maxSortCourse == null ? 1 : maxSortCourse.getSort() + 1);

        interactiveCourseService.save(interactiveCourse);
        LogRecordContext.putVariable("interactiveCourse", interactiveCourse);
        LogRecordContext.putVariable("id", interactiveCourse.getId());
        // 返回
        return interactiveCourse.getId();
    }

    /**
     * 更新互动课
     */
    @LogRecord(type = LogRecordType.INTERACTIVE_COURSE_TYPE,
        bizNo = "{{#updateReqVO.id}}",
        subType = "修改互动课信息",
        success = "更新了互动课【{{#interactiveCourse.courseNameCn}}】的信息")
    @Caching(evict = {
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_LIST, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_COUNT, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_POSITION, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_FIRST, allEntries = true)
    })
    public void updateInteractiveCourse(InteractiveCourseSaveReqVO updateReqVO) {
        // 校验存在
        validateInteractiveCourseExists(updateReqVO.getId());
        // 更新
        InteractiveCourseDO updateObj = InteractiveCourseConvert.INSTANCE.createReqVOToDO(updateReqVO);
        interactiveCourseService.updateById(updateObj);

        LogRecordContext.putVariable("interactiveCourse", updateObj);
    }

    /**
     * 删除互动课
     *
     * @param id 身份证
     */
    @Caching(evict = {
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_LIST, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_COUNT, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_POSITION, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_FIRST, allEntries = true)
    })
    public void deleteInteractiveCourse(Long id) {

        // 校验存在
        InteractiveCourseDO courseDO = validateInteractiveCourseExists(id);
        // 删除
        interactiveCourseService.removeById(id);

        LogRecordContext.putVariable("interactiveCourse", courseDO);
    }

    private InteractiveCourseDO validateInteractiveCourseExists(Long id) {
        InteractiveCourseDO courseDO = interactiveCourseService.getById(id);
        if (courseDO == null) {
            throw exception(ErrorCodeConstants.INTERACTIVE_COURSE_NOT_EXISTS);
        }
        return courseDO;
    }

    /**
     * 获取交互式课程
     *
     * @param id 身份证
     * @return {@code InteractiveCourseDO }
     */
    public InteractiveCourseDO getInteractiveCourse(Long id) {
        return interactiveCourseService.getById(id);
    }


    public PageResult<InteractiveCourseRespVO> getInteractiveCourseVoPage(
        InteractiveCoursePageReqVO pageReqVO) {
        PageResult<InteractiveCourseDO> interactiveCoursePage = interactiveCourseService.getInteractiveCoursePage(
            pageReqVO);
        List<InteractiveCourseDO> list = interactiveCoursePage.getList();
        List<InteractiveCourseRespVO> interactiveCourseRespVOList = InteractiveCourseConvert.INSTANCE.doListToCreateReqVOList(
            list);

        // 批量查询课程单元数量
        if (!interactiveCourseRespVOList.isEmpty()) {
            List<Long> courseIds = interactiveCourseRespVOList.stream()
                .map(InteractiveCourseRespVO::getId)
                .toList();
            Map<Long, Long> courseUnitCountMap = interactiveCourseUnitService.countByCourseIds(
                courseIds);

            // 设置单元数量
            interactiveCourseRespVOList.forEach(courseRespVO ->
                courseRespVO.setUnitCount(
                    courseUnitCountMap.getOrDefault(courseRespVO.getId(), 0L)));
        }
        
        return new PageResult<>(interactiveCourseRespVOList, interactiveCoursePage.getTotal());
    }

    /**
     * 更新状态
     *
     * @param id id
     */
    @LogRecord(type = LogRecordType.INTERACTIVE_COURSE_TYPE,
        subType = INTERACTIVE_COURSE_UPDATE_STATUS_SUB_TYPE,
        bizNo = "{{#id}}",
        success = INTERACTIVE_COURSE_UPDATE_STATUS_SUB_TYPE_SUCCESS)
    @Caching(evict = {
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_LIST, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_COUNT, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_POSITION, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_FIRST, allEntries = true)
    })
    public void updateStatus(Long id) {
        // 校验存在
        InteractiveCourseDO courseDO = validateInteractiveCourseExists(id);
        courseDO.setDisplayStatus(courseDO.getDisplayStatus() == 0 ? 1 : 0);
        interactiveCourseService.updateById(courseDO);

        // 记录操作日志上下文
        LogRecordContext.putVariable("courseDO", courseDO);
        LogRecordContext.putVariable("displayStatus", courseDO.getDisplayStatus());
    }

    /**
     * 修改排序
     * <p>
     *  序号在 同一 "HSK等级 +课程类型"下不重复
     *  采用区间调整法，保持序号连续性
     * </p>
     */
    @LogRecord(type = LogRecordType.INTERACTIVE_COURSE_TYPE, bizNo = "{{#id}}", success = INTERACTIVE_COURSE_UPDATE_SORT_SUB_TYPE, subType = "修改排序")
    @Caching(evict = {
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_POSITION, allEntries = true),
        @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_FIRST, allEntries = true)
    })
    public void updateSort(Long id, Integer newSort) {
        // 校验存在
        InteractiveCourseDO course = validateInteractiveCourseExists(id);
        Integer oldSort = course.getSort();

        LogRecordContext.putVariable("oldSort", oldSort);

        // 不需要调整
        if (oldSort.equals(newSort)) {
            return;
        }

        // 在同一 "HSK等级 + 课程类型" 下调整序号
        if (newSort < oldSort) {
            // 向前移动：将[newSort, oldSort-1]范围内的课程序号+1
            interactiveCourseService.lambdaUpdate()
                .setSql("sort = sort + 1")
                .eq(InteractiveCourseDO::getHskLevel, course.getHskLevel())
                .eq(InteractiveCourseDO::getType, course.getType())
                .between(InteractiveCourseDO::getSort, newSort, oldSort - 1)
                .update();
        } else {
            // 向后移动：将[oldSort+1, newSort]范围内的课程序号-1
            interactiveCourseService.lambdaUpdate()
                .setSql("sort = sort - 1")
                .eq(InteractiveCourseDO::getHskLevel, course.getHskLevel())
                .eq(InteractiveCourseDO::getType, course.getType())
                .between(InteractiveCourseDO::getSort, oldSort + 1, newSort)
                .update();
        }

        // 设置该课程的新序号
        course.setSort(newSort);
        course.setUpdateTime(LocalDateTime.now());
        interactiveCourseService.updateById(course);

        LogRecordContext.putVariable("courseDO", course);
    }
}
