package com.xt.hsk.module.edu.service.elitecourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseTeacherPageReqVO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherBasicInfoRespVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseTeacherDO;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 精品课程讲师关联 Service 接口
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
public interface EliteCourseTeacherService extends IService<EliteCourseTeacherDO> {
    PageResult<EliteCourseTeacherDO> selectPage(@Valid EliteCourseTeacherPageReqVO pageReqVO);

    /**
     * 统计讲师关联的课程数量
     *
     * @param teacherIds 讲师ID列表
     * @return 讲师ID -> 关联课程数量的映射
     */
    Map<Long, Integer> countCourseByTeacherIds(List<Long> teacherIds);

    /**
     * 统计讲师关联的未删除课程数量
     *
     * @param teacherIds 讲师ID列表
     * @return 讲师ID -> 关联未删除课程数量的映射
     */
    Map<Long, Integer> countNotDeletedCourseByTeacherIds(List<Long> teacherIds);

    /**
     * 批量保存课程教师
     *
     * @param teacherIds 教师 ID 列表
     * @param courseId   课程 ID
     */
    void saveBatch(List<Long> teacherIds, Long courseId);

    /**
     * 根据课程ID获取讲师基本信息
     *
     * @param teacherId 课程ID
     * @return 讲师基本信息列表
     */
    List<TeacherBasicInfoRespVO> getBasicInfoByCourseId(Long teacherId);
}