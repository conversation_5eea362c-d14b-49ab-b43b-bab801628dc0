package com.xt.hsk.module.edu.service.teacher;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.teacher.TeacherDO;

/**
 * 讲师 Service 接口
 *
 * <AUTHOR>
 */
public interface TeacherService extends IService<TeacherDO> {

    /**
     * 获取讲师分页
     *
     * @param pageReqVO 分页查询参数
     * @return 分页结果
     */
    PageResult<TeacherDO> getTeacherPage(TeacherPageReqVO pageReqVO);

    /**
     * 根据手机区号和手机号查询讲师
     *
     * @param countryCode 手机区号
     * @param mobile      手机号
     * @return 讲师信息
     */
    TeacherDO getByCountryCodeAndMobile(String countryCode, String mobile);

    /**
     * 统计讲师数据总数
     *
     * @param pageReqVO 查询条件
     * @return 数据总数
     */
    Long dataTotalCount(TeacherPageReqVO pageReqVO);

} 