package com.xt.hsk.module.edu.service.textbook;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.TEXTBOOK_HAS_QUESTION;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.UNIT_HAS_QUESTION;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.common.exception.ServerException;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.BinaryUtils;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionUnitCount;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionTextbookCount;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterPageReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterRespVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.TextbookPageReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.TextbookRespVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.TextbookSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitRespVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitTreeReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitTreeResultVO;
import com.xt.hsk.module.edu.dal.dataobject.chapter.ChapterDO;
import com.xt.hsk.module.edu.dal.dataobject.textbook.TextbookDO;
import com.xt.hsk.module.edu.dal.dataobject.unit.UnitDO;
import com.xt.hsk.module.edu.service.chapter.ChapterService;
import com.xt.hsk.module.edu.service.question.QuestionService;
import com.xt.hsk.module.edu.service.question.questiontype.QuestionTypeService;
import com.xt.hsk.module.edu.service.unit.UnitService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 教材 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TextbookManager {

    @Resource
    private TextbookService textbookService;
    @Resource
    private QuestionService questionService;
    @Resource
    private ChapterService chapterService;
    @Resource
    private UnitService unitService;
    @Resource
    private QuestionTypeService questionTypeService;


    public Long createTextbook(TextbookSaveReqVO createReqVO) {
        // 插入
        TextbookDO textbook = BeanUtils.toBean(createReqVO, TextbookDO.class);
        LambdaQueryWrapper<TextbookDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TextbookDO::getNameCn, textbook.getNameCn());
        if (textbookService.count(queryWrapper) > 0) {
            throw exception(500, "该教材名称已存在，请修改后再确认");
        }
        if (createReqVO.getSort() < 1) {
            throw exception(500, "教材序号应该是大于1的正整数，请修改后再确认");
        }
        // 修改序号 小于的不变 大的加1
        LambdaUpdateWrapper<TextbookDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TextbookDO::getHskLevel, textbook.getHskLevel());
        updateWrapper.ge(TextbookDO::getSort, textbook.getSort());
        updateWrapper.setIncrBy(TextbookDO::getSort, 1);

        textbookService.update(updateWrapper);
        textbookService.save(textbook);

        // 设置日志上下文变量
        LogRecordContext.putVariable("textbookId", textbook.getId());
        LogRecordContext.putVariable("textbook", textbook);

        // 返回
        return textbook.getId();
    }


    public void updateTextbook(TextbookSaveReqVO updateReqVO) {
        // 校验存在
        validateTextbookExists(updateReqVO.getId());
        LambdaQueryWrapper<TextbookDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TextbookDO::getNameCn, updateReqVO.getNameCn());
        queryWrapper.ne(TextbookDO::getId, updateReqVO.getId());
        if (textbookService.count(queryWrapper) > 0) {
            throw exception(500, "该教材名称已存在，请修改后再确认");
        }
        if (updateReqVO.getSort() < 1) {
            throw exception(500, "教材序号应该是大于1的正整数，请修改后再确认");
        }
        // 更新
        TextbookDO updateObj = BeanUtils.toBean(updateReqVO, TextbookDO.class);
        TextbookDO textbookDO = textbookService.getById(updateObj.getId());
        int oldSort = textbookDO.getSort();
        // 修改序号 往小修改 [新,旧)+1 往大修改 (旧,新]-1
        if (updateObj.getSort() < oldSort) {
            LambdaUpdateWrapper<TextbookDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TextbookDO::getHskLevel, updateObj.getHskLevel());
            updateWrapper.lt(TextbookDO::getSort, oldSort);
            updateWrapper.ge(TextbookDO::getSort, updateObj.getSort());
            updateWrapper.setIncrBy(TextbookDO::getSort, 1);
            textbookService.update(updateWrapper);
        } else {
            LambdaUpdateWrapper<TextbookDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TextbookDO::getHskLevel, updateObj.getHskLevel());
            updateWrapper.le(TextbookDO::getSort, updateObj.getSort());
            updateWrapper.gt(TextbookDO::getSort, oldSort);
            updateWrapper.setDecrBy(TextbookDO::getSort, 1);
            textbookService.update(updateWrapper);
        }

        textbookService.updateById(updateObj);

        // 设置日志上下文变量
        LogRecordContext.putVariable("textbook", updateObj);
    }


    public void deleteTextbook(Long id) {
        // 校验存在
        validateTextbookExists(id);
        // 校验是否存在题目
        long questionCount = questionService.countByTextbookId(id);
        if (questionCount > 0) {
            throw exception(TEXTBOOK_HAS_QUESTION);
        }
        TextbookDO textbookDO = textbookService.getById(id);

        // 设置日志上下文变量
        LogRecordContext.putVariable("textbook", textbookDO);

        LambdaUpdateWrapper<TextbookDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TextbookDO::getHskLevel, textbookDO.getHskLevel());
        updateWrapper.ge(TextbookDO::getSort, textbookDO.getSort());
        updateWrapper.setDecrBy(TextbookDO::getSort, 1);
        textbookService.update(updateWrapper);
        // 删除
        textbookService.removeById(id);
    }

    private void validateTextbookExists(Long id) {
        if (textbookService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }

    public TextbookRespVO getTextbook(Long id) {
        TextbookDO textbookDO = textbookService.getById(id);
//        List<ChapterDO> chapterDOS = chapterService.getByTextbookId(id);
//        List<ChapterRespVO> bean = BeanUtils.toBean(chapterDOS, ChapterRespVO.class);
        TextbookRespVO respVO = BeanUtils.toBean(textbookDO, TextbookRespVO.class);
//        respVO.setChapterList(bean);

        return respVO;
    }

    public PageResult<TextbookRespVO> getTextbookPage(@Valid TextbookPageReqVO pageReqVO) {
        // 计算总题目数
        PageResult<TextbookDO> pageResult = textbookService.selectPage(pageReqVO);

        PageResult<TextbookRespVO> result = BeanUtils.toBean(pageResult, TextbookRespVO.class);
        Set<Long> textbookIds = pageResult.getList().stream().map(TextbookDO::getId).collect(Collectors.toSet());
        List<QuestionTextbookCount> unitCounts = questionService.countByTextbookIds(textbookIds);
        Map<Long, Integer> textCountMap = unitCounts.stream().collect(Collectors.toMap(QuestionTextbookCount::getTextBookId, QuestionTextbookCount::getCount, (oldValue, newValue) -> oldValue));
        for (TextbookRespVO chapterRespVO : result.getList()) {
            chapterRespVO.setQuestionNumber(textCountMap.getOrDefault(chapterRespVO.getId(), 0));
        }
        return result;
    }

    public Long createChapter(@Valid ChapterSaveReqVO createReqVO) {
        List<Integer> subjects = createReqVO.getSubjects();
        // 求和 subjects
        int sum = 0;
        for (Integer subject : subjects) {
            sum += subject;
        }
        if (createReqVO.getChapterOrder() < 1) {
            throw exception(500, "章节序号应该是大于1的正整数，请修改后再确认");
        }
        TextbookDO textbookDO = textbookService.getById(createReqVO.getTextbookId());
        if (createReqVO.getId() != null) { // 更新
            // 校验存在
            ChapterDO dbChapter = chapterService.getById(createReqVO.getId());
            if (dbChapter == null) {
                throw exception(DATA_NOT_EXIST);
            }
            LambdaQueryWrapper<ChapterDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ChapterDO::getTextbookId, textbookDO.getId());
            queryWrapper.eq(ChapterDO::getChapterNameCn, dbChapter.getChapterNameCn());
            queryWrapper.ne(ChapterDO::getId, dbChapter.getId());
            if (chapterService.count(queryWrapper) > 0) {
                throw exception(500, "该教材下本章节名已存在，请修改后再确认");
            }
            // 修改序号 往小修改 [新,旧)+1 往大修改 (旧,新]-1
            int oldSort = dbChapter.getChapterOrder();
            if (createReqVO.getChapterOrder() < oldSort) {
                LambdaUpdateWrapper<ChapterDO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ChapterDO::getTextbookId, createReqVO.getTextbookId());
                updateWrapper.lt(ChapterDO::getChapterOrder, oldSort);
                updateWrapper.ge(ChapterDO::getChapterOrder, createReqVO.getChapterOrder());
                updateWrapper.setIncrBy(ChapterDO::getChapterOrder, 1);
                chapterService.update(updateWrapper);
            } else {
                LambdaUpdateWrapper<ChapterDO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(ChapterDO::getTextbookId, createReqVO.getTextbookId());
                updateWrapper.le(ChapterDO::getChapterOrder, createReqVO.getChapterOrder());
                updateWrapper.gt(ChapterDO::getChapterOrder, oldSort);
                updateWrapper.setDecrBy(ChapterDO::getChapterOrder, 1);
                chapterService.update(updateWrapper);
            }

            ChapterDO chapter = BeanUtils.toBean(createReqVO, ChapterDO.class);
            chapter.setSubject(sum);
            chapterService.updateById(chapter);
            ChapterDO chapterDO = chapterService.getById(chapter.getId());
            // 更新单元
            List<UnitDO> needAddUnits = new ArrayList<>();
            List<UnitDO> needUpDateUnits = new ArrayList<>();
            createReqVO.getUnitList().forEach(unit -> {
                UnitDO unitDO = BeanUtils.toBean(unit, UnitDO.class);
                if (unit.getId() == null) {
                    unit.setChapterId(chapter.getId());
                    unit.setTextbookId(chapterDO.getTextbookId());
                    unit.setHskLevel(chapterDO.getHskLevel());
                    needAddUnits.add(unitDO);
                } else {
                    needUpDateUnits.add(unitDO);
                }
            });
            unitService.saveBatch(needAddUnits);
            unitService.updateBatchById(needUpDateUnits);

            // 设置日志上下文变量
            LogRecordContext.putVariable("chapterId", chapter.getId());
            LogRecordContext.putVariable("chapter", chapter);
            LogRecordContext.putVariable("textbookName", textbookDO.getNameCn());

            return chapter.getId();
        } else { // 插入
            LambdaQueryWrapper<ChapterDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ChapterDO::getTextbookId, textbookDO.getId());
            queryWrapper.eq(ChapterDO::getChapterNameCn, createReqVO.getChapterNameCn());
            if (chapterService.count(queryWrapper) > 0) {
                throw exception(500, "该教材下本章节名已存在，请修改后再确认");
            }
            LambdaUpdateWrapper<ChapterDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ChapterDO::getTextbookId, createReqVO.getTextbookId());
            updateWrapper.ge(ChapterDO::getChapterOrder, createReqVO.getChapterOrder());
            updateWrapper.setIncrBy(ChapterDO::getChapterOrder, 1);
            chapterService.update(updateWrapper);

            ChapterDO chapter = BeanUtils.toBean(createReqVO, ChapterDO.class);
            chapter.setSubject(sum);
            chapter.setStatus(CommonStatusEnum.ENABLE.getStatus());
            chapter.setHskLevel(textbookDO.getHskLevel());
            chapterService.save(chapter);
            Long chapterId = chapter.getId();
            Long textbookId = chapter.getTextbookId();
            List<UnitDO> needAddUnits = new ArrayList<>();

            createReqVO.getUnitList().forEach(unit -> {
                UnitDO unitDO = BeanUtils.toBean(unit, UnitDO.class);
                unitDO.setChapterId(chapterId);
                unitDO.setTextbookId(textbookId);
                unitDO.setHskLevel(chapter.getHskLevel());
                needAddUnits.add(unitDO);

            });
            unitService.saveBatch(needAddUnits);

            // 设置日志上下文变量
            LogRecordContext.putVariable("chapterId", chapter.getId());
            LogRecordContext.putVariable("chapter", chapter);
            LogRecordContext.putVariable("textbookName", textbookDO.getNameCn());

            return chapter.getId();

        }
    }

    public PageResult<ChapterRespVO> getChapterPage(@Valid ChapterPageReqVO pageReqVO) {
        PageResult<ChapterRespVO> pageResult = chapterService.selectChapterRespVOPage(pageReqVO);
        // 计算总题目数
        Set<Long> unitIds = pageResult.getList().stream().map(ChapterRespVO::getUnitId).collect(Collectors.toSet());
        List<QuestionUnitCount> unitCounts = questionService.countByUnitIds(unitIds);
        Map<Long, Integer> unitCountMap = unitCounts.stream().collect(Collectors.toMap(QuestionUnitCount::getUnitId, QuestionUnitCount::getCount, (oldValue, newValue) -> oldValue));
        for (ChapterRespVO chapterRespVO : pageResult.getList()) {
            chapterRespVO.setQuestionNumber(unitCountMap.get(chapterRespVO.getUnitId()));
        }
        return pageResult;

    }

    public ChapterRespVO getChapter(Long id) {
        ChapterDO chapterDO = chapterService.getById(id);
        ChapterRespVO respVO = BeanUtils.toBean(chapterDO, ChapterRespVO.class);
        List<Integer> list = BinaryUtils.getBinaryBitValues(chapterDO.getSubject());
        respVO.setSubjectList(list);
        // 获取单元
        List<UnitDO> unitDOS = unitService.getByChapterId(id);
        List<UnitRespVO> unitRespVOS = BeanUtils.toBean(unitDOS, UnitRespVO.class);
        if (!unitDOS.isEmpty()) {
            respVO.setUnitList(unitRespVOS);
        }
        return respVO;
    }

    public Boolean deleteUnitById(Long id) {
        UnitDO unitDO = unitService.getById(id);
        if (unitDO == null) {
            throw exception(DATA_NOT_EXIST);
        }
        long questionCount = questionService.countByUnitId(id);
        if (questionCount > 0) {
            throw exception(UNIT_HAS_QUESTION);
        }

        // 获取章节信息用于日志
        ChapterDO chapter = chapterService.getById(unitDO.getChapterId());

        // 设置日志上下文变量
        LogRecordContext.putVariable("unit", unitDO);
        LogRecordContext.putVariable("chapterName", chapter != null ? chapter.getChapterNameCn() : "未知章节");

        unitService.removeById(id);
        return true;
    }

    /**
     * 所属等级/教材/科目/章节/单元
     *
     * @param treeReqVO 请求参数
     * @return 树形结构列表
     */
    public List<UnitTreeResultVO> getUnitTree(UnitTreeReqVO treeReqVO) {
        if (treeReqVO.getHskLevel() == null) {
            return buildHskLevelTree();
        }
        if (treeReqVO.getTextbookId() == null) {
            return buildTextbookTree(treeReqVO.getHskLevel());
        }
        if (treeReqVO.getChapterId() == null) {
            return buildChapterTree(treeReqVO.getTextbookId());
        }
        if (treeReqVO.getSubject() == null) {
            return buildSubjectTree();
        }
        return buildUnitTree(treeReqVO.getChapterId(), treeReqVO.getSubject());
    }

    private List<UnitTreeResultVO> buildHskLevelTree() {
        return Arrays.stream(HskEnum.values()).filter(hskEnum -> hskEnum.getCode() != 0)
                .map(hskEnum -> new UnitTreeResultVO(Long.valueOf(hskEnum.getCode()), hskEnum.getDesc(), 1))
                .collect(Collectors.toList());
    }

    private List<UnitTreeResultVO> buildTextbookTree(Integer hskLevel) {
        List<TextbookDO> textbookDOS = textbookService.getByHskLevel(hskLevel);
        return textbookDOS.stream()
                .map(textbookDO -> new UnitTreeResultVO(textbookDO.getId(), textbookDO.getNameCn(), 2))
                .collect(Collectors.toList());
    }

    private List<UnitTreeResultVO> buildChapterTree(Long textbookId) {
        List<ChapterDO> chapterDOS = chapterService.getByTextbookId(textbookId);
        return chapterDOS.stream()
                .map(chapterDO -> new UnitTreeResultVO(chapterDO.getId(), chapterDO.getChapterNameCn(), 3))
                .collect(Collectors.toList());
    }

    private List<UnitTreeResultVO> buildSubjectTree() {
        return Arrays.stream(SubjectEnum.values())
                .map(subjectEnum -> new UnitTreeResultVO(Long.valueOf(subjectEnum.getCode()), subjectEnum.getDesc(), 4))
                .collect(Collectors.toList());
    }

    private List<UnitTreeResultVO> buildUnitTree(Long chapterId, Integer subject) {
        List<UnitDO> unitDOS = unitService.getByChapterIdAndSubject(chapterId, subject);
        return unitDOS.stream()
                .map(unitDO -> new UnitTreeResultVO(unitDO.getId(), unitDO.getUnitNameCn(), 5))
                .collect(Collectors.toList());
    
    }

    public long countByUnitId(Long id) {
        return questionService.countByUnitId(id);
    }

    public void enableOrDisableChapter(Long id, Integer status) {
        // 获取章节信息
        ChapterDO chapter = chapterService.getById(id);
        TextbookDO textbook = textbookService.getById(chapter.getTextbookId());

        // 设置日志上下文变量
        String statusText = CommonStatusEnum.ENABLE.getStatus().equals(status) ? "启用" : "禁用";
        LogRecordContext.putVariable("statusText", statusText);
        LogRecordContext.putVariable("chapter", chapter);
        LogRecordContext.putVariable("textbookName", textbook.getNameCn());

        LambdaUpdateWrapper<ChapterDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(ChapterDO::getId, id);
        queryWrapper.set(ChapterDO::getStatus, status);
        chapterService.update(queryWrapper);
    }

    public void deleteChapter(@Valid Long id) {
        Long count = countByChapterId(id);
        if (count > 0) {
            throw new ServerException(500, "章节下存在题目,不允许删除");
        }
        ChapterDO chapterDO = chapterService.getById(id);
        TextbookDO textbook = textbookService.getById(chapterDO.getTextbookId());

        // 设置日志上下文变量
        LogRecordContext.putVariable("chapter", chapterDO);
        LogRecordContext.putVariable("textbookName", textbook.getNameCn());

        LambdaUpdateWrapper<ChapterDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChapterDO::getTextbookId, chapterDO.getTextbookId());
        updateWrapper.ge(ChapterDO::getChapterOrder, chapterDO.getChapterOrder());
        updateWrapper.setDecrBy(ChapterDO::getChapterOrder, 1);
        chapterService.update(updateWrapper);

        chapterService.removeById(id);
        // 删除章节下所有单元
        LambdaUpdateWrapper<UnitDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(UnitDO::getChapterId, id);
        unitService.remove(queryWrapper);
    }

    public Long countByChapterId(Long id) {
        return questionService.countByChapterId(id);
    }

    public Long mockTextbook() {
        Integer hskLevel = 32;
        TextbookDO textbookDO = new TextbookDO();
        textbookDO.setNameCn("6");
        textbookDO.setHskLevel(hskLevel);
        textbookDO.setIsShow(1);
        textbookDO.setType(1);
        textbookDO.setSort(1);
        textbookService.save(textbookDO);
        Long textbookId = textbookDO.getId();
        for (int i = 0; i < 90; i++) {
            ChapterDO chapterDO = new ChapterDO();
            chapterDO.setTextbookId(textbookId);
            chapterDO.setSubject(7);
            chapterDO.setChapterNameCn("test" + (i + 1));
            chapterDO.setHskLevel(hskLevel);
            chapterDO.setStatus(0);
            chapterService.save(chapterDO);
            Long chapterId = chapterDO.getId();
            for (int j = 0; j < 4; j++) {
                UnitDO unitDO = new UnitDO();
                unitDO.setChapterId(chapterId);
                unitDO.setTextbookId(textbookId);
                unitDO.setSubject(1);
                unitDO.setSort(j + 1);
                if (j == 0) {
                    unitDO.setUnitNameCn("第一部分");
                } else if (j == 1) {
                    unitDO.setUnitNameCn("第二部分");
                } else if (j == 2) {
                    unitDO.setUnitNameCn("第三部分");
                } else {
                    unitDO.setUnitNameCn("第四部分");
                }
                unitDO.setHskLevel(hskLevel);
                unitService.save(unitDO);
            }
            for (int j = 0; j < 4; j++) {
                UnitDO unitDO = new UnitDO();
                unitDO.setChapterId(chapterId);
                unitDO.setTextbookId(textbookId);
                unitDO.setSubject(2);
                unitDO.setSort(j + 1);
                if (j == 0) {
                    unitDO.setUnitNameCn("第一部分");
                } else if (j == 1) {
                    unitDO.setUnitNameCn("第二部分");
                } else if (j == 2) {
                    unitDO.setUnitNameCn("第三部分");
                } else {
                    unitDO.setUnitNameCn("第四部分");
                }
                unitDO.setHskLevel(hskLevel);
                unitService.save(unitDO);
            }
            for (int j = 0; j < 4; j++) {
                UnitDO unitDO = new UnitDO();
                unitDO.setChapterId(chapterId);
                unitDO.setTextbookId(textbookId);
                unitDO.setSubject(4);
                unitDO.setSort(j + 1);
                if (j == 0) {
                    unitDO.setUnitNameCn("第一部分");
                } else if (j == 1) {
                    unitDO.setUnitNameCn("第二部分");
                } else if (j == 2) {
                    unitDO.setUnitNameCn("第三部分");
                } else {
                    unitDO.setUnitNameCn("第四部分");
                }
                unitDO.setHskLevel(hskLevel);
                unitService.save(unitDO);
            }
        }


        return 0L;
    }
}