package com.xt.hsk.module.edu.service.interactivecourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitResourceRelDO;
import java.util.List;

/**
 * 互动课单元资源关联 Service 接口
 *
 * <AUTHOR>
 */
public interface InteractiveCourseUnitResourceRelService extends
    IService<InteractiveCourseUnitResourceRelDO> {

    /**
     * 批量创建单元资源关联
     *
     * @param unitId       单元ID
     * @param resourceType 资源类型
     * @param resourceIds  资源ID列表
     * @return 单元资源关联列表
     */
    List<InteractiveCourseUnitResourceRelDO> batchCreateUnitResourceRel(Long unitId,
        Integer resourceType, List<Long> resourceIds);

    /**
     * 删除单元下指定资源类型的所有关联
     *
     * @param unitId       单元ID
     * @param resourceType 资源类型
     */
    void deleteUnitResourceRelByUnitIdAndType(Long unitId, Integer resourceType);

} 