package com.xt.hsk.module.edu.controller.app.exam.vo;

import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 模考 resp vo
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class ExamMetadataRespVO {

    /**
     * 模考ID
     */
    private Long examId;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 模考名称
     */
    private String name;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer type;

    /**
     * 模考封面图片URL
     */
    private String coverUrl;

    /**
     * 题目总数
     */
    private Integer totalQuestionCount;

    /**
     * 总时间（分）
     */
    private Integer totalDuration;

    /**
     * 模考描述
     */
    private String description;


    private List<ExamSubjectMetadataAppRespVO> examSubjectMetadataList;

}