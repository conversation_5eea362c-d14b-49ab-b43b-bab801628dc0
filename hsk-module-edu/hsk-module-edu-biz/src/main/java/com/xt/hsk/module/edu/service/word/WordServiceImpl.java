package com.xt.hsk.module.edu.service.word;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordMeaningDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordTagDO;
import com.xt.hsk.module.edu.dal.mysql.word.WordMeaningMapper;
import com.xt.hsk.module.edu.dal.mysql.word.WordTagMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.xt.hsk.module.edu.dal.dataobject.word.WordDO;

import com.xt.hsk.module.edu.dal.mysql.word.WordMapper;

import java.util.Collections;
import java.util.List;


/**
 * 汉语词典基础数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class WordServiceImpl extends ServiceImpl<WordMapper, WordDO> implements WordService {

    @Resource
    private WordMapper wordMapper;
    @Resource
    private WordMeaningMapper wordMeaningMapper;
    @Resource
    private WordTagMapper wordTagMapper;

    @Override
    public PageResult<WordDO> selectPage(WordPageReqVO pageReqVO) {

        return wordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<WordMeaningDO> getWordsMeaningsByWordIds(List<Long> wordIds) {
        if (wordIds == null || wordIds.isEmpty()) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<WordMeaningDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WordMeaningDO::getWordId, wordIds);
        return wordMeaningMapper.selectList(queryWrapper);
    }

    @Override
    public List<WordTagDO> getWordsTagsByWordIds(List<Long> wordIds) {
        if (wordIds == null || wordIds.isEmpty()) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<WordTagDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WordTagDO::getWordId, wordIds);
        return wordTagMapper.selectList(queryWrapper);
    }

    @Override
    public Long dataTotalCount(WordPageReqVO dto) {
        return wordMapper.dataTotalCount(dto);
    }

    @Override
    public List<WordDO> getWordByName(String name) {
        LambdaQueryWrapper<WordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WordDO::getWord, name);
        queryWrapper.last("limit 10");
        return wordMapper.selectList(queryWrapper);
    }

}