package com.xt.hsk.module.edu.service.question.questiondetailversion;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.question.questiondetailversion.vo.QuestionDetailVersionPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetailversion.QuestionDetailVersionDO;
import com.xt.hsk.module.edu.dal.mysql.questiondetailversion.QuestionDetailVersionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 题目详情表版本库 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionDetailVersionServiceImpl extends ServiceImpl<QuestionDetailVersionMapper, QuestionDetailVersionDO> implements QuestionDetailVersionService {

    @Resource
    private QuestionDetailVersionMapper questionDetailVersionMapper;

    @Override
    public PageResult<QuestionDetailVersionDO> selectPage(QuestionDetailVersionPageReqVO pageReqVO) {

        return questionDetailVersionMapper.selectPage(pageReqVO);
    }

}