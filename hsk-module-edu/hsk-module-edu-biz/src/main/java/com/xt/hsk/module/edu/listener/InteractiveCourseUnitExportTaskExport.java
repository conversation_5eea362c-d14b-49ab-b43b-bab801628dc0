package com.xt.hsk.module.edu.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.BasicEnumUtil;
import com.xt.hsk.framework.translate.core.TranslateUtils;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitPageReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitRespVO;
import com.xt.hsk.module.edu.enums.interactivecourse.UnitQuestionSourceTypeEnum;
import com.xt.hsk.module.edu.manager.interactivecourse.admin.InteractiveCourseUnitManager;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseUnitService;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 互动课单元导出任务导出器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Component
@Slf4j
public class InteractiveCourseUnitExportTaskExport extends
    BaseEasyExcelExport<InteractiveCourseUnitRespVO> {

    @Resource
    private InteractiveCourseUnitService interactiveCourseUnitService;

    @Resource
    private InteractiveCourseUnitManager interactiveCourseUnitManager;

    // 配置ObjectMapper忽略未知字段
    private final ObjectMapper objectMapper = new ObjectMapper()
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    protected List<List<String>> getExcelHead() {
        // 直接在这里定义表头，方便开发人员查看和维护
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("单元ID"));
        head.add(Collections.singletonList("单元名称"));
        head.add(Collections.singletonList("单元时长"));
        head.add(Collections.singletonList("题目来源"));
        head.add(Collections.singletonList("题目类型"));
        head.add(Collections.singletonList("展示状态"));
        head.add(Collections.singletonList("单元序号"));
        head.add(Collections.singletonList("最近更新人"));
        head.add(Collections.singletonList("创建时间"));
        head.add(Collections.singletonList("最近更新时间"));
        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        InteractiveCourseUnitPageReqVO dto = new InteractiveCourseUnitPageReqVO();
        BeanUtil.copyProperties(conditions, dto);
        // 如果有选中的ID，返回选中的数量
        if (CollUtil.isNotEmpty(dto.getIds())) {
            return (long) dto.getIds().size();
        }

        return interactiveCourseUnitService.dataTotalCount(dto);
    }


    /**
     * 构建数据列表
     *
     * @param resultList     结果列表
     * @param queryCondition 查询条件
     * @param pageNo         页码
     * @param pageSize       页面大小
     */
    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition,
        Long pageNo, Long pageSize) {
        InteractiveCourseUnitPageReqVO dto = new InteractiveCourseUnitPageReqVO();
        BeanUtil.copyProperties(queryCondition, dto);
        dto.setPageNo(Math.toIntExact(pageNo));
        dto.setPageSize(Math.toIntExact(pageSize));

        PageResult<InteractiveCourseUnitRespVO> voPage = interactiveCourseUnitManager.getInteractiveCourseUnitVoPage(
            dto);
        List<InteractiveCourseUnitRespVO> unitRespVOList = voPage.getList();

        // 手动触发翻译
        if (CollUtil.isNotEmpty(unitRespVOList)) {
            unitRespVOList = TranslateUtils.translate(unitRespVOList);
        }

        for (InteractiveCourseUnitRespVO interactiveCourseUnitRespVO : unitRespVOList) {
            List<String> row = new ArrayList<>();

            // 单元ID
            row.add(
                interactiveCourseUnitRespVO.getId() != null ? interactiveCourseUnitRespVO.getId()
                    .toString()
                    : "");

            // 单元名称
            row.add(interactiveCourseUnitRespVO.getUnitNameCn() != null
                ? interactiveCourseUnitRespVO.getUnitNameCn() : "");

            // 单元时长（转分钟）
            if (interactiveCourseUnitRespVO.getRecommendedDuration() != null) {
                int minutes = interactiveCourseUnitRespVO.getRecommendedDuration() / 60;
                row.add(minutes + "分钟");
            } else {
                row.add("");
            }

            // 题目来源
            row.add(BasicEnumUtil.getDescByCode(UnitQuestionSourceTypeEnum.class,
                interactiveCourseUnitRespVO.getQuestionSource()));

            // 题目类型 - 这里需要根据题目来源进一步细分
            row.add(interactiveCourseUnitRespVO.getUnitQuestionTypeDesc());

            // 展示状态
            row.add(
                Boolean.TRUE.equals(interactiveCourseUnitRespVO.getDisplayStatus()) ? "显示"
                    : "隐藏");

            // 单元序号
            row.add(interactiveCourseUnitRespVO.getSort() != null
                ? interactiveCourseUnitRespVO.getSort()
                .toString() : "");

            // 最近更新人（这里会显示翻译后的用户名）
            row.add(interactiveCourseUnitRespVO.getUpdaterName() != null
                ? interactiveCourseUnitRespVO.getUpdaterName() : "");

            // 创建时间
            row.add(interactiveCourseUnitRespVO.getCreateTime() != null ?
                DateUtil.format(interactiveCourseUnitRespVO.getCreateTime(), "yyyy-MM-dd HH:mm:ss")
                : "");

            // 最近更新时间
            row.add(interactiveCourseUnitRespVO.getUpdateTime() != null ?
                DateUtil.format(interactiveCourseUnitRespVO.getUpdateTime(), "yyyy-MM-dd HH:mm:ss")
                : "");

            resultList.add(row);
        }
        log.info("互动课单元导出，当前页：{}，每页条数：{}，总条数：{}", pageNo, pageSize,
            voPage.getTotal());
    }
} 