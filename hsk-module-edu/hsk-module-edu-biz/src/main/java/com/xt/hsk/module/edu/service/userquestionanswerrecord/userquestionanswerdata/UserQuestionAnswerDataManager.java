package com.xt.hsk.module.edu.service.userquestionanswerrecord.userquestionanswerdata;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;

import jakarta.validation.Valid;

import com.xt.hsk.module.edu.controller.admin.userquestionanswerdata.vo.*;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerdata.UserQuestionAnswerDataDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;


/**
 * 用户题目作答数据 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserQuestionAnswerDataManager {

    @Resource
    private UserQuestionAnswerDataService userQuestionAnswerDataService;


    public Long createUserQuestionAnswerData(UserQuestionAnswerDataSaveReqVO createReqVO) {
        // 插入
        UserQuestionAnswerDataDO userQuestionAnswerData = BeanUtils.toBean(createReqVO, UserQuestionAnswerDataDO.class);
        userQuestionAnswerDataService.save(userQuestionAnswerData);

        // 返回
        return userQuestionAnswerData.getId();
    }


    public void updateUserQuestionAnswerData(UserQuestionAnswerDataSaveReqVO updateReqVO) {
        // 校验存在
        validateUserQuestionAnswerDataExists(updateReqVO.getId());
        // 更新
        UserQuestionAnswerDataDO updateObj = BeanUtils.toBean(updateReqVO, UserQuestionAnswerDataDO.class);
        userQuestionAnswerDataService.updateById(updateObj);
    }


    public void deleteUserQuestionAnswerData(Long id) {
        // 校验存在
        validateUserQuestionAnswerDataExists(id);
        // 删除
        userQuestionAnswerDataService.removeById(id);
    }

    private void validateUserQuestionAnswerDataExists(Long id) {
        if (userQuestionAnswerDataService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public UserQuestionAnswerDataDO getUserQuestionAnswerData(Long id) {
        return userQuestionAnswerDataService.getById(id);
    }

    public PageResult<UserQuestionAnswerDataDO> getUserQuestionAnswerDataPage(@Valid UserQuestionAnswerDataPageReqVO pageReqVO) {
        return userQuestionAnswerDataService.selectPage(pageReqVO);
    }

}