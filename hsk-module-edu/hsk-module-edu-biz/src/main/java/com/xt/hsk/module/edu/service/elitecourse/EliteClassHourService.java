package com.xt.hsk.module.edu.service.elitecourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteClassHourDO;
import com.xt.hsk.module.edu.manager.elitecourse.dto.CourseHourCountDto;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 精品课课时 Service 接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface EliteClassHourService extends IService<EliteClassHourDO> {
    PageResult<EliteClassHourDO> selectPage(@Valid EliteClassHourPageReqVO pageReqVO);

    /**
     * 根据id获得精品课课时
     *
     * @param id 精品课课时id
     * @return 精品课课时DO
     */
    EliteClassHourDO getEliteClassHour(Long id);

    /**
     * 分页获取精品课时
     * <p>
     * 先按章节排序升序，再按课时排序升序
     *
     * @param pageReqVO 查询条件
     * @return 精品课时
     */
    PageResult<EliteClassHourDO> getClassHourPage(EliteClassHourPageReqVO pageReqVO);

    List<CourseHourCountDto> getEliteClassHourCount(List<Long> courseIds);
}