package com.xt.hsk.module.edu.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.translate.core.TranslateUtils;
import com.xt.hsk.module.edu.controller.admin.question.vo.OptionContentVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetail.QuestionDetailDO;
import com.xt.hsk.module.edu.service.question.QuestionManager;
import com.xt.hsk.module.edu.service.question.QuestionService;
import com.xt.hsk.module.edu.service.question.questiondetail.QuestionDetailService;
import com.xt.hsk.module.edu.util.QuestionContentUtil;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class QuestionExportTaskExport extends
    BaseEasyExcelExport<QuestionRespVO> {

    @Autowired
    private QuestionManager questionManager;

    @Autowired
    private QuestionService questionService;

    @Resource
    private QuestionDetailService questionDetailService;



    @Override
    protected List<List<String>> getExcelHead() {
        // 直接在这里定义表头，方便开发人员查看和维护
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("材料id"));
        head.add(Collections.singletonList("HSK等级"));
        head.add(Collections.singletonList("教材"));
        head.add(Collections.singletonList("章节"));
        head.add(Collections.singletonList("单元"));
        head.add(Collections.singletonList("题型"));
        head.add(Collections.singletonList("科目"));
        head.add(Collections.singletonList("材料音频"));
        head.add(Collections.singletonList("材料图片"));
        head.add(Collections.singletonList("材料文字"));
        head.add(Collections.singletonList("材料选项A"));
        head.add(Collections.singletonList("材料选项B"));
        head.add(Collections.singletonList("材料选项C"));
        head.add(Collections.singletonList("材料选项D"));
        head.add(Collections.singletonList("材料选项E"));
        head.add(Collections.singletonList("材料选项F"));
        head.add(Collections.singletonList("材料选项G"));
        head.add(Collections.singletonList("题干文字"));
        head.add(Collections.singletonList("题干音频"));
        head.add(Collections.singletonList("题干图片"));
        head.add(Collections.singletonList("参考答案"));
        head.add(Collections.singletonList("文字题目解析中文"));
        head.add(Collections.singletonList("文字题目解析英文"));
        head.add(Collections.singletonList("文字题目解析越南文"));
        head.add(Collections.singletonList("音频题目解析"));
        head.add(Collections.singletonList("视频题目解析"));
        head.add(Collections.singletonList("题目选项A"));
        head.add(Collections.singletonList("题目选项B"));
        head.add(Collections.singletonList("题目选项C"));
        head.add(Collections.singletonList("题目选项D"));
        head.add(Collections.singletonList("题目选项E"));
        head.add(Collections.singletonList("题目选项F"));
        head.add(Collections.singletonList("题目选项G"));
        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        // 使用配置好的 ObjectMapper 将 Map 转换为 InteractiveCoursePageReqVO 对象，忽略taskName字段
        QuestionPageReqVO dto = objectMapper.convertValue(conditions,
                QuestionPageReqVO.class);

        // 如果有选中的ID，返回选中的数量
        if (CollUtil.isNotEmpty(dto.getIds())) {
            return (long) dto.getIds().size();
        }
       return questionService.queryQuestionCount(dto);

    }

    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition, Long pageNo, Long pageSize) {
        try {
            QuestionPageReqVO pageReqVO = objectMapper.convertValue(queryCondition, QuestionPageReqVO.class);
            pageReqVO.setPageNo(Math.toIntExact(pageNo));
            pageReqVO.setPageSize(Math.toIntExact(pageSize));

            PageResult<QuestionRespVO> questionPage = questionManager.getQuestionPage(pageReqVO);
            List<QuestionRespVO> pageList = questionPage.getList();
            if (CollUtil.isNotEmpty(pageList)) {
                // 手动触发翻译
                pageList = TranslateUtils.translate(pageList);
                for (QuestionRespVO questionRespVO : pageList) {
                    // 查询题目
                    List<QuestionDetailDO> questionDetailDOS = questionDetailService.selectListByQuestionId(questionRespVO.getId());
                    for (QuestionDetailDO questionDetailDO : questionDetailDOS) {
                        List<String> row = new ArrayList<>();
                        // 材料id
                        row.add(questionRespVO.getId() != null ? questionRespVO.getId().toString() : "");
                        // HSK等级
                        row.add(questionRespVO.getHskLevelDesc() != null ? questionRespVO.getHskLevelDesc() : "");
                        // 教材
                        row.add(questionRespVO.getTextbookNameCn() != null ? questionRespVO.getTextbookNameCn() : "");
                        // 章节
                        row.add(questionRespVO.getChapterNameCn() != null ? questionRespVO.getChapterNameCn() : "");
                        // 单元
                        row.add(questionRespVO.getUnitNameCn() != null ? questionRespVO.getUnitNameCn() : "");
                        // 题型
                        row.add(questionRespVO.getQuestionTypeDesc() != null ? questionRespVO.getQuestionTypeDesc() : "");
                        // 科目
                        row.add(questionRespVO.getSubjectDesc() != null ? questionRespVO.getSubjectDesc() : "");
                        // 材料音频
                        row.add(questionRespVO.getMeterialAudio() != null ? questionRespVO.getMeterialAudio() : "");
                        // 材料图片
                        row.add(questionRespVO.getMeterialImage() != null ? questionRespVO.getMeterialImage() : "");
                        // 材料文字
                        row.add(questionRespVO.getMeterialContent() != null ? questionRespVO.getMeterialContent() : "");
                        List<OptionContentVO> optionsContent = questionRespVO.getOptionsContent();
                        Map<String, OptionContentVO> map=new HashMap<>();
                        if (CollectionUtil.isNotEmpty(optionsContent)){
                            // 根据key进行分组,不是分组成List，就是对象OptionContentVO
                            map = optionsContent.stream()
                                    .collect(Collectors.toMap(
                                            OptionContentVO::getKey,
                                            vo -> vo,
                                            (existing, replacement) -> existing  // 保留第一个，或用 replacement 保留最后一个
                                    ));

                        }
                        boolean a = map.get("A") == null ? row.add("") : row.add(map.get("A").getContent().replaceAll("^\"|\"$", ""));
                        boolean b = map.get("B") == null ? row.add("") : row.add(map.get("B").getContent().replaceAll("^\"|\"$", ""));
                        boolean c = map.get("C") == null ? row.add("") : row.add(map.get("C").getContent().replaceAll("^\"|\"$", ""));
                        boolean d = map.get("D") == null ? row.add("") : row.add(map.get("D").getContent().replaceAll("^\"|\"$", ""));
                        boolean e = map.get("E") == null ? row.add("") : row.add(map.get("E").getContent().replaceAll("^\"|\"$", ""));
                        boolean f = map.get("F") == null ? row.add("") : row.add(map.get("F").getContent().replaceAll("^\"|\"$", ""));
                        boolean g = map.get("G") == null ? row.add("") : row.add(map.get("G").getContent().replaceAll("^\"|\"$", ""));
                        // 题干文字
                        row.add(questionDetailDO.getAttachmentContent() != null ? questionDetailDO.getAttachmentContent() : "");
                        // 题干音频
                        row.add(questionDetailDO.getAttachmentAudio() != null ? questionDetailDO.getAttachmentAudio() : "");
                        // 题干图片
                        row.add(questionDetailDO.getAttachmentImage() != null ? questionDetailDO.getAttachmentImage() : "");
                        // 答案
                        row.add(questionDetailDO.getAnswer() != null ? questionDetailDO.getAnswer() : "");
                        // 文字题目解析中文
                        row.add(questionDetailDO.getExplainTextCn() != null ? questionDetailDO.getExplainTextCn() : "");
                        // 文字题目解析英文
                        row.add(questionDetailDO.getExplainTextEn() != null ? questionDetailDO.getExplainTextEn() : "");
                        // 音频题目解析越南
                        row.add(questionDetailDO.getExplainTextOt() != null ? questionDetailDO.getExplainTextOt() : "");
                        // 音频题目解析音频
                        row.add(questionDetailDO.getExplainAudio() != null ? questionDetailDO.getExplainAudio() : "");
                        // 音频题目解析视频
                        row.add(questionDetailDO.getExplainVideo() != null ? questionDetailDO.getExplainVideo() : "");

                        String detailDOOptions = questionDetailDO.getOptions();
                        Map<String, OptionContentVO> detailDOHashMap = new HashMap<>();
                        if (StringUtil.isNotBlank(detailDOOptions)){
                            List<OptionContentVO> optionContentVOS = QuestionContentUtil.optionConvert(detailDOOptions);
                            // 根据key进行分组,不是分组成List，就是对象OptionContentVO
                            detailDOHashMap = optionContentVOS.stream()
                                    .collect(Collectors.toMap(
                                            OptionContentVO::getKey,
                                            vo -> vo,
                                            (existing, replacement) -> existing  // 保留第一个，或用 replacement 保留最后一个
                                    ));
                        }
                        boolean detailDOA = detailDOHashMap.get("A") == null ? row.add("") : row.add(detailDOHashMap.get("A").getContent().replaceAll("^\"|\"$", ""));
                        boolean detailDOB = detailDOHashMap.get("B") == null ? row.add("") : row.add(detailDOHashMap.get("B").getContent().replaceAll("^\"|\"$", ""));
                        boolean detailDOC = detailDOHashMap.get("C") == null ? row.add("") : row.add(detailDOHashMap.get("C").getContent().replaceAll("^\"|\"$", ""));
                        boolean detailDOD = detailDOHashMap.get("D") == null ? row.add("") : row.add(detailDOHashMap.get("D").getContent().replaceAll("^\"|\"$", ""));
                        boolean detailDOE = detailDOHashMap.get("E") == null ? row.add("") : row.add(detailDOHashMap.get("E").getContent().replaceAll("^\"|\"$", ""));
                        boolean detailDOF = detailDOHashMap.get("F") == null ? row.add("") : row.add(detailDOHashMap.get("F").getContent().replaceAll("^\"|\"$", ""));
                        boolean detailDOG = detailDOHashMap.get("G") == null ? row.add("") : row.add(detailDOHashMap.get("G").getContent().replaceAll("^\"|\"$", ""));
                        resultList.add(row);

                    }
                }
            }
            log.info("字词库导出当前页：{}每页条数：{}总条数：{}", pageNo, pageSize,
                    questionPage.getTotal());
        } catch (Exception e) {
            log.error("构建字词库数据列表时出错页码：{}页面大小：{}，错误：{}", pageNo, pageSize,
                    e.getMessage(), e);
            throw new ServiceException(500, "导出字词库数据失败：" + e.getMessage());
        }

    }
}
