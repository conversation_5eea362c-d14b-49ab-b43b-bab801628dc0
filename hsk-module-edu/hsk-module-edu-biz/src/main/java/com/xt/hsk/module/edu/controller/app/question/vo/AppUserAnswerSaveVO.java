package com.xt.hsk.module.edu.controller.app.question.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class AppUserAnswerSaveVO implements Serializable {
    /**
     * 记录id
     */
    private Long recordId;
    /**
     * 题目主键
     */
    private Long questionId;
    /**
     * 版本
     */
    private Integer version;
    /**
     * 1 进行中 2 已提交 3 ai批改未完成  4 ai批改完成
     */
    private Integer recordStatus;
    /**
     * 练习模式：1-单独练习 2-全真模考 3-30分钟模考 4-15分钟模考
     */
    private Integer practiceMode;

    /**
     * 考试/练习ID（关联到具体的考试或练习）
     */
    private Long practiceId;
    /**
     * 作答耗时（秒）
     */
    private Integer answerTime;


    /**
     * 作答日期（便于按日统计）
     */
    private LocalDate answerDate;


    /**
     * 开始作答时间
     */
    private LocalDateTime startTime;
    /**
     * 结束作答时间
     */
    private LocalDateTime endTime;
    /**
     * 题目总数量
     */
    private Integer questionNum;
    /**
     * 已正确数量
     */
    private Integer correctNum;
    /**
     * 单元部分
     */
    private Integer unitSort;
    /**
     * 作答明细
     */
    private List<AppUserAnswerDetailSaveVO> userAnswerDetailList;
}
