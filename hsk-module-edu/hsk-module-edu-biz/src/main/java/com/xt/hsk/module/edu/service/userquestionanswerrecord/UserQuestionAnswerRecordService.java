package com.xt.hsk.module.edu.service.userquestionanswerrecord;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionSearchReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionStatisticsRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerrecord.UserQuestionAnswerRecordDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import jakarta.validation.Valid;
import com.xt.hsk.module.edu.controller.admin.userquestionanswerrecord.vo.UserQuestionAnswerRecordPageReqVO;

import java.util.List;

/**
 * 用户题目作答记录 Service 接口
 *
 * <AUTHOR>
 */
public interface UserQuestionAnswerRecordService extends IService<UserQuestionAnswerRecordDO> {
   PageResult<UserQuestionAnswerRecordDO> selectPage(@Valid UserQuestionAnswerRecordPageReqVO pageReqVO);

    List<QuestionTypeCountRespVO> getUserUnitSortQuestionTypeCount(QuestionSearchReqVO reqVO);
}