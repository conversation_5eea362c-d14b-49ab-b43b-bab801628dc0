package com.xt.hsk.module.edu.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.BasicEnumUtil;
import com.xt.hsk.framework.translate.core.TranslateUtils;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseRespVO;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseTypeEnum;
import com.xt.hsk.module.edu.manager.interactivecourse.admin.InteractiveCourseManager;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseService;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 互动课导出任务导出器
 *
 * <AUTHOR>
 * @since 2025/05/26
 */
@Component
@Slf4j
public class InteractiveCourseExportTaskExport extends
    BaseEasyExcelExport<InteractiveCourseRespVO> {

    @Resource
    private InteractiveCourseService interactiveCourseService;

    @Resource
    private InteractiveCourseManager interactiveCourseManager;

    // 配置ObjectMapper忽略未知字段
    private final ObjectMapper objectMapper = new ObjectMapper()
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    protected List<List<String>> getExcelHead() {
        // 直接在这里定义表头，方便开发人员查看和维护
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("课程ID"));
        head.add(Collections.singletonList("课程类型"));
        head.add(Collections.singletonList("课程封面"));
        head.add(Collections.singletonList("课程名称"));
        head.add(Collections.singletonList("展示状态"));
        head.add(Collections.singletonList("课程序号"));
        head.add(Collections.singletonList("单元数量"));
        head.add(Collections.singletonList("所属等级"));
        head.add(Collections.singletonList("最近更新人"));
        head.add(Collections.singletonList("创建时间"));
        head.add(Collections.singletonList("最近更新时间"));
        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        // 使用配置好的 ObjectMapper 将 Map 转换为 InteractiveCoursePageReqVO 对象，忽略taskName字段
        InteractiveCoursePageReqVO dto = new InteractiveCoursePageReqVO();
        BeanUtil.copyProperties(conditions, dto);
        // 如果有选中的ID，返回选中的数量
        if (CollUtil.isNotEmpty(dto.getIds())) {
            return (long) dto.getIds().size();
        }

        return interactiveCourseService.dataTotalCount(dto);
    }

    /**
     * 构建数据列表
     *
     * @param resultList     结果列表
     * @param queryCondition 查询条件
     * @param pageNo         页码
     * @param pageSize       页面大小
     */
    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition,
        Long pageNo, Long pageSize) {
        // 使用配置好的 ObjectMapper 将 Map 转换为 InteractiveCoursePageReqVO 对象，忽略taskName字段
        InteractiveCoursePageReqVO dto = new InteractiveCoursePageReqVO();
        BeanUtil.copyProperties(queryCondition, dto);
        dto.setPageNo(Math.toIntExact(pageNo));
        dto.setPageSize(Math.toIntExact(pageSize));

        PageResult<InteractiveCourseRespVO> voPage = interactiveCourseManager.getInteractiveCourseVoPage(
            dto);
        List<InteractiveCourseRespVO> courseRespVOList = voPage.getList();

        // 手动触发翻译
        if (CollUtil.isNotEmpty(courseRespVOList)) {
            courseRespVOList = TranslateUtils.translate(courseRespVOList);
        }

        for (InteractiveCourseRespVO interactiveCourseRespVO : courseRespVOList) {
            List<String> row = new ArrayList<>();

            // 课程编号
            row.add(
                interactiveCourseRespVO.getId() != null ? interactiveCourseRespVO.getId().toString()
                    : "");

            // 课程类型
            row.add(
                BasicEnumUtil.getDescByCode(InteractiveCourseTypeEnum.class,
                    interactiveCourseRespVO.getType()));

            // 课程封面
            row.add(interactiveCourseRespVO.getCoverUrl() != null
                ? interactiveCourseRespVO.getCoverUrl() : "");

            // 课程名称
            row.add(interactiveCourseRespVO.getCourseNameCn() != null
                ? interactiveCourseRespVO.getCourseNameCn() : "");

            // 展示状态
            Integer displayStatus = interactiveCourseRespVO.getDisplayStatus();
            if (displayStatus == null) {
                row.add("");
            } else if (displayStatus == 0) {
                row.add("隐藏");
            } else if (displayStatus == 1) {
                row.add("显示");
            } else {
                row.add("");
            }

            // 课程序号
            row.add(interactiveCourseRespVO.getSort() != null ? interactiveCourseRespVO.getSort()
                .toString() : "");

            // 单元数量
            row.add(interactiveCourseRespVO.getUnitCount() != null
                ? interactiveCourseRespVO.getUnitCount().toString() : "");

            // HSK等级
            row.add(BasicEnumUtil.getDescByCode(HskEnum.class,
                interactiveCourseRespVO.getHskLevel()));


            // 最后更新人（这里会显示翻译后的用户名）
            row.add(interactiveCourseRespVO.getUpdaterName() != null
                ? interactiveCourseRespVO.getUpdaterName() : "");

            // 创建时间
            row.add(interactiveCourseRespVO.getCreateTime() != null ?
                DateUtil.format(interactiveCourseRespVO.getCreateTime(), "yyyy-MM-dd HH:mm:ss")
                : "");

            // 最后更新时间
            row.add(interactiveCourseRespVO.getUpdateTime() != null ?
                DateUtil.format(interactiveCourseRespVO.getUpdateTime(), "yyyy-MM-dd HH:mm:ss")
                : "");

            resultList.add(row);
        }
        log.info("互动课导出，当前页：{}，每页条数：{}，总条数：{}", pageNo, pageSize, voPage.getTotal());
    }
}
