package com.xt.hsk.module.edu.service.exam;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRulePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDO;
import jakarta.validation.Valid;

/**
 * 模考组卷规则 Service 接口
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
public interface ExamPaperRuleService extends IService<ExamPaperRuleDO> {
    PageResult<ExamPaperRuleDO> selectPage(@Valid ExamPaperRulePageReqVO pageReqVO);

    /**
     * 根据id获取模考组卷规则DO
     *
     * @param id 模考组卷规则ID
     * @return 模考组卷规则DO
     */
    ExamPaperRuleDO getPaperRule(Long id);
}