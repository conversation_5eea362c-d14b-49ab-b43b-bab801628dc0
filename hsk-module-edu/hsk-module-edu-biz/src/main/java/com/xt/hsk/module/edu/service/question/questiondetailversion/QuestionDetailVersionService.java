package com.xt.hsk.module.edu.service.question.questiondetailversion;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.question.questiondetailversion.vo.QuestionDetailVersionPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetailversion.QuestionDetailVersionDO;
import jakarta.validation.Valid;

/**
 * 题目详情表版本库 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionDetailVersionService extends IService<QuestionDetailVersionDO> {
    PageResult<QuestionDetailVersionDO> selectPage(@Valid QuestionDetailVersionPageReqVO pageReqVO);

}