package com.xt.hsk.module.edu.service.userpracticerecord;

import com.xt.hsk.module.edu.service.userpracticerecord.UserPracticeRecordService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;

import jakarta.validation.Valid;

import java.util.*;

import com.xt.hsk.module.edu.controller.admin.userpracticerecord.vo.*;
import com.xt.hsk.module.edu.dal.dataobject.userpracticerecord.UserPracticeRecordDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.framework.common.util.object.BeanUtils;


/**
 * 用户练习记录 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserPracticeRecordManager {

    @Resource
    private UserPracticeRecordService userPracticeRecordService;


    public Long createUserPracticeRecord(UserPracticeRecordSaveReqVO createReqVO) {
        // 插入
        UserPracticeRecordDO userPracticeRecord = BeanUtils.toBean(createReqVO, UserPracticeRecordDO.class);
        userPracticeRecordService.save(userPracticeRecord);

        // 返回
        return userPracticeRecord.getId();
    }


    public void updateUserPracticeRecord(UserPracticeRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateUserPracticeRecordExists(updateReqVO.getId());
        // 更新
        UserPracticeRecordDO updateObj = BeanUtils.toBean(updateReqVO, UserPracticeRecordDO.class);
        userPracticeRecordService.updateById(updateObj);
    }


    public void deleteUserPracticeRecord(Long id) {
        // 校验存在
        validateUserPracticeRecordExists(id);
        // 删除
        userPracticeRecordService.removeById(id);
    }

    private void validateUserPracticeRecordExists(Long id) {
        if (userPracticeRecordService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public UserPracticeRecordDO getUserPracticeRecord(Long id) {
        return userPracticeRecordService.getById(id);
    }

    public PageResult<UserPracticeRecordDO> getUserPracticeRecordPage(@Valid UserPracticeRecordPageReqVO pageReqVO) {
        return userPracticeRecordService.selectPage(pageReqVO);
    }

}