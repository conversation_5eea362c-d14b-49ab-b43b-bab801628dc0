package com.xt.hsk.module.edu.service.elitecourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteChapterPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteChapterDO;
import jakarta.validation.Valid;

/**
 * 精品课章节 Service 接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface EliteChapterService extends IService<EliteChapterDO> {
    PageResult<EliteChapterDO> selectPage(@Valid EliteChapterPageReqVO pageReqVO);

    /**
     * 获取课程下最大排序
     * <p>
     * 获取最大排序，如果没有则返回0
     *
     * @param courseId 课程 ID
     * @return 最大排序
     */
    Integer getMaxSortByCourseId(Long courseId);
}