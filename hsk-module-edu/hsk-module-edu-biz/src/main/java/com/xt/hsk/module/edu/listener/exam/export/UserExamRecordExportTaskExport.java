package com.xt.hsk.module.edu.listener.exam.export;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.translate.core.TranslateUtils;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageRespVO;
import com.xt.hsk.module.edu.manager.exam.admin.UserExamRecordAdminManager;
import com.xt.hsk.module.edu.service.exam.UserExamRecordService;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 用户模考记录导出任务导出
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Slf4j
@Component
public class UserExamRecordExportTaskExport extends BaseEasyExcelExport<UserExamRecordPageRespVO> {

    @Resource
    private UserExamRecordService userExamRecordService;

    @Resource
    private UserExamRecordAdminManager userExamRecordAdminManager;

    @Override
    protected List<List<String>> getExcelHead() {
        // 直接在这里定义表头，方便开发人员查看和维护
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("学员昵称"));
        head.add(Collections.singletonList("区号"));
        head.add(Collections.singletonList("手机号"));
        head.add(Collections.singletonList("总得分"));
        head.add(Collections.singletonList("听力得分"));
        head.add(Collections.singletonList("阅读得分"));
        head.add(Collections.singletonList("书写得分"));
        head.add(Collections.singletonList("答卷时长"));
        head.add(Collections.singletonList("交卷时间"));
        head.add(Collections.singletonList("记录状态"));
        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        // 使用配置好的 ObjectMapper 将 Map 转换为 UserExamRecordPageReqVO 对象，忽略taskName字段
        UserExamRecordPageReqVO dto = objectMapper.convertValue(conditions, UserExamRecordPageReqVO.class);

        return userExamRecordService.countUserExamRecordPage(dto);
    }

    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition, Long pageNo, Long pageSize) {

        UserExamRecordPageReqVO pageReqVO = objectMapper.convertValue(queryCondition, UserExamRecordPageReqVO.class);
        pageReqVO.setPageNo(Math.toIntExact(pageNo));
        pageReqVO.setPageSize(Math.toIntExact(pageSize));

        PageResult<UserExamRecordPageRespVO> recordPage = userExamRecordAdminManager.getUserExamRecordPage(pageReqVO);
        List<UserExamRecordPageRespVO> voList = recordPage.getList();

        if (CollUtil.isNotEmpty(voList)) {
            // 手动触发翻译
            voList = TranslateUtils.translate(voList);

            for (UserExamRecordPageRespVO vo : voList) {
                List<String> row = new ArrayList<>();
                row.add(CharSequenceUtil.isNotBlank(vo.getNickname()) ? vo.getNickname() : "-");
                row.add(CharSequenceUtil.isNotBlank(vo.getCountryCode()) ? vo.getCountryCode() : "-");
                row.add(CharSequenceUtil.isNotBlank(vo.getMobile()) ? vo.getMobile() : "-");
                row.add(vo.getActualScore() != null ? vo.getActualScore().toString() : "-");
                row.add(vo.getListeningScore() != null ? vo.getListeningScore().toString() : "-");
                row.add(vo.getReadingScore() != null ? vo.getReadingScore().toString() : "-");
                row.add(vo.getWritingScore() != null ? vo.getWritingScore().toString() : "-");
                row.add(CharSequenceUtil.isNotBlank(vo.getAnswerTimeStr()) ? vo.getAnswerTimeStr() : "-");
                row.add(vo.getEndTime() != null ? DateUtil.format(vo.getEndTime(), "yyyy-MM-dd HH:mm:ss") : "-");
                row.add(CharSequenceUtil.isNotBlank(vo.getCorrectionStatusDesc()) ? vo.getCorrectionStatusDesc() : "-");

                resultList.add(row);
            }
        }
        log.info("用户模考记录导出当前页：{}每页条数：{}总条数：{}", pageNo, pageSize, recordPage.getTotal());
    }

    @Override
    protected Long eachSheetTotalCount() {
        return 5000L;
    }

    @Override
    protected Long eachTimesWriteSheetTotalCount() {
        return 1000L;
    }
} 