package com.xt.hsk.module.edu.service.question.questiondetail;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetail.QuestionDetailDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 题目详情 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionDetailService extends IService<QuestionDetailDO> {
    PageResult<QuestionDetailDO> selectPage(@Valid QuestionDetailPageReqVO pageReqVO);

    List<QuestionDetailDO> selectListByQuestionId(Long questionId);

}