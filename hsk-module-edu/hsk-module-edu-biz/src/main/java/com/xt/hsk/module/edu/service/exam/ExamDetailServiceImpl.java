package com.xt.hsk.module.edu.service.exam;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamDetailPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRefCountDto;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDetailDO;
import com.xt.hsk.module.edu.dal.mysql.exam.ExamDetailMapper;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.List;
import org.springframework.stereotype.Service;


/**
 * 模考详情 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class ExamDetailServiceImpl extends ServiceImpl<ExamDetailMapper, ExamDetailDO> implements ExamDetailService {

    @Resource
    private ExamDetailMapper examDetailMapper;

    @Override
    public PageResult<ExamDetailDO> selectPage(ExamDetailPageReqVO pageReqVO) {

        return examDetailMapper.selectPage(pageReqVO);
    }

    @Override
    public long countQuestionQuote(List<Long> questionIds) {

        return examDetailMapper.countQuestionQuote(questionIds);
    }

    @Override
    public List<QuestionRefCountDto> getExamQuestionQuoteCount(Collection<Long> questionIds) {

        return examDetailMapper.getExamQuestionQuoteCount(questionIds);
    }

}