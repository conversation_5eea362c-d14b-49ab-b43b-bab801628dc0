package com.xt.hsk.module.edu.controller.app.exam.vo;

import com.xt.hsk.framework.common.enums.SubjectEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 模考科目元信息 req vo
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
public class ExamSubjectMetadataReqVO {

    /**
     * 模考ID
     */
    @NotNull(message = "模考ID不能为空")
    private Long examId;

    /**
     * 科目
     *
     * @see SubjectEnum
     */
    @NotNull(message = "科目不能为空")
    private Integer subject;
} 