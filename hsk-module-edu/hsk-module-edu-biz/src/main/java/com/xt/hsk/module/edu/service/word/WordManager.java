package com.xt.hsk.module.edu.service.word;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.WORD_EXAMPLE_TOO_MANY_ERROR;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.WORD_HSK_LEVEL_CONFLICT_ERROR;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.WORD_INTERPRETATION_TOO_MANY_ERROR;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.WORD_LONG_ERROR;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.WORD_ONLY_HANZI_ERROR;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.WORD_PINYIN_ERROR;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.WORD_REQUIRED_ERROR;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.framework.common.util.i18n.LanguageUtils.LANGUAGE_EN;
import static com.xt.hsk.framework.common.util.i18n.LanguageUtils.LANGUAGE_VI;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.LanguangeEnum;
import com.xt.hsk.framework.common.enums.WordKindEnum;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.BinaryUtils;
import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.tag.vo.TagRespVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordExampleRespVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordExampleSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordMeaningRespVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordMeaningSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordPageReqVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordRespVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordSaveReqVO;
import com.xt.hsk.module.edu.controller.app.word.vo.AppWordCardVo;
import com.xt.hsk.module.edu.controller.app.word.vo.AppWordExampleVo;
import com.xt.hsk.module.edu.controller.app.word.vo.AppWordMeaningVo;
import com.xt.hsk.module.edu.controller.app.word.vo.AppWordSearchVo;
import com.xt.hsk.module.edu.controller.app.word.vo.AppWordVo;
import com.xt.hsk.module.edu.controller.app.word.vo.WordSearchResultVo;
import com.xt.hsk.module.edu.dal.dataobject.tag.TagDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordExampleDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordMeaningDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordTagDO;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseService;
import com.xt.hsk.module.edu.service.tag.TagService;
import com.xt.hsk.module.game.api.SpecialExerciseApi;
import com.xt.hsk.module.game.api.dto.SpecialExerciseOtQuoteDTO;
import com.xt.hsk.module.game.api.dto.SpecialExerciseOtQuoteReqDTO;
import com.xt.hsk.module.game.api.dto.WordQuoteCountDTO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;


/**
 * 汉语词典基础数据 Manager
 *
 * <AUTHOR>
 */
@Service
public class WordManager {

    static final List<Character> TONE_VOWELS_ARRAY = Collections.unmodifiableList(Arrays.asList(
            'a', 'ā', 'á', 'ǎ', 'à',
            'e', 'ē', 'é', 'ě', 'è',
            'i', 'ī', 'í', 'ǐ', 'ì',
            'o', 'ō', 'ó', 'ǒ', 'ò',
            'u', 'ū', 'ú', 'ǔ', 'ù',
            'v', 'ǖ', 'ǘ', 'ǚ', 'ǜ'
    ));
    private final String SPLIT_CHAR = ";";
    @Resource
    private WordService wordService;
    @Resource
    private TagService tagService;
    @Resource
    private WordMeaningService wordMeaningService;
    @Resource
    private WordExampleService wordExampleService;
    @Resource
    private WordTagService wordTagService;
    @Resource
    private SpecialExerciseApi specialExerciseApi;
    @Resource
    private InteractiveCourseService interactiveCourseService;

    public static List<Integer> getBinaryBitValues(int number) {
        List<Integer> result = new ArrayList<>();

        // 处理负数的情况
        if (number < 0) {
            return Collections.emptyList();
        }
        if (number == 0) {
            result.add(0);
            return result;
        }

        int mask = 1;
        while (mask <= number && mask > 0) {  // mask > 0 防止整数溢出
            if ((number & mask) != 0) {
                result.add(mask);
            }
            mask <<= 1;  // 左移一位，检查下一个二进制位
        }

//         为了从最高位到最低位排序（可选）
        Collections.reverse(result);

        return result;
    }

    public Long createWord(WordSaveReqVO createReqVO) {
        // 校验入参是否合理
        validateSaveWord(createReqVO);
        // 查重
        String wordStr = createReqVO.getWord();
        LambdaQueryWrapper<WordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WordDO::getWord, wordStr);
        queryWrapper.eq(WordDO::getPinyin, createReqVO.getPinyin());
        long count = wordService.count(queryWrapper);
        if (count > 0) {
            throw new ServiceException(500, "<[" + wordStr + "] [" + createReqVO.getPinyin() + "]>已存在，无法保存");
        }
        // 插入
        WordDO word = BeanUtils.toBean(createReqVO, WordDO.class);

        List<Integer> hskLevels = createReqVO.getHskLevels();
        if (CollUtil.isNotEmpty(hskLevels)) {
            // 求和hskLevels
            int hskLevelSum = hskLevels.stream().mapToInt(Integer::intValue).sum();
            word.setHskLevel(hskLevelSum);
        }
        wordService.save(word);

        List<Long> tags = createReqVO.getTags();
        List<WordTagDO> wordTagDOS = new ArrayList<>();
        for (Long tag : tags) {
            WordTagDO wordTag = new WordTagDO();
            wordTag.setWordId(word.getId());
            wordTag.setTagId(tag);
            wordTag.setWord(word.getWord());
            wordTag.setPinyin(word.getPinyin());
            wordTagDOS.add(wordTag);
        }
        wordTagService.saveBatch(wordTagDOS);
        int isSpecial = 0;
        List<WordMeaningSaveReqVO> meanings = createReqVO.getMeanings();
        for (WordMeaningSaveReqVO meaning : meanings) {
            if (meaning.getIsSpecial() != null && meaning.getIsSpecial() == 1) {
                isSpecial = 1;
            }
            List<String> kinds = meaning.getKinds();
            List<Integer> kindInts = new ArrayList<>();
            for (String kind : kinds) {
                kindInts.add(WordKindEnum.getValueByCode(kind));
            }
            meaning.setKind(BinaryUtils.getArrayNum(kindInts));
            meaning.setWordId(word.getId());
            meaning.setWord(word.getWord());
            meaning.setPinyin(word.getPinyin());
        }
        word.setIsSpecial(isSpecial);
        wordService.updateById(word);
        List<WordMeaningDO> meaningDOS = BeanUtils.toBean(meanings, WordMeaningDO.class);

        wordMeaningService.saveBatch(meaningDOS);

        for (WordMeaningDO meaning : meaningDOS) {
            List<WordExampleSaveReqVO> examples = meaning.getExamples();
            for (WordExampleSaveReqVO example : examples) {
                example.setMeaningId(meaning.getId());
                example.setWord(word.getWord());
                example.setWordId(word.getId());
            }
            List<WordExampleDO> exampleDOS = BeanUtils.toBean(examples, WordExampleDO.class);
            wordExampleService.saveBatch(exampleDOS);
        }

        // 设置日志上下文变量
        LogRecordContext.putVariable("wordId", word.getId());
        LogRecordContext.putVariable("word", word);

        // 返回
        return word.getId();
    }

    private void validateSaveWord(WordSaveReqVO createReqVO) {
        if (createReqVO == null || createReqVO.getWord() == null) {
            throw new ServiceException(BAD_REQUEST);
        }
        String word = createReqVO.getWord();

        // 最多录入10个中文字符
        if (word.length() > 10) {
            throw new ServiceException(WORD_LONG_ERROR);
        }
        // 校验只能输入中文
        char[] charArray = word.toCharArray();
        for (char c : charArray) {
            if (!(c >= '\u4e00' && c <= '\u9fa5')) {//一  龥
                throw new ServiceException(WORD_ONLY_HANZI_ERROR);
            }
        }
        String pinyin = createReqVO.getPinyin();
        //只可输入小写英文字母和拼音、标点和其他语言，在点击保存时做核验
        if (pinyin == null) {
            throw new ServiceException(BAD_REQUEST);
        }
        char[] pinyinCharArray = pinyin.toCharArray();
        for (char c : pinyinCharArray) {
            if (!((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c == ' ') || (TONE_VOWELS_ARRAY.contains(c)))) {
                throw new ServiceException(WORD_PINYIN_ERROR);
            }
        }
        // HSK等级”：可多选，但是不可同时选择0级和其他等级；

        List<Integer> hskLevels = createReqVO.getHskLevels();
        if (hskLevels != null && !hskLevels.isEmpty()) {
            if (hskLevels.contains(0) && hskLevels.size() > 1) {
                throw new ServiceException(WORD_HSK_LEVEL_CONFLICT_ERROR);
            }
        } else {
            throw new ServiceException(WORD_REQUIRED_ERROR);
        }
        // 组词、同近义词、反义词  不能超过十个
        String compound = createReqVO.getCompound();
        // 将非英文的分号转化为英文分号
        if (compound != null) {
            compound = compound.replaceAll("；", SPLIT_CHAR);
            createReqVO.setCompound(compound);
//            if (compound.contains(SPLIT_CHAR)) {
//                if (compound.split(SPLIT_CHAR).length > 10) {
//                    throw new ServiceException(WORD_COMPOUND_TOO_MANY_ERROR);
//                }
//            }
        }

        String antonyms = createReqVO.getAntonyms();
        if (antonyms != null) {
            antonyms = antonyms.replaceAll("；", SPLIT_CHAR);
            createReqVO.setAntonyms(antonyms);
//            if (antonyms.contains(SPLIT_CHAR)) {
//                if (antonyms.split(SPLIT_CHAR).length > 5) {
//                    throw new ServiceException(WORD_COMPOUND_TOO_MANY_ERROR);
//                }
//            }
        }
        String synonyms = createReqVO.getSynonyms();
        if (synonyms != null) {
            synonyms = synonyms.replaceAll("；", SPLIT_CHAR);
            createReqVO.setSynonyms(synonyms);
//            if (synonyms.contains(SPLIT_CHAR)) {
//                if (synonyms.split(SPLIT_CHAR).length > 5) {
//                    throw new ServiceException(WORD_COMPOUND_TOO_MANY_ERROR);
//                }
//            }
        }
        validateSaveWordMeanings(createReqVO.getMeanings());
    }

    private void validateSaveWordMeanings(List<WordMeaningSaveReqVO> meanings) {
        if (meanings == null || meanings.isEmpty()) {
            return;
        }
        for (WordMeaningSaveReqVO meaning : meanings) {
//            String translationOt = meaning.getTranslationOt();
//            if (translationOt == null) {
//                throw new ServiceException(WORD_REQUIRED_ERROR);
//            }
//            translationOt = translationOt.replaceAll("；", SPLIT_CHAR);
//            meaning.setTranslationOt(translationOt);
//            if (translationOt.contains(SPLIT_CHAR)) {
//                if (translationOt.split(SPLIT_CHAR).length > 10) {
//                    throw new ServiceException(WORD_COMPOUND_TOO_MANY_ERROR);
//                }
//            }
//            String translationEn = meaning.getTranslationEn();
//            if (translationEn != null) {
//                translationEn = translationEn.replaceAll("；", SPLIT_CHAR);
//                meaning.setTranslationEn(translationEn);
//                if (translationEn.contains(SPLIT_CHAR)) {
//                    if (translationEn.split(SPLIT_CHAR).length > 10) {
//                        throw new ServiceException(WORD_COMPOUND_TOO_MANY_ERROR);
//                    }
//                }
//            }
            String interpretation = meaning.getInterpretation();
            if (interpretation != null && interpretation.length() > 200) {
                throw new ServiceException(WORD_INTERPRETATION_TOO_MANY_ERROR);
            }
            List<WordExampleSaveReqVO> examples = meaning.getExamples();
            if (examples != null && examples.size() > 5) {
                throw new ServiceException(WORD_EXAMPLE_TOO_MANY_ERROR);
            }
        }
    }

    public void updateWord(WordSaveReqVO updateReqVO) {
        // 1. 校验是否存在该单词
        validateSaveWord(updateReqVO);
        Long wordId = updateReqVO.getId();
        validateWordExists(wordId);
        // 查重
        String wordStr = updateReqVO.getWord();
        LambdaQueryWrapper<WordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WordDO::getWord, wordStr);
        queryWrapper.eq(WordDO::getPinyin, updateReqVO.getPinyin());
        queryWrapper.ne(WordDO::getId, wordId);
        long count = wordService.count(queryWrapper);
        if (count > 0) {
            throw new ServiceException(500, "<[" + wordStr + "] [" + updateReqVO.getPinyin() + "]>已存在，无法保存");
        }
        // 2. 更新主表信息
        WordDO word = BeanUtils.toBean(updateReqVO, WordDO.class);
//        wordService.updateById(word);
        // 3. 更新 HSK 等级信息
        List<Integer> hskLevels = updateReqVO.getHskLevels();
        if (hskLevels == null || hskLevels.isEmpty()) {
            throw new ServiceException(WORD_REQUIRED_ERROR);
        }
        if (hskLevels.contains(0) && hskLevels.size() > 1) {
            throw new ServiceException(WORD_HSK_LEVEL_CONFLICT_ERROR);
        }

        if (CollUtil.isNotEmpty(hskLevels)) {
            // 求和hskLevels
            int hskLevelSum = hskLevels.stream().mapToInt(Integer::intValue).sum();
            word.setHskLevel(hskLevelSum);
        }
        // 4. 更新标签信息
        List<Long> tagIds = updateReqVO.getTags();
        List<WordTagDO> wordTagDOS = tagIds.stream()
                .map(tagId -> {
                    WordTagDO tag = new WordTagDO();
                    tag.setWordId(wordId);
                    tag.setTagId(tagId);
                    tag.setWord(word.getWord());
                    tag.setPinyin(word.getPinyin());
                    return tag;
                })
                .collect(Collectors.toList());

        wordTagService.removeByWordId(wordId); // 清除旧标签
        wordTagService.saveBatch(wordTagDOS);

        // 5. 更新词义和例句
        List<WordMeaningSaveReqVO> meaningVOS = updateReqVO.getMeanings();
        if (meaningVOS == null || meaningVOS.isEmpty()) {
            throw new ServiceException(WORD_REQUIRED_ERROR);
        }

        int isSpecial = 0;
        for (WordMeaningSaveReqVO meaningVO : meaningVOS) {
            if (meaningVO.getIsSpecial() != null && meaningVO.getIsSpecial() == 1) {
                isSpecial = 1;
            }
            List<String> kinds = meaningVO.getKinds();
            List<Integer> kindInts = new ArrayList<>();
            for (String kind : kinds) {
                kindInts.add(WordKindEnum.getValueByCode(kind));
            }
            meaningVO.setKind(BinaryUtils.getArrayNum(kindInts));
            meaningVO.setWordId(wordId);
            meaningVO.setWord(word.getWord());
        }

        word.setIsSpecial(isSpecial);
        wordService.updateById(word);

        List<WordMeaningDO> meaningDOS = BeanUtils.toBean(meaningVOS, WordMeaningDO.class);

        // 删除原有词义及其例句
        wordExampleService.removeByWordId(wordId);
        wordMeaningService.removeByWordId(wordId);

        // 批量保存新词义
        wordMeaningService.saveBatch(meaningDOS);

        // 保存每个词义下的例句
        List<WordExampleDO> exampleDOS = new ArrayList<>();
        for (WordMeaningDO meaning : meaningDOS) {
            List<WordExampleSaveReqVO> examples = meaning.getExamples();
            if (examples == null || examples.isEmpty()) continue;

            for (WordExampleSaveReqVO example : examples) {
                example.setMeaningId(meaning.getId());
                example.setWord(word.getWord());
                example.setWordId(wordId);
                exampleDOS.add(BeanUtils.toBean(example, WordExampleDO.class));
            }
        }

        if (!exampleDOS.isEmpty()) {
            wordExampleService.saveBatch(exampleDOS);
        }

        // 设置日志上下文变量
        LogRecordContext.putVariable("word", word);

    }

    public void deleteWord(Long id) {
        // 校验存在
        validateWordExists(id);
        // 获取词典信息
        WordDO word = wordService.getById(id);

        // 设置日志上下文变量
        LogRecordContext.putVariable("word", word);

        // 删除
        wordService.removeById(id);
    }

    private void validateWordExists(Long id) {
        if (wordService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }

    public WordRespVO getWord(Long id) {
        WordDO wordDO = wordService.getById(id);
        return getRespVO(id, wordDO);
    }

    private WordRespVO getRespVO(Long id, WordDO wordDO) {
        WordRespVO vo = BeanUtils.toBean(wordDO, WordRespVO.class);
        // 获取标签
        List<WordTagDO> wordTagDOS = wordTagService.getByWordId(id);
        List<TagDO> tagDOS = new ArrayList<>();
        if (wordTagDOS != null && !wordTagDOS.isEmpty()) {
            List<Long> tagIdList = wordTagDOS.stream().map(WordTagDO::getTagId).toList();
            vo.setTags(tagIdList);
            LambdaQueryWrapper<TagDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(TagDO::getId, tagIdList);
            queryWrapper.eq(TagDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
            tagDOS = tagService.list(queryWrapper);
        }
        List<TagRespVO> bean = BeanUtils.toBean(tagDOS, TagRespVO.class);
        vo.setTagRespVOS(bean);
        String tagNames = tagDOS.stream().map(TagDO::getTagName).collect(Collectors.joining(SPLIT_CHAR));
        vo.setTagsDesc(tagNames);

        // 获取等级
        List<Integer> hskLevels = getBinaryBitValues(vo.getHskLevel());
        vo.setHskLevels(hskLevels);
        StringBuilder hskLevelsDesc = new StringBuilder();
        List<HskEnum> hskEnums = new ArrayList<>();
        for (Integer hskLevel : hskLevels) {
            hskLevelsDesc.append(HskEnum.getDescByCode(hskLevel)).append(SPLIT_CHAR);
            hskEnums.add(HskEnum.getByCode(hskLevel));
        }
        if (!hskLevelsDesc.isEmpty()) {
            vo.setHskLevelsDesc(hskLevelsDesc.deleteCharAt(hskLevelsDesc.length() - 1).toString());
            vo.setHskVos(hskEnums);
        }
        // 获取含义,获取例句
        List<WordMeaningDO> meanings = wordMeaningService.getByWordId(id);
        if (meanings != null && !meanings.isEmpty()) {
            List<WordMeaningRespVO> meaningRespVOS = BeanUtils.toBean(meanings, WordMeaningRespVO.class);
            List<Long> meaningIds = meaningRespVOS.stream().map(WordMeaningRespVO::getId).toList();
            List<WordExampleDO> examples = wordExampleService.getByMeaningIds(meaningIds);
            Map<Long, List<WordExampleDO>> exampleMap = examples.stream().collect(Collectors.groupingBy(WordExampleDO::getMeaningId));

            for (WordMeaningRespVO meaningRespVO : meaningRespVOS) {
                if (meaningRespVO.getKind() != null) {
                    List<Integer> kindInts = BinaryUtils.getBinaryBitValues(meaningRespVO.getKind());
                    Set<String> kindDescs = new HashSet<>();
                    List<String> kindEns = new ArrayList<>();
                    for (Integer kind : kindInts) {
                        kindDescs.add(WordKindEnum.getDescByValue(kind));
                        kindEns.add(WordKindEnum.getCodeByValue(kind));
                    }
                    meaningRespVO.setKinds(kindEns);
                    meaningRespVO.setKindDesc(String.join(",", kindDescs));
                }
                List<WordExampleDO> exampleDOS = exampleMap.get(meaningRespVO.getId());
                meaningRespVO.setExamples(BeanUtils.toBean(exampleDOS, WordExampleRespVO.class));
            }
            vo.setMeanings(meaningRespVOS);
        }
        return vo;
    }

    public PageResult<WordRespVO> getWordPage(@Valid WordPageReqVO pageReqVO) {
        PageResult<WordDO> pageResult = wordService.selectPage(pageReqVO);
        PageResult<WordRespVO> result = BeanUtils.toBean(pageResult, WordRespVO.class);
        List<WordRespVO> list = result.getList();
        List<Long> wordIds = list.stream().map(WordRespVO::getId).toList();

        // 词性
        List<WordMeaningDO> wordsMeaningsByWordIds = wordService.getWordsMeaningsByWordIds(wordIds);
        Map<Long, List<WordMeaningDO>> wordMeaningMap = wordsMeaningsByWordIds.stream().collect(Collectors.groupingBy(WordMeaningDO::getWordId));

        // 标签
        List<WordTagDO> wordTagDOS = wordService.getWordsTagsByWordIds(wordIds);
        Map<Long, List<WordTagDO>> wordTagMap = new HashMap<>();
        Map<Long, TagDO> tagMap;
        if (wordTagDOS != null && !wordTagDOS.isEmpty()) {
            wordTagMap = wordTagDOS.stream().collect(Collectors.groupingBy(WordTagDO::getWordId));
            List<Long> tagIds = wordTagDOS.stream().map(WordTagDO::getTagId).distinct().toList();
            List<TagDO> tags = new ArrayList<>();
            if (!tagIds.isEmpty()) {
                LambdaQueryWrapper<TagDO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(TagDO::getId, tagIds);
                queryWrapper.eq(TagDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
                tags = tagService.list(queryWrapper);
            }
            tagMap = tags.stream().collect(Collectors.toMap(TagDO::getId, tagDO -> tagDO));

        } else {
            tagMap = new HashMap<>();
        }
        // 字词库在专项练习中的引用次数
        List<WordQuoteCountDTO> quoteCountDTOS = specialExerciseApi.countQuoteByWordIds(wordIds);
        Map<Long, Integer> quoteCountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(quoteCountDTOS)) {
            quoteCountMap = quoteCountDTOS.stream().collect(Collectors.toMap(WordQuoteCountDTO::getWordId, WordQuoteCountDTO::getQuoteCount));
        }

        for (WordRespVO item : list) {
            // HSK等级
            List<Integer> hskLevels = getBinaryBitValues(item.getHskLevel());
            item.setHskLevels(hskLevels);
            List<HskEnum> hskEnums = new ArrayList<>();
//            StringBuilder hskLevelsDesc = new StringBuilder();
            List<String> hskLevelsDescs = new ArrayList();
            for (Integer hskLevel : hskLevels) {
//                hskLevelsDesc.append(HskEnum.getDescByCode(hskLevel)).append(SPLIT_CHAR);
                hskLevelsDescs.add(HskEnum.getDescByCode(hskLevel));
                hskEnums.add(HskEnum.getByCode(hskLevel));
            }
            item.setHskLevelsDesc(String.join(SPLIT_CHAR, hskLevelsDescs));
            item.setHskVos(hskEnums);
            // 词性
            if (wordMeaningMap.containsKey(item.getId())) {
                List<WordMeaningDO> meaningDOS = wordMeaningMap.getOrDefault(item.getId(), Collections.emptyList());

                Set<String> ots = new HashSet<>();
                Set<String> collect = meaningDOS.stream().map(WordMeaningDO::getTranslationOt).collect(Collectors.toSet());
                for (String ot : collect) {
                    String[] split = ot.split(SPLIT_CHAR);
                    ots.addAll(Arrays.asList(split));
                }
                item.setTranslationOts(ots);
                List<Integer> kindSets = meaningDOS.stream().map(WordMeaningDO::getKind).filter(Objects::nonNull).filter(kind -> kind > 0).collect(Collectors.toList());
                Set<Integer> kinds = new HashSet<>();// 拆分后的结果
                for (Integer kind : kindSets) {
                    kinds.addAll(BinaryUtils.getBinaryBitValues(kind));
                }
                String kindDesc = joinDescriptions(kinds, WordKindEnum::getDescByValue);
                if (!kinds.isEmpty() && kindSets.size() != meaningDOS.size()) {
                    kindDesc = kindDesc.concat(",-");
                }
                item.setKindsDesc(kindDesc);
            }

            // 标签
            if (wordTagMap.containsKey(item.getId())) {
                Set<Long> tagIdSet = wordTagMap.getOrDefault(item.getId(), Collections.emptyList())
                        .stream()
                        .map(WordTagDO::getTagId)
                        .collect(Collectors.toSet());
                item.setTagsDesc(joinDescriptions(tagIdSet, id -> tagMap.get(id) != null ? tagMap.get(id).getTagName() : null));
            }
            // 引用量
            if (quoteCountMap.containsKey(item.getId())) {
                item.setQuoteCount(quoteCountMap.get(item.getId()));
            }
            // todo 收藏量
        }
        ;
        return result;
    }

    private <T> String joinDescriptions(Collection<T> values, Function<T, String> descriptionMapper) {
        if (values == null || values.isEmpty()) {
            return "";
        }
        return values.stream()
                .map(descriptionMapper)
                .filter(Objects::nonNull)
                .collect(Collectors.joining("，"));
    }

    public PageResult<WordSearchResultVo> searchPage(AppWordSearchVo appWordSearchVo) {
        PageResult<WordMeaningDO> pageResult = wordMeaningService.searchPage(appWordSearchVo);
        PageResult<WordSearchResultVo> result = new PageResult<>();
        result.setTotal(pageResult.getTotal());
        pageResult.getList().forEach(item -> {
            WordSearchResultVo vo = new WordSearchResultVo();
            vo.setId(item.getId());
            vo.setWord(item.getWord());
            vo.setPinyin(item.getPinyin());
            if (LanguangeEnum.ENGLISH.code.equals(appWordSearchVo.getTarget())
                    || LanguangeEnum.ENGLISH.code.equals(appWordSearchVo.getOriginal())) {
                vo.setTranslation(item.getTranslationEn());
            } else {
                vo.setTranslation(item.getTranslationOt());
            }
        });
        return result;
    }

    public List<AppWordVo> getWordByName(String name) {
        List<WordDO> wordByName = wordService.getWordByName(name);
        if (wordByName == null || wordByName.isEmpty()) {
            return Collections.emptyList();
        }
        String language = LanguageUtils.getLanguage();
        List<AppWordVo> result = new ArrayList<>();
        for (WordDO item : wordByName) {
            AppWordVo respVO = getAppWordVo(item, language);
            result.add(respVO);
        }
        return result;
    }

    /***
     *  1专项引用 2互动课引用
     * @param wordId
     * @return
     */
    public List<Integer> getQuoteInfo(Long wordId) {
        List<Integer> quoteInfo = new ArrayList<>();
        // 查看专项中是否引用
        boolean exerciseApiQuote = specialExerciseApi.isQuoteWord(wordId);
        if (exerciseApiQuote) {
            quoteInfo.add(1);
        }
        // 互动课中是否引用
        boolean interactiveCourseApiQuote = interactiveCourseService.isQuoteWord(wordId);
        if (interactiveCourseApiQuote) {
            quoteInfo.add(2);
        }
        return quoteInfo;
    }

    public AppWordVo getAppWord(Long id, String language) {
        WordDO wordDO = wordService.getById(id);
        return getAppWordVo(wordDO, language);
    }

    private AppWordVo getAppWordVo(WordDO wordDO, String language) {
        if (wordDO == null) {
            return null;
        }

        AppWordVo bean = BeanUtils.toBean(wordDO, AppWordVo.class);

        // HSK级别处理
        Integer hskLevel = wordDO.getHskLevel();
        List<Integer> list = Optional.ofNullable(hskLevel)
                .map(BinaryUtils::getBinaryBitValues)
                .orElse(Collections.emptyList());
        List<String> hskLevelsDesc = list.stream()
                .map(HskEnum::getDescByCode)
                .collect(Collectors.toList());
        bean.setHskLevels(list);
        bean.setHskLevelsDesc(String.join(",", hskLevelsDesc));

        // 词性处理
        List<WordMeaningDO> meaningDOS = wordMeaningService.getByWordId(wordDO.getId());
        Set<Integer> kinds = new HashSet<>();
        List<Long> meaningIds = meaningDOS.stream()
                .map(WordMeaningDO::getId)
                .collect(Collectors.toList());
        List<WordExampleDO> examples = wordExampleService.getByMeaningIds(meaningIds);
        Map<Long, List<WordExampleDO>> exampleMap = examples.stream()
                .collect(Collectors.groupingBy(WordExampleDO::getMeaningId));

        Map<Integer, List<AppWordMeaningVo>> kindDetails = new HashMap<>();
        for (WordMeaningDO meaningDO : meaningDOS) {
            processMeaning(meaningDO, language, kinds, exampleMap, kindDetails);
        }
        List<WordKindEnum> kindEnums = kinds.stream()
                .map(WordKindEnum::getByValue)
                .collect(Collectors.toList());
        bean.setKinds(kindEnums);
        bean.setKindDetails(kindDetails);

        // 标签处理
        List<WordTagDO> tagDOS = wordTagService.getByWordId(wordDO.getId());
        Set<Long> tagIds = tagDOS.stream()
                .map(WordTagDO::getTagId)
                .collect(Collectors.toSet());
        LambdaQueryWrapper<TagDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TagDO::getId, tagIds);
        queryWrapper.eq(TagDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        List<TagDO> tagDOS1 = tagService.list(queryWrapper);
        bean.setTags(tagDOS1);

        // 反义词处理
        String antonyms = wordDO.getAntonyms();
        List<String> wordNames = new ArrayList<>();
        List<String> antonymsList = new ArrayList<>();
        List<String> synonymsList = new ArrayList<>();
        List<String> compoundList = new ArrayList<>();
        if (antonyms != null && antonyms.contains(SPLIT_CHAR)) {
            antonymsList = Arrays.asList(antonyms.split(SPLIT_CHAR));
            wordNames.addAll(antonymsList);
        } else {
            wordNames.add(antonyms);
        }
        // 近义词
        String synonyms = wordDO.getSynonyms();
        if (synonyms != null && synonyms.contains(SPLIT_CHAR)) {
            synonymsList = Arrays.asList(synonyms.split(SPLIT_CHAR));
            wordNames.addAll(synonymsList);
        } else {
            wordNames.add(synonyms);
        }
        // 复合词
        String compound = wordDO.getCompound();
        if (compound != null && compound.contains(SPLIT_CHAR)) {
            compoundList = Arrays.asList(compound.split(SPLIT_CHAR));
            wordNames.addAll(compoundList);
        } else {
            wordNames.add(compound);
        }
        LambdaQueryWrapper<WordDO> wordQueryWrapper = new LambdaQueryWrapper<>();
        wordQueryWrapper.in(WordDO::getWord, wordNames);
        List<WordDO> wordDOList = wordService.list(wordQueryWrapper);
        Map<String, List<WordDO>> wordMap = wordDOList.stream().collect(Collectors.groupingBy(WordDO::getWord));
        for (String wordName : antonymsList) {
            List<WordDO> wordDOList1 = wordMap.get(wordName);
            List<AppWordCardVo> wordCardVos = BeanUtils.toBean(wordDOList1, AppWordCardVo.class);
            bean.getAntonyms().addAll(wordCardVos);
        }
        for (String wordName : synonymsList) {
            List<WordDO> wordDOList1 = wordMap.get(wordName);
            List<AppWordCardVo> wordCardVos = BeanUtils.toBean(wordDOList1, AppWordCardVo.class);
            bean.getSynonyms().addAll(wordCardVos);
        }
        for (String wordName : compoundList) {
            List<WordDO> wordDOList1 = wordMap.get(wordName);
            List<AppWordCardVo> wordCardVos = BeanUtils.toBean(wordDOList1, AppWordCardVo.class);
            bean.getCompounds().addAll(wordCardVos);
        }

        return bean;
    }

    private void processMeaning(WordMeaningDO meaningDO, String language,
                                Set<Integer> kinds, Map<Long, List<WordExampleDO>> exampleMap,
                                Map<Integer, List<AppWordMeaningVo>> kindDetails) {
        String translation = null;
        if (language.equals(LANGUAGE_EN) && meaningDO.getTranslationEn() != null && !meaningDO.getTranslationEn().isEmpty()) {
            translation = meaningDO.getTranslationEn();
        } else if (language.equals(LANGUAGE_VI) && meaningDO.getTranslationOt() != null && !meaningDO.getTranslationOt().isEmpty()) {
            translation = meaningDO.getTranslationOt();
        }
        if (translation == null) {
            return;
        }
        List<Integer> meaningKinds = BinaryUtils.getBinaryBitValues(meaningDO.getKind());
        kinds.addAll(meaningKinds);

        List<WordExampleDO> exampleDOS = Optional.ofNullable(exampleMap.get(meaningDO.getId()))
                .orElse(Collections.emptyList());
        List<AppWordExampleVo> exampleVos = BeanUtils.toBean(exampleDOS, AppWordExampleVo.class);

        for (Integer meaningKind : meaningKinds) {
            AppWordMeaningVo appWordMeaningVo = new AppWordMeaningVo();
            appWordMeaningVo.setTitle(translation);
            appWordMeaningVo.setInterpretation(meaningDO.getInterpretation());
            appWordMeaningVo.setIsSpecial(meaningDO.getIsSpecial());
            appWordMeaningVo.setExamples(exampleVos);

            kindDetails.computeIfAbsent(meaningKind, k -> new ArrayList<>())
                    .add(appWordMeaningVo);
        }
    }

    public SpecialExerciseOtQuoteDTO getSpecialExerciseQuoteInfo(SpecialExerciseOtQuoteReqDTO reqDTO) {
        List<WordMeaningDO> meaningDOS = wordMeaningService.getByWordId(reqDTO.getWordId());
        Set<String> dbVis = new HashSet<>();
        for (WordMeaningDO meaningDO : meaningDOS) {
            String translationOt = meaningDO.getTranslationOt();
            if (translationOt != null) {
                String[] split = translationOt.split(SPLIT_CHAR);
                dbVis.addAll(Arrays.asList(split));
            }
        }
        List<String> newVis = (List<String>) reqDTO.getVis();
        Set<String> newVisSet = new HashSet<>(newVis);
        for (String newVi : newVis) {
            String[] split = newVi.split(SPLIT_CHAR);
            newVisSet.addAll(Arrays.asList(split));
        }
        // 如果db有，现在没有就需要查询引用的专项练习组
        boolean isNeedQuery = false;
        if (!dbVis.isEmpty()) {
            for (String dbVi : dbVis) {
                if (!newVisSet.contains(dbVi)) {
                    isNeedQuery = true;
                    break;
                }
            }
        }
        if (isNeedQuery) {
            SpecialExerciseOtQuoteReqDTO req = new SpecialExerciseOtQuoteReqDTO();
            req.setWordId(reqDTO.getWordId());
            req.setVis(dbVis);
            SpecialExerciseOtQuoteDTO specialExerciseOtQuoteDTO = specialExerciseApi.getSpecialExerciseQuoteInfo(req);
            return specialExerciseOtQuoteDTO;
        }
        return new SpecialExerciseOtQuoteDTO();
    }
}