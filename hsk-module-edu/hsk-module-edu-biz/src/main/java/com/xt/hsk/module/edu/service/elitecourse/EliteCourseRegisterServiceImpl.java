package com.xt.hsk.module.edu.service.elitecourse;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.*;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.EliteCourseAppPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseRegisterDO;
import com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteCourseRegisterMapper;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseLearningStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum;
import com.xt.hsk.module.edu.manager.elitecourse.dto.CourseAndRegisterDto;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Date;
import java.util.List;


/**
 * 课程登记 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
@Validated
public class EliteCourseRegisterServiceImpl extends ServiceImpl<EliteCourseRegisterMapper, EliteCourseRegisterDO> implements EliteCourseRegisterService {

    @Resource
    private EliteCourseRegisterMapper eliteCourseRegisterMapper;

    @Resource
    private EliteCourseService eliteCourseService;

    @Override
    public PageResult<EliteCourseRegisterDO> selectPage(EliteCourseRegisterPageReqVO pageReqVO) {

        return eliteCourseRegisterMapper.selectPage(pageReqVO);
    }

    /**
     * 分页获取课程用户
     */
    @Override
    public PageResult<EliteCourseUserRespVO> getCourseUserPage(EliteCourseStudyPageReqVO pageReqVO) {
        if (checkStatusValid(pageReqVO)) {
            return new PageResult<>(0L);
        }

        IPage<EliteCourseUserRespVO> page = eliteCourseRegisterMapper.getCourseUserPage(new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize()), pageReqVO);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 课程用户总数
     */
    @Override
    public Long countCourseUser(EliteCourseStudyPageReqVO pageReqVO) {
        if (checkStatusValid(pageReqVO)) {
            return 0L;
        }
        return eliteCourseRegisterMapper.countCourseUser(pageReqVO);
    }

    /**
     * 分页获取课程注册用户
     */
    @Override
    public PageResult<EliteCourseRegisterUserRespVO> getCourseRegisterUserPage(EliteCourseRegisterUserPageReqVO pageReqVO) {
        IPage<EliteCourseRegisterUserRespVO> page = eliteCourseRegisterMapper.getCourseRegisterUserPage(new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize()), pageReqVO);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 课程注册用户总数
     */
    @Override
    public Long countCourseRegisterUser(EliteCourseRegisterUserPageReqVO pageReqVO) {
        return eliteCourseRegisterMapper.countCourseRegisterUser(pageReqVO);
    }

    @Override
    public Long getUserCourseRegisterCount(Long userId) {
        return eliteCourseRegisterMapper.getUserCourseRegisterCount(userId);
    }

    @Override
    public PageResult<CourseAndRegisterDto> getUserCourseRegister(EliteCourseAppPageReqVO pageVO) {
        IPage<CourseAndRegisterDto> page = eliteCourseRegisterMapper.getUserCourseRegister(new Page<>(pageVO.getPageNo(), pageVO.getPageSize()), pageVO);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 检查状态有效期
     */
    private boolean checkStatusValid(EliteCourseStudyPageReqVO pageReqVO) {
        EliteCourseDO course = eliteCourseService.getEliteCourse(pageReqVO.getCourseId());
        Integer validityPeriod = course.getLearningValidityPeriod();
        Integer learningStatus = pageReqVO.getLearningStatus();

        if (LearningValidityPeriodEnum.PERMANENT.getCode().equals(validityPeriod)) {
            return EliteCourseLearningStatusEnum.EXPIRED.getCode().equals(learningStatus);
        }

        if (LearningValidityPeriodEnum.BY_DEADLINE.getCode().equals(validityPeriod)) {
            Date deadline = DateUtil.date(course.getDeadline());
            boolean isAfterDeadline = DateUtil.date().after(deadline);

            // 当前日期在截止日之后 -> 无“未过期”用户；当前日期在截止日之前 -> 无“已过期”用户
            if ((isAfterDeadline && EliteCourseLearningStatusEnum.NORMAL.getCode().equals(learningStatus))
                    || (!isAfterDeadline && EliteCourseLearningStatusEnum.EXPIRED.getCode().equals(learningStatus))) {
                return true;
            }

            pageReqVO.setDeadline(course.getDeadline());
        } else if (LearningValidityPeriodEnum.BY_DAYS.getCode().equals(validityPeriod)) {
            pageReqVO.setEffectiveDays(course.getEffectiveDays());
        }

        pageReqVO.setLearningValidityPeriod(validityPeriod);
        return false;
    }
}