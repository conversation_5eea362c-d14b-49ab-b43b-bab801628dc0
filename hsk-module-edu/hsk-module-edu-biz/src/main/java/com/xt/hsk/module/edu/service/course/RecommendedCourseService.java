package com.xt.hsk.module.edu.service.course;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCourseRespVO;
import com.xt.hsk.module.edu.controller.app.elitecourse.vo.EliteCourseDetailRespVO.RecommendedCourseVO;
import com.xt.hsk.module.edu.dal.dataobject.course.RecommendedCourseDO;
import java.util.List;

/**
 * 课程推荐 Service 接口
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
public interface RecommendedCourseService extends IService<RecommendedCourseDO> {

    /**
     * 分页获取课程推荐列表
     *
     * @param pageReqVO 分页查询条件
     * @return 课程推荐分页列表
     */
    PageResult<RecommendedCourseDO> getRecommendedCoursePage(RecommendedCoursePageReqVO pageReqVO);

    /**
     * 查询特定HSK等级下最大的排序序号
     *
     * @param hskLevel HSK等级
     * @return 最大排序序号
     */
    Integer getMaxSortByHskLevel(Integer hskLevel);

    /**
     * 获取推荐课程 在看课程详情中可能会出现推荐自己的情况 需要过滤一下 通过  notCourseId
     *
     * @param hskLevel    HSK 级别
     * @param notCourseId 需要过滤的课程ID
     * @return 推荐课程
     */
    List<RecommendedCourseVO> selectRecommendedCoursesByHskLevel(Integer hskLevel,
        Long notCourseId);

    /**
     * 分页查询 使用联表查询 推荐课表连课程表
     */
    PageResult<RecommendedCourseRespVO> selectJoinPage(RecommendedCoursePageReqVO reqVO);
    
    /**
     * 在特定HSK等级下更新指定排序区间内的课程排序
     *
     * @param hskLevel HSK等级
     * @param startSort 开始排序值
     * @param endSort 结束排序值
     * @param increment 增量值（可为正数或负数）
     */
    void updateSortInRange(Integer hskLevel, Integer startSort, Integer endSort, Integer increment);
} 