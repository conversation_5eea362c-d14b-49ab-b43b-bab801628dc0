package com.xt.hsk.module.edu.service.question.questiondetail;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetail.QuestionDetailDO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 题目详情 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionDetailManager {

    @Resource
    private QuestionDetailService questionDetailService;


    public Long createQuestionDetail(QuestionDetailSaveReqVO createReqVO) {
        // 插入
        QuestionDetailDO questionDetail = BeanUtils.toBean(createReqVO, QuestionDetailDO.class);
        questionDetailService.save(questionDetail);

        // 返回
        return questionDetail.getId();
    }


    public void updateQuestionDetail(QuestionDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionDetailExists(updateReqVO.getId());
        // 更新
        QuestionDetailDO updateObj = BeanUtils.toBean(updateReqVO, QuestionDetailDO.class);
        questionDetailService.updateById(updateObj);
    }


    public void deleteQuestionDetail(Long id) {
        // 校验存在
        validateQuestionDetailExists(id);
        // 删除
        questionDetailService.removeById(id);
    }

    private void validateQuestionDetailExists(Long id) {
        if (questionDetailService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public PageResult<QuestionDetailDO> getQuestionDetailPage(@Valid QuestionDetailPageReqVO pageReqVO) {
        return questionDetailService.selectPage(pageReqVO);
    }

}