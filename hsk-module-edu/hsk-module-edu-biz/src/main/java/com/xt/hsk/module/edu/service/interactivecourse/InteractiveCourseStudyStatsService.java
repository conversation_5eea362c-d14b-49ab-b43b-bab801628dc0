package com.xt.hsk.module.edu.service.interactivecourse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseStudyStatsDO;

/**
 * 互动课程学习统计 Service 接口
 *
 * <AUTHOR>
 */
public interface InteractiveCourseStudyStatsService extends
    IService<InteractiveCourseStudyStatsDO> {

    /**
     * 获取课程的学习人数 这里设计只是查询单个课程的学习人数的原因是防止在修改某单元到一门新的课程。所以只使用一个课程ID进行查询。
     *
     * @param courseId 课程ID
     * @return 学习人数
     */
    Integer getCourseStudyCount(Long courseId);

    /**
     * 获取单元的学习人数
     *
     * @param unitId 课程单元id
     * @return 单元的学习人数
     */
    Long getUnitStudyCount(Long unitId);

    /**
     * 更新学习统计
     *
     * @param courseId 课程ID
     * @param unitId   单元ID，可为null
     * @param userId   用户ID
     * @param isFirstTimeStudy 是否首次学习该单元
     */
    void updateStudyStats(Long courseId, Long unitId, Long userId, boolean isFirstTimeStudy);

} 