package com.xt.hsk.module.edu.listener.question;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerdata.UserQuestionAnswerDataDO;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerrecord.UserQuestionAnswerRecordDO;
import com.xt.hsk.module.edu.enums.question.QuestionRecordStatusEnum;
import com.xt.hsk.module.edu.manager.exam.admin.ExamCorrectionManager;
import com.xt.hsk.module.edu.service.userquestionanswerrecord.UserQuestionAnswerRecordService;
import com.xt.hsk.module.edu.service.userquestionanswerrecord.userquestionanswerdata.UserQuestionAnswerDataService;
import com.xt.hsk.module.thirdparty.api.WriteAiCorrection.WritingAiCorrectionRecordApi;
import com.xt.hsk.module.thirdparty.dto.coze.WritingAiCorrectionRecordRespVO;
import com.xt.hsk.module.thirdparty.enums.AiCorrectBizTypeEnum;
import com.xt.hsk.module.thirdparty.event.CozeEvent;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 真题练习的题目内容更新监听器
 */
@Component("UserRegisterListener")
@AllArgsConstructor
@Slf4j
public class QusestionUpdateListener {

    @Resource
    private UserQuestionAnswerRecordService userQuestionAnswerRecordService;
    @Resource
    private UserQuestionAnswerDataService userQuestionAnswerDataService;
    @Resource
    private WritingAiCorrectionRecordApi writingAiCorrectionRecordApi;
    @Resource
    private ExamCorrectionManager examCorrectionManager;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = CozeEvent.class, fallbackExecution = true)
    public void handleAiCorrectionEvent(CozeEvent event) {
        log.info("题目内容更新监听器");
        // 获取题目练习记录
        Integer totalScore = 0;
        Integer userScore = 0;
        if (event.getSuccess()) {
            // 获取ai批改记录
            WritingAiCorrectionRecordRespVO correctionRecordRespVO = writingAiCorrectionRecordApi.getById(event.getWritingAiCorrectionRecordId());
//        // 获取ai批改数据
//        List<WritingAiCorrectionDetailRespVO> aiCorrectionRecords = writingAiCorrectionRecordApi.getByRecordId(correctionRecordRespVO.getId());
            // 跟新记录和数据
            // 更新总分和用户得分
            totalScore = correctionRecordRespVO.getTotalScore();
            userScore = correctionRecordRespVO.getScore();
        }
        LambdaUpdateWrapper<UserQuestionAnswerRecordDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserQuestionAnswerRecordDO::getId, event.getRecordId());
        updateWrapper.set(UserQuestionAnswerRecordDO::getRecordStatus, event.getSuccess() ? QuestionRecordStatusEnum.AI_CORRECTION_COMPLETED.getCode() : QuestionRecordStatusEnum.AI_CORRECTION_FAILED.getCode());
        updateWrapper.set(UserQuestionAnswerRecordDO::getTotalScore, totalScore);
        updateWrapper.set(UserQuestionAnswerRecordDO::getUserScore, userScore);
        userQuestionAnswerRecordService.update(updateWrapper);

        if (event.getSuccess()) { // 获取题目作答数据
            LambdaUpdateWrapper<UserQuestionAnswerDataDO> updateWrapper1 = new LambdaUpdateWrapper<>();
            updateWrapper1.eq(UserQuestionAnswerDataDO::getRecordId, event.getRecordId());
            updateWrapper1.set(UserQuestionAnswerDataDO::getAiCorrectStatus, 1);
            userQuestionAnswerDataService.update(updateWrapper1);
        }

        if (AiCorrectBizTypeEnum.MOCK_EXAM.getCode().equals(event.getBizType())) {
            examCorrectionManager.updateExamCorrectionStatus(event.getRecordId(), event.getSuccess());
        }
    }
}
