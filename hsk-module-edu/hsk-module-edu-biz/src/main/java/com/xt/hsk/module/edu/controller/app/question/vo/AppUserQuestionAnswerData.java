package com.xt.hsk.module.edu.controller.app.question.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class AppUserQuestionAnswerData implements Serializable {

    /**
     * 主键
     */
    private Long id;


    /**
     * 作答id
     */
    private Long recordId;


    /**
     * 用户答案
     */
    private String userAnswer;
    /**
     * 用户答案内容
     */
    private String userAnswerContent;
    /**
     * 用户展示的答案
     */
    private String userAnswerShow;


    /**
     * 参考答案
     */
    private String answer;
    /**
     * 展示的答案顺序
     */
    private String answerShow;
    /**
     * 是否正确 0-错误 1-正确
     */
    private Boolean isCorrect;
    /**
     * ai 批改状态 0-未批改 1-已批改
     */
    private Integer aiCorrectStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 题目id
     */
    private Long questionId;
    /**
     * 题目明细id
     */
    private Long questionDetailId;
    /**
     * 版本
     */
    private Integer version;
}
