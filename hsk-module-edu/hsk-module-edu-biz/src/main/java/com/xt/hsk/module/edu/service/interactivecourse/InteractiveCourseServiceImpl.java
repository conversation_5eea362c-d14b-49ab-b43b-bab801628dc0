package com.xt.hsk.module.edu.service.interactivecourse;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.constants.RedisKeyPrefix;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.framework.redis.utils.RedisUtil;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseBaseInfoRespVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitQuoteVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRefCountDto;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCoursePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.UserInteractiveCourseRecordDO;
import com.xt.hsk.module.edu.dal.mysql.interactivecourse.InteractiveCourseMapper;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseStatusEnum;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 互动课 Service 实现类
 * extends ServiceImpl<CoinStreamMapper, CoinStream> implements
 *         CoinStreamService
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class InteractiveCourseServiceImpl extends
    ServiceImpl<InteractiveCourseMapper, InteractiveCourseDO> implements
    InteractiveCourseService {
    @Resource
    private InteractiveCourseMapper interactiveCourseMapper;

    @Resource
    private UserInteractiveCourseRecordService userInteractiveCourseRecordService;

    @Resource
    private InteractiveCourseUnitService interactiveCourseUnitService;

    @Resource
    private RedisUtil redisUtil;


    @Override
    public PageResult<InteractiveCourseDO> getInteractiveCoursePage(
        InteractiveCoursePageReqVO pageReqVO) {
        return interactiveCourseMapper.selectPage(pageReqVO);
    }

    @Override
    public Long dataTotalCount(InteractiveCoursePageReqVO reqVO) {
        if (reqVO == null) {
            return interactiveCourseMapper.selectCount();
        }

        LambdaQueryWrapperX<InteractiveCourseDO> query = new LambdaQueryWrapperX<>();

        // 如果有选中的ID，优先使用ID列表查询，忽略其他条件
        if (CollUtil.isNotEmpty(reqVO.getIds())) {
            query.in(InteractiveCourseDO::getId, reqVO.getIds());
        } else {
            // 没有选中ID时，使用其他查询条件
            query.eqIfPresent(InteractiveCourseDO::getType, reqVO.getType())
                .likeIfPresent(InteractiveCourseDO::getCourseNameCn, reqVO.getCourseNameCn())
                .eqIfPresent(InteractiveCourseDO::getDisplayStatus, reqVO.getDisplayStatus())
                .eqIfPresent(InteractiveCourseDO::getHskLevel, reqVO.getHskLevel());
        }

        return interactiveCourseMapper.selectCount(query);
    }

    /**
     * 根据资源ID列表获取互动课基本信息列表
     *
     * @param resourceIdList 资源ID列表
     * @return 互动课基本信息列表
     */
    @Override
    public List<InteractiveCourseBaseInfoRespVO> listByResourceIdList(List<Long> resourceIdList) {
        if (CollUtil.isEmpty(resourceIdList)) {
            return Collections.emptyList();
        }
        return interactiveCourseMapper.listByResourceIdList(resourceIdList);
    }

    /**
     * 根据互动课程名称获取资源ID列表
     *
     * @param courseName 资源 ID 列表
     * @return 资源ID列表
     */
    @Override
    public List<Long> listResourceIdByCourseName(String courseName) {
        return interactiveCourseMapper.listResourceIdByCourseName(courseName);
    }

    @Override
    public List<InteractiveCourseUnitQuoteVO> getInteractiveCourseQuoteByWordId(Long wordId) {

        return interactiveCourseMapper.getInteractiveCourseQuoteByWordId(wordId);
    }

    /**
     * 按 HSK 级别获取显示计数 RedisKeyPrefix
     *
     * @param hskLevel HSK 级别
     * @return {@code Long }
     */
    @Override
    public Long getDisplayCountByHskLevel(Integer hskLevel) {
        if (Boolean.TRUE.equals(redisUtil.exists(RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_COUNT + ":" + hskLevel))) {
            return redisUtil.getLong(RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_COUNT + ":" + hskLevel);
        }
        Long count = this.lambdaQuery().eq(InteractiveCourseDO::getHskLevel, hskLevel)
            .eq(InteractiveCourseDO::getDisplayStatus, IsEnum.YES.getCode())
            .count();
        redisUtil.set(RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_COUNT + ":" + hskLevel, count, Duration.ofHours(1));
        return count;
    }

    /**
     * 获取已完成课程计数
     * 查询思路：1.先取所有状态为展示的课程ID 2.取单元数量 3.获取单元完成数量 4.单元最新记录全部都是已完成 那么课程计数+1
     *
     * @param userId   用户 ID
     * @param hskLevel HSK 级别
     * @return {@code Long }
     */
    @Override
    public Long getCompletedCourseCount(Long userId, Integer hskLevel) {
        if (userId == null || hskLevel == null) {
            return 0L;
        }

        // 1. 获取显示状态的课程ID列表
        List<Long> courseIdList = getDisplayCourseIds(hskLevel);
        if (CollUtil.isEmpty(courseIdList)) {
            return 0L;
        }

        // 2. 获取课程单元信息
        List<InteractiveCourseUnitDO> allUnits = getCourseUnits(courseIdList);
        if (CollUtil.isEmpty(allUnits)) {
            return 0L;
        }

        // 3. 获取用户完成记录并进行版本匹配
        Set<Long> versionMatchedCompletedUnitIds = getVersionMatchedCompletedUnitIds(userId, allUnits);
        if (CollUtil.isEmpty(versionMatchedCompletedUnitIds)) {
            return 0L;
        }

        // 4. 计算已完成的课程数量
        return calculateCompletedCourseCount(allUnits, versionMatchedCompletedUnitIds);
    }

    /**
     * 获取指定HSK等级下显示状态的课程ID列表
     *
     * @param hskLevel HSK等级
     * @return 课程ID列表
     */
    private List<Long> getDisplayCourseIds(Integer hskLevel) {
        return interactiveCourseMapper.selectList(
                new LambdaQueryWrapperX<InteractiveCourseDO>()
                    .eq(InteractiveCourseDO::getHskLevel, hskLevel)
                    .eq(InteractiveCourseDO::getDisplayStatus, IsEnum.YES.getCode())
                    .select(InteractiveCourseDO::getId))
            .stream()
            .map(InteractiveCourseDO::getId)
            .toList();
    }

    /**
     * 获取课程下显示状态的单元信息
     *
     * @param courseIdList 课程ID列表
     * @return 单元信息列表
     */
    private List<InteractiveCourseUnitDO> getCourseUnits(List<Long> courseIdList) {
        return interactiveCourseUnitService.lambdaQuery()
            .select(InteractiveCourseUnitDO::getId, InteractiveCourseUnitDO::getCourseId, InteractiveCourseUnitDO::getResourceVersion)
            .in(InteractiveCourseUnitDO::getCourseId, courseIdList)
            .eq(InteractiveCourseUnitDO::getDisplayStatus, IsEnum.YES.getCode())
            .list();
    }

    /**
     * 获取版本匹配的已完成单元ID集合
     *
     * @param userId   用户ID
     * @param allUnits 所有单元信息
     * @return 版本匹配的已完成单元ID集合
     */
    private Set<Long> getVersionMatchedCompletedUnitIds(Long userId, List<InteractiveCourseUnitDO> allUnits) {
        // 提取单元ID列表
        List<Long> allUnitIds = allUnits.stream()
            .map(InteractiveCourseUnitDO::getId)
            .toList();

        // 创建单元ID到资源版本的映射
        Map<Long, Integer> unitResourceVersionMap = allUnits.stream()
            .collect(Collectors.toMap(
                InteractiveCourseUnitDO::getId,
                InteractiveCourseUnitDO::getResourceVersion
            ));

        // 获取用户完成的单元记录
        List<UserInteractiveCourseRecordDO> completedRecords = getUserCompletedRecords(userId, allUnitIds);
        if (CollUtil.isEmpty(completedRecords)) {
            return Collections.emptySet();
        }

        // 过滤出资源版本匹配的完成记录
        return completedRecords.stream()
            .filter(record -> isResourceVersionMatched(record, unitResourceVersionMap))
            .map(UserInteractiveCourseRecordDO::getUnitId)
            .collect(Collectors.toSet());
    }

    /**
     * 获取用户完成的单元记录
     *
     * @param userId     用户ID
     * @param allUnitIds 所有单元ID列表
     * @return 用户完成的单元记录列表
     */
    private List<UserInteractiveCourseRecordDO> getUserCompletedRecords(Long userId, List<Long> allUnitIds) {
        return userInteractiveCourseRecordService.lambdaQuery()
            .select(UserInteractiveCourseRecordDO::getUnitId, UserInteractiveCourseRecordDO::getCourseId, UserInteractiveCourseRecordDO::getResourceVersion)
            .eq(UserInteractiveCourseRecordDO::getUserId, userId)
            .in(UserInteractiveCourseRecordDO::getUnitId, allUnitIds)
            .eq(UserInteractiveCourseRecordDO::getIsLatest, IsEnum.YES.getCode())
            .eq(UserInteractiveCourseRecordDO::getStatus, InteractiveCourseStatusEnum.COMPLETED.getCode())
            .list();
    }

    /**
     * 检查记录的资源版本是否与单元的资源版本匹配
     *
     * @param record                  用户学习记录
     * @param unitResourceVersionMap 单元ID到资源版本的映射
     * @return 是否匹配
     */
    private boolean isResourceVersionMatched(UserInteractiveCourseRecordDO record, Map<Long, Integer> unitResourceVersionMap) {
        Integer unitResourceVersion = unitResourceVersionMap.get(record.getUnitId());
        Integer recordResourceVersion = record.getResourceVersion();
        return Objects.equals(unitResourceVersion, recordResourceVersion);
    }

    /**
     * 计算已完成的课程数量
     *
     * @param allUnits                        所有单元信息
     * @param versionMatchedCompletedUnitIds 版本匹配的已完成单元ID集合
     * @return 已完成的课程数量
     */
    private Long calculateCompletedCourseCount(List<InteractiveCourseUnitDO> allUnits, Set<Long> versionMatchedCompletedUnitIds) {
        // 统计每个课程的总单元数
        Map<Long, Long> courseTotalUnits = allUnits.stream()
            .collect(Collectors.groupingBy(
                InteractiveCourseUnitDO::getCourseId,
                Collectors.counting()
            ));

        // 统计每个课程的已完成单元数
        Map<Long, Long> courseCompletedUnits = allUnits.stream()
            .filter(unit -> versionMatchedCompletedUnitIds.contains(unit.getId()))
            .collect(Collectors.groupingBy(
                InteractiveCourseUnitDO::getCourseId,
                Collectors.counting()
            ));

        // 计算已完成的课程数量（所有单元都完成的课程）
        return courseTotalUnits.entrySet().stream()
            .filter(entry -> {
                Long courseId = entry.getKey();
                Long totalUnits = entry.getValue();
                Long completedUnits = courseCompletedUnits.getOrDefault(courseId, 0L);
                return totalUnits.equals(completedUnits) && totalUnits > 0;
            })
            .count();
    }

    @Override
    @Cacheable(value = RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_LIST,
        key = "#hskLevel",
        unless = "#hskLevel == null")
    @SuppressWarnings("java:S6204")
    public List<Long> getDisplayInteractiveCourseListByHskLevel(Integer hskLevel) {
        if (hskLevel == null) {
            return Collections.emptyList();
        }
        return this.lambdaQuery().eq(InteractiveCourseDO::getHskLevel, hskLevel)
            .eq(InteractiveCourseDO::getDisplayStatus, IsEnum.YES.getCode())
            .select(InteractiveCourseDO::getId)
            .list().stream()
            .map(InteractiveCourseDO::getId)
            // 不使用toList()的原因是：toList()会返回包装类 序列化进redis时不会带类型信息再次查询时会报错
            .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = RedisKeyPrefix.INTERACTIVE_COURSE_POSITION,
        key = "#hskLevel + ':' + #courseId",
        unless = "#hskLevel == null || #courseId == null")
    public Integer getCoursePositionInLevel(Integer hskLevel, Long courseId) {
        if (hskLevel == null || courseId == null) {
            return 0;
        }
        return this.lambdaQuery().eq(InteractiveCourseDO::getHskLevel, hskLevel)
            .eq(InteractiveCourseDO::getDisplayStatus, IsEnum.YES.getCode())
            .orderByAsc(InteractiveCourseDO::getSort)
            .select(InteractiveCourseDO::getId)
            .list().stream()
            .map(InteractiveCourseDO::getId)
            .toList()
            .indexOf(courseId) + 1;
    }

    @Override
    public boolean isQuoteWord(Long wordId) {
        return interactiveCourseMapper.isQuoteWord(wordId) > 0;
    }

    @Override
    public PageResult<InteractiveCourseDO> getInteractiveCoursePageForApp(
        AppInteractiveCoursePageReqVO appPageReqVO, Long userId) {
        // 直接使用APP专用的Mapper方法查询数据
        return interactiveCourseMapper.selectPageForApp(appPageReqVO);
    }

    @Override
    public long countQuestionQuote(List<Long> questionIds) {
        if (CollUtil.isEmpty(questionIds)) {
            return 0L;
        }

        return interactiveCourseMapper.countQuestionQuote(questionIds);
    }

    @Override
    public List<QuestionRefCountDto> getInteractiveCourseQuestionQuoteCount(Collection<Long> questionIds) {

        return interactiveCourseMapper.getInteractiveCourseQuestionQuoteCount(questionIds);
    }

    @Override
    @Cacheable(value = RedisKeyPrefix.INTERACTIVE_COURSE_FIRST,
        key = "#hskLevel",
        unless = "#hskLevel == null")
    public InteractiveCourseDO getFirstCourseByHskLevel(Integer hskLevel) {
        return  this.lambdaQuery()
            .eq(InteractiveCourseDO::getHskLevel, hskLevel)
            .eq(InteractiveCourseDO::getDisplayStatus, IsShowEnum.SHOW.getCode())
            .orderByAsc(InteractiveCourseDO::getSort)
            .last("limit 1")
            .one();
    }

}