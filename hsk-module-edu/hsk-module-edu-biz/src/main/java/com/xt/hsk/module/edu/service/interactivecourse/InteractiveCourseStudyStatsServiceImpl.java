package com.xt.hsk.module.edu.service.interactivecourse;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseStudyStatsDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.UserInteractiveCourseRecordDO;
import com.xt.hsk.module.edu.dal.mysql.interactivecourse.InteractiveCourseStudyStatsMapper;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 互动课程学习统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class InteractiveCourseStudyStatsServiceImpl extends
    ServiceImpl<InteractiveCourseStudyStatsMapper, InteractiveCourseStudyStatsDO> implements
    InteractiveCourseStudyStatsService {


    @Resource
    private InteractiveCourseUnitService interactiveCourseUnitService;

    @Resource
    @Lazy
    private UserInteractiveCourseRecordService userInteractiveCourseRecordService;


    /**
     * 获取课程学习计数
     * 优先查询课程级别的统计记录（unitId为NULL），如果不存在则通过用户记录表统计去重用户数
     *
     * @param courseId 课程 ID
     * @return {@code Integer }
     */
    @Override
    public Integer getCourseStudyCount(Long courseId) {
        // 优先查询课程级别的统计记录
        InteractiveCourseStudyStatsDO courseStats = lambdaQuery()
            .eq(InteractiveCourseStudyStatsDO::getCourseId, courseId)
            .isNull(InteractiveCourseStudyStatsDO::getUnitId)
            .one();

        if (courseStats != null) {
            return courseStats.getStudyCount();
        }

        // 如果没有课程级别统计，则通过用户记录表统计去重用户数（兼容旧数据）
        List<Long> unitIdList = interactiveCourseUnitService.lambdaQuery()
            .eq(InteractiveCourseUnitDO::getCourseId, courseId)
            .eq(InteractiveCourseUnitDO::getDisplayStatus, IsEnum.YES.getCode())
            .select(InteractiveCourseUnitDO::getId)
            .list().stream().map(InteractiveCourseUnitDO::getId).toList();
        if (CollUtil.isEmpty(unitIdList)) {
            return 0;
        }

        // 查询学习过该课程任意单元的去重用户数
        Long distinctUserCount = userInteractiveCourseRecordService.
                countDistinctUsersByCourseAndUnits(courseId, unitIdList);

        return distinctUserCount.intValue();
    }

    @Override
    public Long getUnitStudyCount(Long unitId) {
        // 从数据库查询
        InteractiveCourseStudyStatsDO stats = lambdaQuery()
            .eq(InteractiveCourseStudyStatsDO::getUnitId, unitId)
            .one();
        return stats != null ? stats.getStudyCount() : 0L;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStudyStats(Long courseId, Long unitId, Long userId, boolean isFirstTimeStudy) {
        // 只有用户首次学习该单元时才更新学习人数统计
        if (isFirstTimeStudy) {
            // 更新单元级别统计
            updateOrCreateStats(courseId, unitId);
        }
        // 检查用户是否首次学习该课程，如果是则更新课程级别统计
        boolean isFirstTimeCourseStudy = !userInteractiveCourseRecordService.lambdaQuery()
            .eq(UserInteractiveCourseRecordDO::getUserId, userId)
            .eq(UserInteractiveCourseRecordDO::getCourseId, courseId)
            .exists();

        if (isFirstTimeCourseStudy) {
            // 更新课程级别统计（unitId为NULL）
            updateOrCreateCourseStats(courseId);
        }
    }

    /**
     * 更新或创建单元级别统计记录
     * 使用分布式锁防止并发问题：
     * 1. 防止查询-更新之间的竞态条件导致计数不准确
     * 2. 防止多个线程同时插入相同unitId的记录
     *
     * @param courseId 课程ID
     * @param unitId   单元ID
     */
    @Lock4j(name = "updateOrCreateStats", keys = {"#unitId"}, acquireTimeout = 3000, expire = 10000)
    private void updateOrCreateStats(Long courseId, Long unitId) {
        if (courseId == null || unitId == null) {
            log.warn("参数不全无法更新互动课学习人数记录: courseId={}, unitId={}", courseId, unitId);
            return;
        }
        // 查询这个单元是否存在统计记录
        InteractiveCourseStudyStatsDO stats = lambdaQuery()
            .eq(InteractiveCourseStudyStatsDO::getUnitId, unitId)
            .one();

        if (stats != null) {
            // 存在则增加计数
            this.lambdaUpdate()
                .set(InteractiveCourseStudyStatsDO::getStudyCount, stats.getStudyCount() + 1)
                .eq(InteractiveCourseStudyStatsDO::getUnitId, unitId)
                .update();
        } else {
            // 不存在则创建新记录
            stats = InteractiveCourseStudyStatsDO.builder()
                .courseId(courseId)
                .unitId(unitId)
                // 初始为1
                .studyCount(1)
                .build();
            this.save(stats);
        }
    }

    /**
     * 更新或创建课程级别统计记录
     * 使用分布式锁防止并发问题
     *
     * @param courseId 课程ID
     */
    @Lock4j(name = "updateOrCreateCourseStats", keys = {"#courseId"}, acquireTimeout = 3000, expire = 10000)
    private void updateOrCreateCourseStats(Long courseId) {
        if (courseId == null) {
            log.warn("课程ID为空，无法更新课程级别学习人数统计: courseId={}", courseId);
            return;
        }

        // 查询课程级别统计记录（unitId为NULL）
        InteractiveCourseStudyStatsDO courseStats = lambdaQuery()
            .eq(InteractiveCourseStudyStatsDO::getCourseId, courseId)
            .isNull(InteractiveCourseStudyStatsDO::getUnitId)
            .one();

        if (courseStats != null) {
            // 存在则增加计数
            this.lambdaUpdate()
                .set(InteractiveCourseStudyStatsDO::getStudyCount, courseStats.getStudyCount() + 1)
                .eq(InteractiveCourseStudyStatsDO::getCourseId, courseId)
                .isNull(InteractiveCourseStudyStatsDO::getUnitId)
                .update();
        } else {
            // 不存在则创建新记录
            courseStats = InteractiveCourseStudyStatsDO.builder()
                .courseId(courseId)
                // 课程级别统计，unitId为NULL
                .unitId(null)
                // 初始为1
                .studyCount(1)
                .build();
            this.save(courseStats);
        }
    }

}