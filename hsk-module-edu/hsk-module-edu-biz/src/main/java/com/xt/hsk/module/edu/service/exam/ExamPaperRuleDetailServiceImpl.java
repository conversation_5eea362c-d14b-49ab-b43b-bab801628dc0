package com.xt.hsk.module.edu.service.exam;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleDetailPageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleQuestionTypeRespVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamPaperRuleDetailDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiontype.QuestionTypeDO;
import com.xt.hsk.module.edu.dal.mysql.exam.ExamPaperRuleDetailMapper;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import com.xt.hsk.module.edu.service.question.questiontype.QuestionTypeService;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;


/**
 * 模考组卷规则明细 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Service
public class ExamPaperRuleDetailServiceImpl extends ServiceImpl<ExamPaperRuleDetailMapper, ExamPaperRuleDetailDO> implements ExamPaperRuleDetailService {

    @Resource
    private ExamPaperRuleDetailMapper examPaperRuleDetailMapper;

    @Resource
    private QuestionTypeService questionTypeService;

    @Override
    public PageResult<ExamPaperRuleDetailDO> selectPage(ExamPaperRuleDetailPageReqVO pageReqVO) {

        return examPaperRuleDetailMapper.selectPage(pageReqVO);
    }

    /**
     * 获取模考组卷规则明细的题型列表
     *
     * @param paperRuleId 组卷规则ID
     * @return 模考组卷规则明细的题型列表
     */
    @Override
    public List<ExamPaperRuleQuestionTypeRespVO> getPaperRuleQuestionType(Long paperRuleId) {
        List<ExamPaperRuleQuestionTypeRespVO> respVOList = examPaperRuleDetailMapper.getPaperRuleQuestionType(paperRuleId);

        // 设置题型名称信息
        setQuestionTypeNames(respVOList);

        return respVOList;
    }

    /**
     * 设置题型名称信息
     *
     * @param paperRuleQuestionTypeList 题型列表
     */
    @Override
    public void setQuestionTypeNames(List<ExamPaperRuleQuestionTypeRespVO> paperRuleQuestionTypeList) {
        // 提取所有题型ID
        List<Long> questionTypeIdList = extractQuestionTypeIds(paperRuleQuestionTypeList);
        if (CollUtil.isEmpty(questionTypeIdList)) {
            return;
        }

        // 批量查询题型名称并构建映射
        Map<Long, String> questionTypeNameMap = buildQuestionTypeNameMap(questionTypeIdList);

        // 为每个题型设置名称信息
        paperRuleQuestionTypeList.forEach(respVO -> {
            // 设置单位描述
            respVO.setUnitDesc(ExamQuestionTypeUnitEnum.getDescByCode(respVO.getUnit()));

            // 设置题型名称列表
            List<String> questionTypeNameList = getQuestionTypeNameList(respVO.getQuestionTypeIds(), questionTypeNameMap);
            respVO.setQuestionTypeNameList(questionTypeNameList);
        });
    }

    /**
     * 提取所有题型ID
     *
     * @param paperRuleQuestionTypeList 题型列表
     * @return 题型ID列表
     */
    private List<Long> extractQuestionTypeIds(List<ExamPaperRuleQuestionTypeRespVO> paperRuleQuestionTypeList) {
        if (CollUtil.isEmpty(paperRuleQuestionTypeList)) {
            return Collections.emptyList();
        }

        return paperRuleQuestionTypeList.stream()
                .filter(e -> CharSequenceUtil.isNotBlank(e.getQuestionTypeIds()))
                .map(e -> JSONUtil.toList(e.getQuestionTypeIds(), Long.class))
                .filter(CollUtil::isNotEmpty)
                .flatMap(List::stream)
                .distinct()
                .toList();
    }

    /**
     * 构建题型名称映射
     *
     * @param questionTypeIdList 题型ID列表
     * @return 题型ID到名称的映射
     */
    private Map<Long, String> buildQuestionTypeNameMap(List<Long> questionTypeIdList) {
        if (CollUtil.isEmpty(questionTypeIdList)) {
            return Collections.emptyMap();
        }

        return questionTypeService.lambdaQuery()
                .in(QuestionTypeDO::getId, questionTypeIdList)
                .list()
                .stream()
                .collect(Collectors.toMap(
                        QuestionTypeDO::getId,
                        QuestionTypeDO::getNameCn,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 获取题型名称列表
     *
     * @param questionTypeIds     题型ID字符串
     * @param questionTypeNameMap 题型名称映射
     * @return 题型名称列表
     */
    private List<String> getQuestionTypeNameList(String questionTypeIds, Map<Long, String> questionTypeNameMap) {
        if (CharSequenceUtil.isBlank(questionTypeIds)) {
            return Collections.emptyList();
        }

        return JSONUtil.toList(questionTypeIds, Long.class)
                .stream()
                .map(questionTypeNameMap::get)
                .filter(CharSequenceUtil::isNotBlank)
                .toList();
    }

}