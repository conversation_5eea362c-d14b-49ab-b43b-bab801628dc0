package com.xt.hsk.module.edu.service.word;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.edu.dal.dataobject.word.WordExampleDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import jakarta.validation.Valid;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordExamplePageReqVO;

import java.util.List;

/**
 * 释义关联例句 Service 接口
 *
 * <AUTHOR>
 */
public interface WordExampleService extends IService<WordExampleDO> {
    PageResult<WordExampleDO> selectPage(@Valid WordExamplePageReqVO pageReqVO);

    void removeByWordId(Long wordId);

    List<WordExampleDO> getByMeaningIds(List<Long> meaningIds);
}