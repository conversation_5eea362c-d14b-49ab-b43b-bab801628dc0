package com.xt.hsk.module.edu.service.sourceword;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.xt.hsk.framework.common.util.collection.CollectionUtils;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordDO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordExampleDO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordMeaningDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 汉语词典基础数据 Manager
 *
 * <AUTHOR>
 */
@Service
public class SourceWordManager {

    @Autowired
    private SourceWordService sourceWordService;

    @Autowired
    private SourceWordMeaningService sourceWordMeaningService;

    @Autowired
    private SourceWordExampleService sourceWordExampleService;

    private static final Map<String, String> HANZII_TO_TANG_EN = new HashMap<>();


    static {
        // 动词
        HANZII_TO_TANG_EN.put("v", "v");
        HANZII_TO_TANG_EN.put("动", "v");
        HANZII_TO_TANG_EN.put("verb", "v");

        // 名词
        HANZII_TO_TANG_EN.put("n", "n");
        HANZII_TO_TANG_EN.put("noun", "n");
        HANZII_TO_TANG_EN.put("名", "n");
        HANZII_TO_TANG_EN.put("punct", "n");

        // 数词
        HANZII_TO_TANG_EN.put("numb", "numb");
        HANZII_TO_TANG_EN.put("numeral", "numb");

        // 拟声词
        HANZII_TO_TANG_EN.put("onom", "onom");
        HANZII_TO_TANG_EN.put("onomatopoeia", "onom");

        // 形容词
        HANZII_TO_TANG_EN.put("adjective", "adj");
        HANZII_TO_TANG_EN.put("adj", "adj");
        HANZII_TO_TANG_EN.put("形", "adj");

        // 副词
        HANZII_TO_TANG_EN.put("adv", "adv");
        HANZII_TO_TANG_EN.put("adverb", "adv");
        HANZII_TO_TANG_EN.put("class", "adv");


        HANZII_TO_TANG_EN.put("time", "time");

        HANZII_TO_TANG_EN.put("measure", "measure");
        HANZII_TO_TANG_EN.put("measure word", "measure");

        HANZII_TO_TANG_EN.put("sv", "sv");

        HANZII_TO_TANG_EN.put("nlocal", "nlocal");

        HANZII_TO_TANG_EN.put("pro", "pron");
        HANZII_TO_TANG_EN.put("pronoun", "pron");

        HANZII_TO_TANG_EN.put("prep", "prep");
        HANZII_TO_TANG_EN.put("preposition", "prep");

        HANZII_TO_TANG_EN.put("part", "part");
        HANZII_TO_TANG_EN.put("av", "part");

        HANZII_TO_TANG_EN.put("conj", "conj");
        HANZII_TO_TANG_EN.put("conjunction", "conj");
        HANZII_TO_TANG_EN.put("suff", "conj");
        HANZII_TO_TANG_EN.put("pref", "conj");

        HANZII_TO_TANG_EN.put("sentence", "phrase");
        HANZII_TO_TANG_EN.put("phrase", "phrase");
        HANZII_TO_TANG_EN.put("idioms", "phrase");
        HANZII_TO_TANG_EN.put("proverb", "phrase");

        HANZII_TO_TANG_EN.put("intj", "intj");
        HANZII_TO_TANG_EN.put("interjection", "intj");

        HANZII_TO_TANG_EN.put("particle", "particle");

    }

    private String bastAudioPath="https://hanzii.net/audios/cnvi/0/%s.mp3";

    public void executeWord(){
//        // 查询所有中文的单词
//        List<SourceWordDO> wordList = sourceWordService.getWordByType("cnen");
//        List<SourceWordDO> sourceWordDOS = deduplicateWords(wordList);
//        for (SourceWordDO sourceWordDO : sourceWordDOS) {
//            sourceWordDO.setStatus(1);
//        }
//        sourceWordService.saveOrUpdateBatch(sourceWordDOS);
    }



    public void executeMeans(){
        // 查询出所有的单词
        List<SourceWordDO> wordList = sourceWordService.list();
        // 更具单词名称查询
        for (SourceWordDO sourceWordDO : wordList) {
            if (sourceWordDO.getId()<21000){
                continue;
            }
            List<SourceWordMeaningDO> meaningByWord = sourceWordMeaningService.getByWord(sourceWordDO.getWord());
            List<SourceWordMeaningDO> sourceWordMeaningDOS = deduplicateWordMean(meaningByWord);
            for (SourceWordMeaningDO sourceWordMeaningDO : sourceWordMeaningDOS){
               // 3代表是需要的数据
               sourceWordMeaningDO.setStatus(3);
            }
            sourceWordMeaningService.updateBatchById(sourceWordMeaningDOS);
            for (int i = 0; i < sourceWordMeaningDOS.size(); i++) {
                for (int j = i+1; j < sourceWordMeaningDOS.size(); j++) {
                    if (sourceWordMeaningDOS.get(i).getExplainCn().equals(sourceWordMeaningDOS.get(j).getExplainCn())) {
                        // 比较wordId大小，如果wordId大的，则将大的mean设置到小的mean_vi中
                        if (sourceWordMeaningDOS.get(i).getWordId() > sourceWordMeaningDOS.get(j).getWordId()) {
                            SourceWordMeaningDO wordMeaningsSmall = sourceWordMeaningDOS.get(j);
                            SourceWordMeaningDO wordMeaningsBig = sourceWordMeaningDOS.get(i);
                            wordMeaningsSmall.setMeanVi(wordMeaningsBig.getMean());
                            wordMeaningsSmall.setStatus(4);
                            sourceWordMeaningService.updateById(wordMeaningsSmall);
                            wordMeaningsBig.setStatus(5);
                            sourceWordMeaningService.updateById(wordMeaningsBig);
                        }else if (sourceWordMeaningDOS.get(i).getWordId() < sourceWordMeaningDOS.get(j).getWordId()) {
                            SourceWordMeaningDO wordMeaningsSmall = sourceWordMeaningDOS.get(i);
                            SourceWordMeaningDO wordMeaningsBig = sourceWordMeaningDOS.get(j);
                            wordMeaningsSmall.setMeanVi(wordMeaningsBig.getMean());
                            wordMeaningsSmall.setStatus(4);
                            sourceWordMeaningService.updateById(wordMeaningsSmall);
                            wordMeaningsBig.setStatus(5);
                            sourceWordMeaningService.updateById(wordMeaningsBig);
                        }
                    }
                }
            }
        }
    }

//    public void executeMeans(){
//        // 查询出所有的单词
//        List<SourceWordDO> wordList = sourceWordService.list();
//        // 更具单词名称查询
//        for (SourceWordDO sourceWordDO : wordList) {
//            List<SourceWordMeaningDO> byWord = sourceWordMeaningService.getByWord(sourceWordDO.getWord());
//            List<SourceWordMeaningDO> sourceWordMeaningDOS = deduplicateWordMean(byWord);
//            for (SourceWordMeaningDO sourceWordMeaningDO : sourceWordMeaningDOS){
//                // 3代表是需要的数据
//                sourceWordMeaningDO.setStatus(3);
//            }
//            sourceWordMeaningService.updateBatchById(sourceWordMeaningDOS);
//        }
//    }

   /* public void executeWordFiles(){
        // 查询出所有的单词
        List<SourceWordDO> wordList = sourceWordService.list();
        for (SourceWordDO wordDO : wordList) {
            if (wordDO.getStatus() == 2){
                continue;
            }
            String antonyms = wordDO.getAntonyms();
            if (antonyms != null && !antonyms.isEmpty()) {
                // ["恨", "恶", "憎"] 转化成List<String>
                List<String> antonymList = JSONObject.parseArray(antonyms, String.class);
                if (CollectionUtil.isNotEmpty(antonymList)){
                    // 将这个antonymList每个词用";"隔开成字符串,去掉最后一个拼接符号
                    String antonymsStr = antonymList.stream().map(antonym -> antonym + ";").collect(Collectors.joining());
                    // 去掉antonymsStr最后一个;符号
                    antonymsStr = antonymsStr.substring(0, antonymsStr.length() - 1);
                    wordDO.setAntonyms(antonymsStr);
                }

            }
            // 同理处理synonyms
            String synonyms = wordDO.getSynonyms();
            if (synonyms != null && !synonyms.isEmpty()) {
                List<String> synonymList = JSONObject.parseArray(synonyms, String.class);
                if (CollectionUtil.isNotEmpty(synonymList)){
                    String synonymsStr = synonymList.stream().map(synonym -> synonym + ";").collect(Collectors.joining());
                    synonymsStr = synonymsStr.substring(0, synonymsStr.length() - 1);
                    wordDO.setSynonyms(synonymsStr);
                }
            }
            String audioId = wordDO.getAudioUrl();
            String formattedUrl = String.format(bastAudioPath, audioId);
            wordDO.setAudioUrl(formattedUrl);
            wordDO.setStatus(2);
            sourceWordService.updateById(wordDO);
        }

    }*/

    public void  executeMeanField(){
        List<SourceWordMeaningDO> wordExampleDOS = sourceWordMeaningService.list();
        for (SourceWordMeaningDO sourceWordMeaningDO : wordExampleDOS) {
            String sourceKind = sourceWordMeaningDO.getKind();
            if (sourceKind != null && !sourceKind.isEmpty()) {
                // 用逗号分隔
                String[] kinds = sourceKind.split(",");
                if (kinds.length > 0){
                    StringBuilder kindStr= new StringBuilder();
                    // 用,拼接
                    for (String kind : kinds) {
                        // 去掉前后空格
                        kindStr.append(HANZII_TO_TANG_EN.get(kind.trim())).append(",");
                    }// 去掉最后一个符号
                    kindStr = new StringBuilder(kindStr.substring(0, kindStr.length() - 1));
                    sourceWordMeaningDO.setKind(kindStr.toString());
                }
                sourceWordMeaningService.updateById(sourceWordMeaningDO);
            }
        }
    }


    public void executeExample(){
        // 获取所有单词
        List<SourceWordDO> wordList = sourceWordService.list();
        for (SourceWordDO sourceWordDO : wordList) {
            // 通过词查询出例句
            List<SourceWordExampleDO> exampleByWord = sourceWordExampleService.getByWord(sourceWordDO.getWord());
            // 进行例句去重word，exampleType,translationEn,translationEn
            List<SourceWordExampleDO> deduplicatedExamples = deduplicateExamples(exampleByWord);
            for (SourceWordExampleDO sourceWordExampleDO : deduplicatedExamples) {
                sourceWordExampleDO.setStatus(3);
            }
            sourceWordExampleService.updateBatchById(deduplicatedExamples);
            for (int i = 0; i < deduplicatedExamples.size(); i++) {
                for (int j = i+1; j < deduplicatedExamples.size(); j++) {
                    if (deduplicatedExamples.get(i).getTranslationCn().equals(deduplicatedExamples.get(j).getTranslationCn())) {
                        // 比较wordId大小，如果wordId大的，则将大的mean设置到小的mean_vi中
                        if (deduplicatedExamples.get(i).getWordId() > deduplicatedExamples.get(j).getWordId()) {
                            SourceWordExampleDO wordMeaningsSmall = deduplicatedExamples.get(j);
                            SourceWordExampleDO wordMeaningsBig = deduplicatedExamples.get(i);
                            wordMeaningsSmall.setTranslationVi(wordMeaningsBig.getTranslationEn());
                            wordMeaningsSmall.setStatus(4);
                            sourceWordExampleService.updateById(wordMeaningsSmall);
                            wordMeaningsBig.setStatus(5);
                            sourceWordExampleService.updateById(wordMeaningsBig);
                        }else if (deduplicatedExamples.get(i).getWordId() < deduplicatedExamples.get(j).getWordId()) {
                            SourceWordExampleDO wordMeaningsSmall = deduplicatedExamples.get(i);
                            SourceWordExampleDO wordMeaningsBig = deduplicatedExamples.get(j);
                            wordMeaningsSmall.setTranslationVi(wordMeaningsBig.getTranslationEn());
                            wordMeaningsSmall.setStatus(4);
                            sourceWordExampleService.updateById(wordMeaningsSmall);
                            wordMeaningsBig.setStatus(5);
                            sourceWordExampleService.updateById(wordMeaningsBig);
                        }
                    }
                }
            }

        }
    }

    private List<SourceWordExampleDO> deduplicateExamples(List<SourceWordExampleDO> exampleByWord) {
        // 进行例句去重word，exampleType,translationCn,translationEn
        return exampleByWord.stream()
                // 过滤空值（根据需求调整，若字段允许null需修改判断逻辑）
                .filter(exampleDO -> exampleDO.getWord() != null
                        && exampleDO.getExampleType() != null
                        && exampleDO.getTranslationCn() != null
                        && exampleDO.getTranslationEn() != null
                )
                // 按{word, pinyin, phonetic}分组，每组保留id最小的记录
                .collect(Collectors.groupingBy(
                        // 分组键：三个字段的组合
                        exampleDO -> new GroupKeyExample(
                                exampleDO.getWord(),exampleDO.getExampleType(),
                                exampleDO.getTranslationCn(),exampleDO.getTranslationEn()),
                        // 合并策略：保留id较小的元素
                        Collectors.collectingAndThen(
                                Collectors.minBy(Comparator.comparingLong(SourceWordExampleDO::getWordId)),
                                optional -> optional.orElseThrow() // 确保每组至少有一个元素（已过滤空值）
                        )
                ))
                // 从Map值中提取结果列表
                .values()
                .stream()
                .collect(Collectors.toList());
    }


//    public List<SourceWordDO> deduplicateWords(List<SourceWordDO> originalList) {
//        return originalList.stream()
//                // 过滤空值（根据需求调整，若字段允许null需修改判断逻辑）
//                .filter(word -> word.getWord() != null
//                        && word.getPinyin() != null
//                        && word.getPhonetic() != null)
//                // 按{word, pinyin, phonetic}分组，每组保留id最小的记录
//                .collect(Collectors.groupingBy(
//                        // 分组键：三个字段的组合
//                        word -> new GroupKey(word.getWord(), word.getPinyin()),
//                        // 合并策略：保留id较小的元素
//                        Collectors.collectingAndThen(
//                                Collectors.minBy(Comparator.comparingLong(SourceWordDO::getId)),
//                                optional -> optional.orElseThrow() // 确保每组至少有一个元素（已过滤空值）
//                        )
//                ))
//                // 从Map值中提取结果列表
//                .values()
//                .stream()
//                .collect(Collectors.toList());
//    }

    public List<SourceWordMeaningDO> deduplicateWordMean(List<SourceWordMeaningDO> originalList) {
        return originalList.stream()
                // 过滤空值（根据需求调整，若字段允许null需修改判断逻辑）
                .filter(word -> word.getWord() != null
                        && word.getMean() != null)
                // 按{word, pinyin, phonetic}分组，每组保留id最小的记录
                .collect(Collectors.groupingBy(
                        // 分组键：三个字段的组合
                        word -> new GroupKey(word.getWord(), word.getMean(),word.getExplainCn()),
                        // 合并策略：保留id较小的元素
                        Collectors.collectingAndThen(
                                Collectors.minBy(Comparator.comparingLong(SourceWordMeaningDO::getWordId)),
                                optional -> optional.orElseThrow() // 确保每组至少有一个元素（已过滤空值）
                        )
                ))
                // 从Map值中提取结果列表
                .values()
                .stream()
                .collect(Collectors.toList());
    }


    private record GroupKey(String word, String pinyin,String explainCn) {

    }

    private record GroupKeyExample(String word, String type,String translationCn,String translationEn) {

    }
}