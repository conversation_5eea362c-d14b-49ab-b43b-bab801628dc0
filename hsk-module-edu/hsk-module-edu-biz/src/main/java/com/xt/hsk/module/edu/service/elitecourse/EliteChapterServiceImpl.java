package com.xt.hsk.module.edu.service.elitecourse;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteChapterPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteChapterDO;
import com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteChapterMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 精品课章节 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
@Validated
public class EliteChapterServiceImpl extends ServiceImpl<EliteChapterMapper, EliteChapterDO> implements EliteChapterService {

    @Resource
    private EliteChapterMapper eliteChapterMapper;

    @Override
    public PageResult<EliteChapterDO> selectPage(EliteChapterPageReqVO pageReqVO) {

        return eliteChapterMapper.selectPage(pageReqVO);
    }

    /**
     * 获取课程下最大排序
     * <p>
     * 获取最大排序，如果没有则返回0
     *
     * @param courseId 课程 ID
     * @return 最大排序
     */
    @Override
    public Integer getMaxSortByCourseId(Long courseId) {
        return lambdaQuery()
                .eq(EliteChapterDO::getCourseId, courseId)
                .select(EliteChapterDO::getSort)
                .orderByDesc(EliteChapterDO::getSort)
                .last("LIMIT 1")
                .oneOpt()
                .map(EliteChapterDO::getSort)
                .orElse(0);
    }

}