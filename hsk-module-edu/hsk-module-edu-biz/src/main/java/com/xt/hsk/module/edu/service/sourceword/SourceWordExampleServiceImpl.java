package com.xt.hsk.module.edu.service.sourceword;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordExamplePageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordExampleDO;
import com.xt.hsk.module.edu.dal.mysql.sourceword.SourceWordExampleMapper;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;


/**
 * 释义关联例句 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SourceWordExampleServiceImpl extends ServiceImpl<SourceWordExampleMapper, SourceWordExampleDO> implements SourceWordExampleService {

    @Autowired
    private SourceWordExampleMapper wordExampleMapper;

    @Override
    public PageResult<SourceWordExampleDO> selectPage(WordExamplePageReqVO pageReqVO) {

        return wordExampleMapper.selectPage(pageReqVO);
    }

    @Override
    public void removeByWordId(Long wordId) {
        LambdaUpdateWrapper<SourceWordExampleDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(SourceWordExampleDO::getWordId, wordId);
        wordExampleMapper.delete(queryWrapper);
    }

    @Override
    public List<SourceWordExampleDO> getByWord(String word) {
        LambdaUpdateWrapper<SourceWordExampleDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(SourceWordExampleDO::getWord, word);
        return wordExampleMapper.selectList(queryWrapper);
    }


}