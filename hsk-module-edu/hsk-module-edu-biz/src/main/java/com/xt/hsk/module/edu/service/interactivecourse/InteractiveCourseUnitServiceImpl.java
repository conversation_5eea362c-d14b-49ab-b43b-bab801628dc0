package com.xt.hsk.module.edu.service.interactivecourse;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitResourceRelDO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import com.xt.hsk.module.edu.dal.mysql.interactivecourse.InteractiveCourseUnitMapper;
import com.xt.hsk.module.edu.dal.mysql.interactivecourse.InteractiveCourseUnitResourceRelMapper;
import com.xt.hsk.module.edu.dal.mysql.question.QuestionMapper;
import com.xt.hsk.module.edu.dal.mysql.questiontype.QuestionTypeMapper;
import com.xt.hsk.module.edu.enums.interactivecourse.UnitQuestionSourceTypeEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.UnitResourceTypeEnum;
import com.xt.hsk.module.game.api.SpecialExerciseApi;
import com.xt.hsk.module.game.api.dto.SpecialExercisePageRespDTO;
import com.xt.hsk.module.game.enums.question.GameQuestionTypeEnum;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 互动课单元 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class InteractiveCourseUnitServiceImpl extends
    ServiceImpl<InteractiveCourseUnitMapper, InteractiveCourseUnitDO> implements
    InteractiveCourseUnitService {
    
    @Resource
    private InteractiveCourseUnitMapper interactiveCourseUnitMapper;

    @Resource
    private InteractiveCourseUnitResourceRelMapper unitResourceRelMapper;

    @Resource
    private QuestionTypeMapper questionTypeMapper;

    @Resource
    private QuestionMapper questionMapper;

    @Resource
    private SpecialExerciseApi specialExerciseApi;

    @Override
    public PageResult<InteractiveCourseUnitDO> getInteractiveCourseUnitPage(
        InteractiveCourseUnitPageReqVO pageReqVO) {
        return interactiveCourseUnitMapper.selectPage(pageReqVO);
    }

    @Override
    public Long dataTotalCount(InteractiveCourseUnitPageReqVO reqVO) {
        if (reqVO == null) {
            return interactiveCourseUnitMapper.selectCount();
        }

        LambdaQueryWrapperX<InteractiveCourseUnitDO> query = new LambdaQueryWrapperX<InteractiveCourseUnitDO>();

        // 如果有选中的ID，优先使用ID列表查询，忽略其他条件
        if (CollUtil.isNotEmpty(reqVO.getIds())) {
            query.in(InteractiveCourseUnitDO::getId, reqVO.getIds());
        } else {
            // 没有选中ID时，使用其他查询条件
            query.eqIfPresent(InteractiveCourseUnitDO::getCourseId, reqVO.getCourseId())
                .likeIfPresent(InteractiveCourseUnitDO::getUnitNameCn, reqVO.getUnitNameCn())
                .eqIfPresent(InteractiveCourseUnitDO::getDisplayStatus, reqVO.getDisplayStatus())
                .eqIfPresent(InteractiveCourseUnitDO::getHskLevel, reqVO.getHskLevel())
                .eqIfPresent(InteractiveCourseUnitDO::getQuestionSource, reqVO.getQuestionSource());
        }

        return interactiveCourseUnitMapper.selectCount(query);
    }

    @Override
    public Long countByCourseId(Long courseId) {
        return lambdaQuery().eq(InteractiveCourseUnitDO::getCourseId, courseId).count();
    }

    @Override
    public Map<Long, Long> countByCourseIds(Collection<Long> courseIds) {
        if (CollUtil.isEmpty(courseIds)) {
            return new HashMap<>();
        }
        // 查询所有课程的单元数量
        List<Map<String, Object>> countList = interactiveCourseUnitMapper.selectCountByCourseIds(
            courseIds);

        // 转换为Map
        Map<Long, Long> result = new HashMap<>();
        for (Map<String, Object> map : countList) {
            Long courseId = ((Number) map.get("course_id")).longValue();
            Long count = ((Number) map.get("count")).longValue();
            result.put(courseId, count);
        }
        return result;
    }

    @Override
    public Integer getUnitDurationByCourseId(Long courseId) {
        List<InteractiveCourseUnitDO> list = lambdaQuery()
            .eq(InteractiveCourseUnitDO::getCourseId, courseId)
            .eq(InteractiveCourseUnitDO::getDisplayStatus, true)
            .select(InteractiveCourseUnitDO::getRecommendedDuration).list();
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().mapToInt(InteractiveCourseUnitDO::getRecommendedDuration).sum();
        }
        return 0;
    }

    @Override
    public Map<Long, String> getUnitContentType(List<Long> unitIdList) {
        if (CollUtil.isEmpty(unitIdList)) {
            return new HashMap<>();
        }
        List<InteractiveCourseUnitDO> courseUnitDOList = this.lambdaQuery()
            .select(InteractiveCourseUnitDO::getId, InteractiveCourseUnitDO::getQuestionSource)
            .in(InteractiveCourseUnitDO::getId, unitIdList)
            .list();
        // questionSource 题目来源 1-视频 2-专项练习 3-真题练习
        // 如果是 1 直接返回视频 如果是2 去关联表中取专项练习ID 根据枚举获取专项练习名称 @see GameQuestionTypeEnum
        // 如果是3 去关联表中取真题ID 根据类型ID获取名称 只要get0即可 题目只会有一个类型
        Map<Long, String> result = new HashMap<>();
        
        if (CollUtil.isEmpty(courseUnitDOList)) {
            return result;
        }
        
        // 分类处理不同来源的单元
        List<Long> specialExerciseUnitIds = new java.util.ArrayList<>();
        List<Long> realExamUnitIds = new java.util.ArrayList<>();
        
        // 先处理视频类型，并收集其他类型的单元ID
        for (InteractiveCourseUnitDO unit : courseUnitDOList) {
            if (UnitQuestionSourceTypeEnum.VIDEO.getCode().equals(unit.getQuestionSource())) {
                // 视频类型直接设置
                result.put(unit.getId(), "视频");
            } else if (UnitQuestionSourceTypeEnum.SPECIAL_PRACTICE.getCode().equals(unit.getQuestionSource())) {
                specialExerciseUnitIds.add(unit.getId());
            } else if (UnitQuestionSourceTypeEnum.QUESTION.getCode().equals(unit.getQuestionSource())) {
                realExamUnitIds.add(unit.getId());
            }
        }
        
        // 处理专项练习类型
        if (CollUtil.isNotEmpty(specialExerciseUnitIds)) {
            // 查询单元与专项练习的关联关系，资源类型 3-专项练习
            Map<Long, Long> unitToSpecialExerciseMap = unitResourceRelMapper.selectSpecialExerciseIdsByUnitIds(specialExerciseUnitIds);
            if (CollUtil.isNotEmpty(unitToSpecialExerciseMap)) {
                // 获取所有专项练习ID
                List<Long> specialExerciseIds = unitToSpecialExerciseMap.values().stream().toList();
                // 调用API获取专项练习信息
                Map<Long, Integer> specialExerciseTypeMap = specialExerciseApi.getQuestionInfoList(
                        specialExerciseIds).stream()
                    .collect(Collectors.toMap(SpecialExercisePageRespDTO::getId,
                        SpecialExercisePageRespDTO::getType));

                // 设置单元对应的专项练习名称
                for (Map.Entry<Long, Long> entry : unitToSpecialExerciseMap.entrySet()) {
                    Long unitId = entry.getKey();
                    Long specialExerciseId = entry.getValue();
                    result.put(unitId, GameQuestionTypeEnum.getDescByCode(
                        specialExerciseTypeMap.get(specialExerciseId)));
                }
            }
        }
        
        // 处理真题练习类型
        if (CollUtil.isNotEmpty(realExamUnitIds)) {
            // 查询单元与真题的关联关系，资源类型 4-真题练习
            Map<Long, Long> unitToQuestionMap = unitResourceRelMapper.selectQuestionTypeIdsByUnitIds(realExamUnitIds);
            if (CollUtil.isNotEmpty(unitToQuestionMap)) {
                // 获取所有问题
                List<QuestionDO> questions = questionMapper.selectList(new LambdaQueryWrapperX<QuestionDO>()
                    .in(QuestionDO::getId, unitToQuestionMap.values())
                    .eq(QuestionDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                    .select(QuestionDO::getId, QuestionDO::getTypeId));

                // 建立问题ID到类型ID的映射
                Map<Long, Long> questionToTypeMap = questions.stream()
                    .collect(Collectors.toMap(QuestionDO::getId, QuestionDO::getTypeId));

                // 获取所有问题类型ID
                Collection<Long> questionTypeIds = questionToTypeMap.values().stream().distinct().toList();

                // 查询问题类型名称
                Map<Long, String> questionTypeNames = questionTypeMapper.selectNamesByIds(questionTypeIds);

                // 设置单元对应的真题类型名称
                for (Map.Entry<Long, Long> entry : unitToQuestionMap.entrySet()) {
                    Long unitId = entry.getKey();
                    Long questionId = entry.getValue();
                    Long typeId = questionToTypeMap.get(questionId);
                    String name = questionTypeNames.get(typeId);
                    result.put(unitId, name);
                }
            }
        }

        return result;
    }

    @Override
    public Long getNextUnitId(Long courseId, Integer currentSort) {
        if (courseId == null || currentSort == null) {
            return null;
        }

        InteractiveCourseUnitDO nextUnit = lambdaQuery()
            .eq(InteractiveCourseUnitDO::getCourseId, courseId)
            .eq(InteractiveCourseUnitDO::getDisplayStatus, true)
            .gt(InteractiveCourseUnitDO::getSort, currentSort)
            .orderByAsc(InteractiveCourseUnitDO::getSort)
            .last("LIMIT 1")
            .one();

        return nextUnit != null ? nextUnit.getId() : null;
    }

    @Override
    public Long getUnitQuestionTypeId(Long unitId) {
        List<InteractiveCourseUnitResourceRelDO> relDOList = unitResourceRelMapper.selectListByUnitIdAndResourceType(
            unitId, UnitResourceTypeEnum.QUESTION.getCode());
        if (CollUtil.isEmpty(relDOList)) {
            return null;
        }
        Long questionId = relDOList.get(0).getResourceId();
        QuestionDO questionDO = questionMapper.selectOne(QuestionDO::getId, questionId);
        if (questionDO == null) {
            log.warn("unitId:{}单元对应的题目不存在，questionId:{}", unitId, questionId);
            return null;
        }
        return questionDO.getTypeId();
    }
}