package com.xt.hsk.module.edu.controller.app.exam.vo;

import com.xt.hsk.module.edu.enums.exam.ExamPublishStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模考 app resp vo
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
public class ExamAppRespVO {

    /**
     * 模考ID
     */
    private Long examId;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 模考组卷规则id
     */
    private Long paperRuleId;

    /**
     * 模考名称
     */
    private String name;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer type;

    /**
     * 模考封面图片URL
     */
    private String coverUrl;

    /**
     * 模考描述
     */
    private String description;

    /**
     * 听力考试时长 (秒)
     */
    private Integer listeningDuration;

    /**
     * 阅读考试时长 (秒)
     */
    private Integer readingDuration;

    /**
     * 书写考试时长 (秒)
     */
    private Integer writingDuration;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 已参加考试人数
     */
    private Integer examCount;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 发布状态：0-未发布 1-已发布 2-已下架
     *
     * @see ExamPublishStatusEnum
     */
    private Integer publishStatus;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}