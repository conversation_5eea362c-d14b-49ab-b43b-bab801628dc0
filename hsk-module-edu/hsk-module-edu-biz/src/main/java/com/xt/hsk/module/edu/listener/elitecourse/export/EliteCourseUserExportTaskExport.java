package com.xt.hsk.module.edu.listener.elitecourse.export;

import cn.hutool.core.collection.CollUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseUserRespVO;
import com.xt.hsk.module.edu.manager.elitecourse.EliteCourseRegisterManager;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 精品课学员导出任务导出
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Slf4j
@Component
public class EliteCourseUserExportTaskExport extends BaseEasyExcelExport<EliteCourseUserRespVO> {

    @Resource
    private EliteCourseRegisterManager eliteCourseRegisterManager;

    @Override
    protected List<List<String>> getExcelHead() {
        // 直接在这里定义表头，方便开发人员查看和维护
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("学员昵称"));
        head.add(Collections.singletonList("区号"));
        head.add(Collections.singletonList("手机号码"));
        head.add(Collections.singletonList("学习状态"));
        head.add(Collections.singletonList("报名途径"));
        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        // 使用配置好的 ObjectMapper 将 Map 转换为 EliteCourseStudyPageReqVO 对象，忽略taskName字段
        EliteCourseStudyPageReqVO reqVO = objectMapper.convertValue(conditions, EliteCourseStudyPageReqVO.class);

        // 如果有选中的ID，返回选中的数量
        if (CollUtil.isNotEmpty(reqVO.getIds())) {
            return (long) reqVO.getIds().size();
        }

        return eliteCourseRegisterManager.countCourseUser(reqVO);
    }

    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition, Long pageNo, Long pageSize) {

        EliteCourseStudyPageReqVO pageReqVO = objectMapper.convertValue(queryCondition, EliteCourseStudyPageReqVO.class);
        pageReqVO.setPageNo(Math.toIntExact(pageNo));
        pageReqVO.setPageSize(Math.toIntExact(pageSize));

        PageResult<EliteCourseUserRespVO> userPage = eliteCourseRegisterManager.getCourseUserList(pageReqVO);
        List<EliteCourseUserRespVO> voList = userPage.getList();
        if (CollUtil.isNotEmpty(voList)) {

            for (EliteCourseUserRespVO vo : voList) {
                List<String> row = new ArrayList<>();
                row.add(vo.getNickname() == null ? "-" : vo.getNickname());
                row.add(vo.getCountryCode() == null ? "-" : vo.getCountryCode());
                row.add(vo.getMobile() == null ? "-" : vo.getMobile());
                row.add(vo.getLearningStatusStr() == null ? "-" : vo.getLearningStatusStr());
                row.add(vo.getRegisterTypeStr() == null ? "-" : vo.getRegisterTypeStr());

                resultList.add(row);
            }
        }
        log.info("精品课学员导出当前页：{}每页条数：{}总条数：{}", pageNo, pageSize,
                userPage.getTotal());

    }

    @Override
    protected Long eachSheetTotalCount() {
        return 5000L;
    }

    @Override
    protected Long eachTimesWriteSheetTotalCount() {
        return 1000L;
    }
} 