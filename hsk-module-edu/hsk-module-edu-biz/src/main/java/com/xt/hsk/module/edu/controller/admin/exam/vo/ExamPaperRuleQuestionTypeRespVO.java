package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import lombok.Data;

import java.util.List;

/**
 * 模考组卷规则 resp vo
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
public class ExamPaperRuleQuestionTypeRespVO {

    /**
     * 组卷规则明细ID
     */
    private Long id;

    /**
     * 组卷规则ID
     */
    private Long paperRuleId;

    /**
     * 模考题型ID
     */
    private Long examQuestionTypeId;

    /**
     * 题型id列表
     */
    private String questionTypeIds;

    /**
     * 题型ID名称列表
     */
    private List<String> questionTypeNameList;

    /**
     * 科目
     *
     * @see SubjectEnum
     */
    private Integer subject;

    /**
     * 单元部分
     *
     * @see ExamQuestionTypeUnitEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = ExamQuestionTypeUnitEnum.class, ref = "unitDesc")
    private Integer unit;

    /**
     * 单元部分描述
     */
    private String unitDesc;

    /**
     * 题数
     */
    private Integer questionCount;

}