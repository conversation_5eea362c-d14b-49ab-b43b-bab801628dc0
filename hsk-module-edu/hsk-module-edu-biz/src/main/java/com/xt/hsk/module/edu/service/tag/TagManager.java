package com.xt.hsk.module.edu.service.tag;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.IMPORT_DOWNLOAD_TEMPLATE_ERROR;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.TAG_NAME_DUPLICATE;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import cn.idev.excel.EasyExcel;
import cn.idev.excel.exception.ExcelAnalysisException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.framework.common.exception.ServerException;
import com.xt.hsk.framework.common.pojo.ImportResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.tag.vo.TagPageReqVO;
import com.xt.hsk.module.edu.controller.admin.tag.vo.TagSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordTagRespVO;
import com.xt.hsk.module.edu.dal.dataobject.tag.TagDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordTagDO;
import com.xt.hsk.module.edu.listener.importTask.TagImportListener;
import com.xt.hsk.module.edu.service.word.WordTagService;
import com.xt.hsk.module.infra.listener.CompositeRowLimitListener;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;


/**
 * 标签 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TagManager {

    @Resource
    private TagService tagService;
    @Resource
    private WordTagService wordTagService;


    public Long createTag(TagSaveReqVO createReqVO) {
        // 校验名字是否存在
        LambdaQueryWrapper<TagDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagDO::getTagName, createReqVO.getTagName());
        long count = tagService.count(queryWrapper);
        if (count > 0) {
            throw exception(TAG_NAME_DUPLICATE);
        }
        // 插入
        TagDO tag = BeanUtils.toBean(createReqVO, TagDO.class);
        tag.setStatus(CommonStatusEnum.ENABLE.getStatus());
        tagService.save(tag);

        // 设置日志上下文变量
        LogRecordContext.putVariable("tagId", tag.getId());
        LogRecordContext.putVariable("tag", tag);

        // 返回
        return tag.getId();
    }


    public void updateTag(TagSaveReqVO updateReqVO) {
        // 校验存在
        validateTagExists(updateReqVO.getId());
        // 校验名字是否存在
        LambdaQueryWrapper<TagDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagDO::getTagName, updateReqVO.getTagName());
        queryWrapper.ne(TagDO::getId, updateReqVO.getId());
        long count = tagService.count(queryWrapper);
        if (count > 0) {
            throw exception(TAG_NAME_DUPLICATE);
        }
        // 更新
        TagDO updateObj = BeanUtils.toBean(updateReqVO, TagDO.class);
        tagService.updateById(updateObj);

        // 设置日志上下文变量
        LogRecordContext.putVariable("tag", updateObj);
    }


    public void deleteTag(Long id) {
        // 校验存在
        validateTagExists(id);
        // 获取标签信息
        TagDO tag = tagService.getById(id);

        // 设置日志上下文变量
        LogRecordContext.putVariable("tag", tag);

        // 删除
        tagService.removeById(id);
    }

    private void validateTagExists(Long id) {
        if (tagService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public TagDO getTag(Long id) {
        return tagService.getById(id);
    }

    public PageResult<TagDO> getTagPage(@Valid TagPageReqVO pageReqVO) {
        return tagService.selectPage(pageReqVO);
    }

    public PageResult<WordTagRespVO> getWordTagPage(TagPageReqVO pageReqVO) {
        PageResult<WordTagDO> wordTagDos = wordTagService.getWordTagPageByTagIds(pageReqVO);
        return BeanUtils.toBean(wordTagDos, WordTagRespVO.class);
    }

    public void deleteTagBatch(List<Long> ids) {
        // 设置日志上下文变量
        LogRecordContext.putVariable("deleteCount", ids.size());
        LogRecordContext.putVariable("batchBizNo", ids.get(0).toString());
        LogRecordContext.putVariable("ids", ids);

        tagService.removeByIds(ids);
    }

    public List<TagDO> list() {
        LambdaQueryWrapper<TagDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        return tagService.list(queryWrapper);
    }

    public void downloadImportTemplate(HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("批量导入标签模板", StandardCharsets.UTF_8);
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            // 获取模板文件路径
            ClassPathResource resource = new ClassPathResource("excelTemplate/批量导入标签模板.xlsx");
            InputStream inputStream = resource.getInputStream();

            // 写入响应流
            FileCopyUtils.copy(inputStream, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载导入标签模板失败", e);
            throw new ServerException(IMPORT_DOWNLOAD_TEMPLATE_ERROR);
        }
    }

    public ImportResult importTag(MultipartFile file) {
        ImportResult result = new ImportResult();
        try {
//            // 创建数据处理监听器
//            ExamImportListener dataListener = new ExamImportListener(examService, examCenterService, areaService);
            TagImportListener dataListener = new TagImportListener(tagService);
//
            // 创建组合监听器，限制最多导入300个考试场次
            CompositeRowLimitListener<TagDO> compositeListener =
                    new CompositeRowLimitListener<>(300, dataListener, "一次最多导入300个标签，请分批导入");
//
            // 执行导入，设置headRowNumber=2表示从第三行开始读取数据
            EasyExcel.read(file.getInputStream(), TagDO.class, compositeListener)
                    // 设置表头占用2行，数据从第3行开始
                    .headRowNumber(2)
                    .sheet()
                    .doRead();

            result.setSuccess(dataListener.SUCCESS);
            result.setMsg(dataListener.msg);
            result.setInvalidCount(dataListener.INVALID_COUNT);
            result.setValidCount(dataListener.VALID_COUNT);


        } catch (ExcelAnalysisException e) {
            log.warn("导入标签受限: {}", e.getMessage());
            result.setSuccess(false);
            result.setMsg(Collections.singletonList(e.getMessage()));
            result.setInvalidCount(0);
            result.setValidCount(0);
        } catch (Exception e) {
            log.error("导入标签失败: {}", e.getMessage());
            result.setSuccess(false);
            result.setMsg(Collections.singletonList("导入失败：" + e.getMessage()));
            result.setInvalidCount(0);
            result.setValidCount(0);
        }

        // 设置日志上下文变量
        LogRecordContext.putVariable("fileName", file.getOriginalFilename());
        LogRecordContext.putVariable("importResult", result);

        return result;
    }
}