package com.xt.hsk.module.edu.service.sourceQuestion;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.sourceQuestion.SourceQuestionDO;
import com.xt.hsk.module.edu.dal.mysql.question.QuestionMapper;
import com.xt.hsk.module.edu.dal.mysql.sourceQuestion.SourceQuestionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;



/**
 * 题目 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SourceQuestionServiceImpl extends ServiceImpl<SourceQuestionMapper, SourceQuestionDO> implements SourceQuestionService {

    @Resource
    private SourceQuestionMapper sourceQuestionMapper;

    @Override
    public PageResult<SourceQuestionDO> selectPage(QuestionPageReqVO pageReqVO) {
       return sourceQuestionMapper.selectPage(pageReqVO);
    }

}