package com.xt.hsk.module.edu.listener.elitecourse.export;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRegisterUserPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyStatsRespVO;
import com.xt.hsk.module.edu.manager.elitecourse.EliteCourseStudyStatsManager;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 精品课课时学习统计导出任务导出
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Slf4j
@Component
public class EliteClassHourStudyStatsExportTaskExport extends
    BaseEasyExcelExport<EliteCourseStudyStatsRespVO> {

    @Resource
    private EliteCourseStudyStatsManager eliteCourseStudyStatsManager;

    @Override
    protected List<List<String>> getExcelHead() {
        // 直接在这里定义表头，方便开发人员查看和维护
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("学员昵称"));
        head.add(Collections.singletonList("区号"));
        head.add(Collections.singletonList("手机号码"));
        head.add(Collections.singletonList("学习次数"));
        head.add(Collections.singletonList("学习进度"));
        head.add(Collections.singletonList("学习时长"));
        head.add(Collections.singletonList("开始学习时间"));
        head.add(Collections.singletonList("最后学习时间"));
        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        // 使用配置好的 ObjectMapper 将 Map 转换为 EliteCourseRegisterUserPageReqVO 对象，忽略taskName字段
        EliteCourseRegisterUserPageReqVO reqVO = objectMapper.convertValue(conditions, EliteCourseRegisterUserPageReqVO.class);

        // 如果有选中的ID，返回选中的数量
        if (CollUtil.isNotEmpty(reqVO.getIds())) {
            return (long) reqVO.getIds().size();
        }

        return eliteCourseStudyStatsManager.countClassHourStudyStats(reqVO);
    }

    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition, Long pageNo, Long pageSize) {

        EliteCourseRegisterUserPageReqVO pageReqVO = objectMapper.convertValue(queryCondition, EliteCourseRegisterUserPageReqVO.class);
        pageReqVO.setPageNo(Math.toIntExact(pageNo));
        pageReqVO.setPageSize(Math.toIntExact(pageSize));

        PageResult<EliteCourseStudyStatsRespVO> statsPage = eliteCourseStudyStatsManager.getClassHourStudyStatsPage(pageReqVO);
        List<EliteCourseStudyStatsRespVO> voList = statsPage.getList();
        if (CollUtil.isNotEmpty(voList)) {

            for (EliteCourseStudyStatsRespVO vo : voList) {
                List<String> row = new ArrayList<>();
                row.add(vo.getNickname() == null ? "-" : vo.getNickname());
                row.add(vo.getCountryCode() == null ? "-" : vo.getCountryCode());
                row.add(vo.getMobile() == null ? "-" : vo.getMobile());
                row.add(vo.getStudyCount() == null ? "-" : vo.getStudyCount().toString());
                row.add(vo.getStudyProgressStr() == null ? "0" : vo.getStudyProgressStr());
                row.add(vo.getStudyLengthStr() == null ? "-" : vo.getStudyLengthStr());
                row.add(vo.getFirstStudyTime() == null ? "-" : DateUtil.format(vo.getFirstStudyTime(), DatePattern.NORM_DATETIME_PATTERN));
                row.add(vo.getLastStudyTime() == null ? "-" : DateUtil.format(vo.getLastStudyTime(), DatePattern.NORM_DATETIME_PATTERN));

                resultList.add(row);
            }
        }
        log.info("精品课课时学习统计导出当前页：{}每页条数：{}总条数：{}", pageNo, pageSize,
                statsPage.getTotal());

    }

    @Override
    protected Long eachSheetTotalCount() {
        return 5000L;
    }

    @Override
    protected Long eachTimesWriteSheetTotalCount() {
        return 1000L;
    }
} 