package com.xt.hsk.module.edu.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.listener.BaseExportTask;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 讲师导出任务
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Component("teacherExportTask")
@Slf4j
public class TeacherExportTask extends BaseExportTask<TeacherExportTaskExport> {

    @Resource
    private TeacherExportTaskExport teacherExportTaskExport;

    @Override
    protected TeacherExportTaskExport getExporter() {
        return teacherExportTaskExport;
    }

    @Override
    protected String getFileName() {
        return "讲师数据";
    }

    @Override
    protected Map<String, Object> buildQueryParams(String params) {
        log.info("=== TeacherExportTask 开始解析参数 === params: {}", params);

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            ExportTaskParams taskParams = objectMapper.readValue(params, ExportTaskParams.class);

            log.info("解析ExportTaskParams成功: {}", taskParams);

            // 返回查询参数，任务名称已经在创建任务时使用，这里不需要处理
            Map<String, Object> result = objectMapper.convertValue(taskParams.getQueryParams(),
                new TypeReference<Map<String, Object>>() {
                });

            log.info("=== TeacherExportTask 参数解析完成 === result: {}", result);

            return result;
        } catch (Exception e) {
            log.error("=== TeacherExportTask 解析导出参数失败 === params: {}, 错误: {}", params,
                e.getMessage(), e);
            return Map.of();
        }
    }
} 