package com.xt.hsk.module.edu.service.exam;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.api.exam.UserExamRecordApi;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageRespVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.ExamAppPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.UserExamRecordDO;
import com.xt.hsk.module.edu.dal.mysql.exam.UserExamRecordMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 用户模考记录 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Service
@Validated
public class UserExamRecordServiceImpl extends ServiceImpl<UserExamRecordMapper, UserExamRecordDO> implements UserExamRecordService, UserExamRecordApi {

    @Resource
    private UserExamRecordMapper userExamRecordMapper;

    /**
     * 分页获取用户模考记录
     */
    @Override
    public PageResult<UserExamRecordPageRespVO> getUserExamRecordPage(UserExamRecordPageReqVO pageReqVO) {

        IPage<UserExamRecordPageRespVO> page = userExamRecordMapper.getUserExamRecordPage(new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize()), pageReqVO);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 用户模考记录总数
     */
    @Override
    public Long countUserExamRecordPage(UserExamRecordPageReqVO pageReqVO) {
        return userExamRecordMapper.countUserExamRecordPage(pageReqVO);
    }

    @Override
    public PageResult<UserExamRecordDO> getMyExamRecordPage(ExamAppPageReqVO reqVO) {
        IPage<UserExamRecordDO> page = userExamRecordMapper.getMyExamRecordPage(new Page<>(reqVO.getPageNo(), reqVO.getPageSize()), reqVO);
        if (page == null || page.getRecords() == null || page.getRecords().isEmpty()) {
            return new PageResult<>(0L);
        }
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public Long getUserExamRecordCount(Long userId) {
        return userExamRecordMapper.getUserExamRecordCount(userId);
    }
}