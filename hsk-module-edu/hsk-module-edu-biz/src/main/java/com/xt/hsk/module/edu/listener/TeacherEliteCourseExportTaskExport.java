package com.xt.hsk.module.edu.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.translate.core.TranslateUtils;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRespVO;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseTypeEnum;
import com.xt.hsk.module.edu.manager.elitecourse.EliteCourseManager;
import com.xt.hsk.module.edu.service.elitecourse.EliteCourseService;
import com.xt.hsk.module.infra.listener.BaseEasyExcelExport;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 讲师精品课程导出任务导出
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Component
public class TeacherEliteCourseExportTaskExport extends BaseEasyExcelExport<EliteCourseRespVO> {

    @Resource
    private EliteCourseService eliteCourseService;

    @Resource
    private EliteCourseManager eliteCourseManager;

    /**
     * 讲师id
     */
    private Long teacherId;

    @Override
    protected List<List<String>> getExcelHead() {
        // 直接在这里定义表头，方便开发人员查看和维护
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("课程ID"));
        head.add(Collections.singletonList("课程名称"));
        head.add(Collections.singletonList("课程封面大图"));
        head.add(Collections.singletonList("课程封面小图"));
        head.add(Collections.singletonList("价格"));
        head.add(Collections.singletonList("课程类型"));
        head.add(Collections.singletonList("所属等级"));
        head.add(Collections.singletonList("所属分类"));
        head.add(Collections.singletonList("在读人数"));
        head.add(Collections.singletonList("学习有效期"));
        head.add(Collections.singletonList("上架状态"));
        head.add(Collections.singletonList("展示状态"));
        head.add(Collections.singletonList("最近更新人"));
        head.add(Collections.singletonList("创建时间"));
        head.add(Collections.singletonList("最近更新时间"));
        return head;
    }

    @Override
    protected Long dataTotalCount(Map<String, Object> conditions) {
        // 使用配置好的 ObjectMapper 将 Map 转换为 InteractiveCoursePageReqVO 对象，忽略taskName字段
        EliteCoursePageReqVO dto = objectMapper.convertValue(conditions, EliteCoursePageReqVO.class);

        // 如果有选中的ID，返回选中的数量
        if (CollUtil.isNotEmpty(dto.getIds())) {
            return (long) dto.getIds().size();
        }

        return eliteCourseService.dataTotalCount(dto);
    }

    @Override
    protected void buildDataList(List<List<String>> resultList, Map<String, Object> queryCondition, Long pageNo, Long pageSize) {

        EliteCoursePageReqVO pageReqVO = objectMapper.convertValue(queryCondition, EliteCoursePageReqVO.class);
        pageReqVO.setPageNo(Math.toIntExact(pageNo));
        pageReqVO.setPageSize(Math.toIntExact(pageSize));

        if (this.teacherId == null) {
            this.teacherId = pageReqVO.getTeacherId();
        }

        PageResult<EliteCourseRespVO> wordPage = eliteCourseManager.getEliteCoursePage(pageReqVO);
        List<EliteCourseRespVO> voList = wordPage.getList();
        if (CollUtil.isNotEmpty(voList)) {

            // 手动触发翻译
            if (CollUtil.isNotEmpty(voList)) {
                voList = TranslateUtils.translate(voList);
            }

            for (EliteCourseRespVO vo : voList) {
                List<String> row = new ArrayList<>();
                row.add(vo.getId().toString());
                row.add(vo.getCourseNameCn());
                row.add(vo.getCoverUrlLarge());
                row.add(vo.getCoverUrlSmall());
                row.add(vo.getSellingPriceCn() == null ? "-" : vo.getSellingPriceCn().toString());
                row.add(EliteCourseTypeEnum.getDescByCode(vo.getType()));
                row.add(vo.getHskLevel() == null ? "" : HskEnum.getDescByCode(vo.getHskLevel()));
                row.add(vo.getCategoryNameCn());
                row.add(vo.getReadingCount() == null ? "0" : vo.getReadingCount().toString());
                row.add(vo.getLearningValidityPeriodStr());
                row.add(EliteCourseListingStatusEnum.getDescByCode(vo.getListingStatus()));
                row.add(IsEnum.YES.getCode().equals(vo.getIsShow()) ? "展示" : "隐藏");
                row.add(vo.getUpdaterName());
                row.add(vo.getCreateTime() == null ? "-" : DateUtil.format(vo.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                row.add(vo.getUpdateTime() == null ? "-" : DateUtil.format(vo.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));

                resultList.add(row);
            }
        }
        log.info("精品课导出当前页：{}每页条数：{}总条数：{}", pageNo, pageSize,
                wordPage.getTotal());

    }

    @Override
    protected Long eachSheetTotalCount() {
        return 5000L;
    }

    @Override
    protected Long eachTimesWriteSheetTotalCount() {
        return 1000L;
    }
}
