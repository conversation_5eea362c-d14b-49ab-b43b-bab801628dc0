package com.xt.hsk.module.edu.service.sourceword;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordMeaningPageReqVO;
import com.xt.hsk.module.edu.controller.app.word.vo.AppWordSearchVo;
import com.xt.hsk.module.edu.dal.dataobject.sourceword.SourceWordMeaningDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 词语多释义表（冗余word） Service 接口
 *
 * <AUTHOR>
 */
public interface SourceWordMeaningService extends IService<SourceWordMeaningDO> {
    PageResult<SourceWordMeaningDO> selectPage(@Valid WordMeaningPageReqVO pageReqVO);

    void removeByWordId(Long wordId);

    List<SourceWordMeaningDO> getByWordId(Long id);
    /**
     * 根据词汇ID列表批量获取含义
     *
     * @param wordIds 词汇ID列表
     * @return 含义列表
     */
    List<SourceWordMeaningDO> getByWordIds(List<Long> wordIds);

    List<SourceWordMeaningDO> getByWord(String word);
}