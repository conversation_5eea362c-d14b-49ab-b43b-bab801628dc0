package com.xt.hsk.module.edu.controller.app.exam.vo;

import lombok.Data;

/**
 * 模考答题卡作答数据VO
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
public class ExamAnswerCardDataVO {

    /**
     * 记录id
     */
    private Long recordId;
    /**
     * 科目 1听力 2阅读 4写作
     */
    private Integer subject;
    /**
     * 单元部分
     */
    private Integer unitSort;
    /**
     * 题目ID
     */
    private Long questionId;
    /**
     * 题目详情id
     */
    private Long questionDetailId;
    /**
     * 是否正确 0-错误 1-正确
     */
    private Boolean isCorrect;
    /**
     * ai 批改状态 0-未批改 1-已批改
     */
    private Integer aiCorrectStatus;

}