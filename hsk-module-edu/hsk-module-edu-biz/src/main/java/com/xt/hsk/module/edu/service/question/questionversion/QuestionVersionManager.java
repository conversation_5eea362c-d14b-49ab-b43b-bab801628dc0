package com.xt.hsk.module.edu.service.question.questionversion;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.DATA_NOT_EXIST;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.question.questionversion.vo.QuestionVersionPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.questionversion.vo.QuestionVersionSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questionversion.QuestionVersionDO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 题目表版本库 Manager
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionVersionManager {

    @Resource
    private QuestionVersionService questionVersionService;


    public Long createQuestionVersion(QuestionVersionSaveReqVO createReqVO) {
        // 插入
        QuestionVersionDO questionVersion = BeanUtils.toBean(createReqVO, QuestionVersionDO.class);
        questionVersionService.save(questionVersion);

        // 返回
        return questionVersion.getId();
    }


    public void updateQuestionVersion(QuestionVersionSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionVersionExists(updateReqVO.getId());
        // 更新
        QuestionVersionDO updateObj = BeanUtils.toBean(updateReqVO, QuestionVersionDO.class);
        questionVersionService.updateById(updateObj);
    }


    public void deleteQuestionVersion(Long id) {
        // 校验存在
        validateQuestionVersionExists(id);
        // 删除
        questionVersionService.removeById(id);
    }

    private void validateQuestionVersionExists(Long id) {
        if (questionVersionService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }


    public QuestionVersionDO getQuestionVersion(Long id) {
        return questionVersionService.getById(id);
    }

    public PageResult<QuestionVersionDO> getQuestionVersionPage(@Valid QuestionVersionPageReqVO pageReqVO) {
        return questionVersionService.selectPage(pageReqVO);
    }



}