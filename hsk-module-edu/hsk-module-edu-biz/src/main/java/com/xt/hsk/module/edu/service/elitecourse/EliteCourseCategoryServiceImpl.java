package com.xt.hsk.module.edu.service.elitecourse;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xt.hsk.framework.common.enums.IsEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseCategoryPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseCategoryDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteCourseCategoryMapper;
import com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteCourseMapper;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 精品课-分类 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
@Validated
public class EliteCourseCategoryServiceImpl extends ServiceImpl<EliteCourseCategoryMapper, EliteCourseCategoryDO> implements EliteCourseCategoryService {

    @Resource
    private EliteCourseCategoryMapper eliteCourseCategoryMapper;

    @Resource
    private EliteCourseMapper eliteCourseMapper;

    @Override
    public PageResult<EliteCourseCategoryDO> selectPage(EliteCourseCategoryPageReqVO pageReqVO) {
        return eliteCourseCategoryMapper.selectPage(pageReqVO);
    }

    @Override
    public List<EliteCourseCategoryDO> getShowCategories() {
        // 1. 查询所有已上架且展示的课程
        List<EliteCourseDO> courses = eliteCourseMapper.selectList(
            new LambdaQueryWrapper<EliteCourseDO>()
                // 已上架
                .eq(EliteCourseDO::getListingStatus, 1)
                // 展示
                .eq(EliteCourseDO::getIsShow, IsEnum.YES.getCode()));

        // 2. 如果没有课程，则返回空列表
        if (courses.isEmpty()) {
            return Collections.emptyList();
        }

        // 3. 获取所有分类ID
        Set<Long> categoryIds = new HashSet<>();
        for (EliteCourseDO course : courses) {
            if (course.getPrimaryCategoryId() != null) {
                categoryIds.add(course.getPrimaryCategoryId());
            }
        }

        // 4. 如果没有分类，则返回空列表
        if (categoryIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 5. 查询所有分类
        return eliteCourseCategoryMapper.selectList(
            new LambdaQueryWrapper<EliteCourseCategoryDO>()
                .in(EliteCourseCategoryDO::getId, categoryIds)
                .orderByAsc(EliteCourseCategoryDO::getSort));
    }

    /**
     * 获取展示的精品课分类列表 仅返回存在上架且显示的精品课的分类 支持按HSK等级筛选 展示ALL分类逻辑 -> 1.没有任何上架展示课程时，分类列表为空 2.有课程 没有挂任何分类时
     * 拼一个ALL 3.有课程 有挂分类时 拼一个ALL
     *
     * @param hskLevel HSK等级，如果为null则返回所有分类
     * @return 精品课分类列表
     */
    @Override
    public List<EliteCourseCategoryDO> getShowCategories(Integer hskLevel) {
        // 如果未指定HSK等级，则返回所有分类
        if (hskLevel == null) {
            List<EliteCourseCategoryDO> result = new ArrayList<>();
            result.add(buildAllCategory());
            result.addAll(getShowCategories());
            return result;
        }

        // 1. 查询所有已上架且展示的课程，按HSK等级筛选
        List<EliteCourseDO> courses = eliteCourseMapper.selectList(
            new LambdaQueryWrapper<EliteCourseDO>()
                // 已上架
                .eq(EliteCourseDO::getListingStatus, 1)
                // 展示
                .eq(EliteCourseDO::getIsShow, IsEnum.YES.getCode())
                // 按HSK等级筛选
                .eq(EliteCourseDO::getHskLevel, hskLevel));

        // 2. 如果没有课程，则返回空列表
        if (CollUtil.isEmpty(courses)) {
            return Collections.emptyList();
        }

        // 3. 获取所有分类ID
        Set<Long> categoryIds = new HashSet<>();
        for (EliteCourseDO course : courses) {
            if (course.getPrimaryCategoryId() != null) {
                categoryIds.add(course.getPrimaryCategoryId());
            }
        }

        // 4. 如果没有分类，则拼一个ALL
        if (categoryIds.isEmpty()) {
            return Collections.singletonList(buildAllCategory());
        }

        // 5. 查询所有分类 并且拼ALL
        List<EliteCourseCategoryDO> categoryDOList = eliteCourseCategoryMapper.selectList(
            new LambdaQueryWrapper<EliteCourseCategoryDO>()
                .in(EliteCourseCategoryDO::getId, categoryIds)
                .orderByAsc(EliteCourseCategoryDO::getSort));
        categoryDOList.add(0, buildAllCategory());
        return categoryDOList;
    }

    private EliteCourseCategoryDO buildAllCategory() {
        return EliteCourseCategoryDO.builder()
            .nameCn("全部")
            .nameEn("All")
            .nameOt("Toàn bộ")
            // 只要是ALL 排序永远在最前面
            .sort(0)
            .build();
    }

}