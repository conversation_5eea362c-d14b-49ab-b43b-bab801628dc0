package com.xt.hsk.module.edu.service.userquestionanswerrecord.userquestionanswerdata;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerdata.UserQuestionAnswerDataDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import jakarta.validation.Valid;
import com.xt.hsk.module.edu.controller.admin.userquestionanswerdata.vo.UserQuestionAnswerDataPageReqVO;

/**
 * 用户题目作答数据 Service 接口
 *
 * <AUTHOR>
 */
public interface UserQuestionAnswerDataService extends IService<UserQuestionAnswerDataDO> {
    PageResult<UserQuestionAnswerDataDO> selectPage(@Valid UserQuestionAnswerDataPageReqVO pageReqVO);

}