package com.xt.hsk.module.edu.service.question.questionversion;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.question.questionversion.vo.QuestionVersionPageReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questionversion.QuestionVersionDO;
import jakarta.validation.Valid;

/**
 * 题目表版本库 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionVersionService extends IService<QuestionVersionDO> {
    PageResult<QuestionVersionDO> selectPage(@Valid QuestionVersionPageReqVO pageReqVO);

    QuestionVersionDO getQuestionByIdAndVersion(Long questionId, Integer version);
}