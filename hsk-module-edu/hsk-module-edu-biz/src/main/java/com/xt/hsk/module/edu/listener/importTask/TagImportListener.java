package com.xt.hsk.module.edu.listener.importTask;

import cn.hutool.core.collection.CollUtil;
import cn.idev.excel.context.AnalysisContext;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.module.edu.dal.dataobject.tag.TagDO;
import com.xt.hsk.module.edu.service.tag.TagService;
import com.xt.hsk.module.infra.listener.BaseAnalysisEventListener;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TagImportListener extends BaseAnalysisEventListener<TagDO> {

    private final List<TagDO> tagDOS = new ArrayList<>();
    private final TagService tagService;
    /**
     * 标记是否有格式错误（非重复错误）
     */
    private boolean hasFormatError = false;
    /**
     * 标记是否有重复数据错误
     */
    private boolean hasDuplicateError = false;
    /**
     * 校验是否重复
     */
    private Set<String> tagName = new HashSet<>();
    /**
     * 校验是否重复
     */
    private Set<String> dBtagName = new HashSet<>();

    public TagImportListener(TagService tagService) {
        this.tagService = tagService;
        // 获取数据库中的标签名称
        dBtagName.addAll(tagService.getAllTagName());
    }

    @Override
    public void invoke(TagDO data, AnalysisContext context) {
        // 行号(从1开始，去掉表头就是2)
        int rowIndex = context.readRowHolder().getRowIndex() + 1;

        try {
            // 校验并处理数据
            TagDO tag = validateTag(data, rowIndex);

            if (tag != null) {
                tag.setStatus(CommonStatusEnum.ENABLE.getStatus());
                tagDOS.add(tag);
                tagName.add(tag.getTagName());
                VALID_COUNT++;
            } else {
                SUCCESS = false;
                INVALID_COUNT++;
            }
        } catch (Exception e) {
            // 记录未预期的异常
            log.error("处理第{}行数据时发生异常", rowIndex, e);
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, e.getMessage()));
            INVALID_COUNT++;
            SUCCESS = false;
            hasFormatError = true;
        }
    }

    private TagDO validateTag(TagDO data, int rowIndex) {
        if (data == null) {
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, "数据为空"));
            return null;
        }
        if (data.getTagName() == null || data.getTagName().trim().isEmpty()) {
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, "标签名称不能为空"));
            return null;
        }
        if (data.getTagName().length() > 10) {
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, "标签名称不能超过10个字符"));
            return null;
        }
        // 不可输入标点和其他语言
        if (!data.getTagName().matches("^[\\p{Script=Han}]+$")) {
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, "标签名称只能输入中文,不能输入标点和其他语言"));
            return null;
        }
        //备注信息最多输入100个中文字符
        if (data.getRemark() != null && data.getRemark().length() > 100) {
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, "备注信息最多输入100个字符"));
            return null;
        }
        if (dBtagName.contains(data.getTagName())) {
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, "标签名称已存在"));
            return null;
        }
        if (tagName.contains(data.getTagName())) {
            msg.add(String.format("第%d行：处理失败，原因：%s", rowIndex, "标签名称重复了"));
            return null;
        }
        return data;
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 如果只有重复数据错误，导入不重复的数据
        if (CollUtil.isNotEmpty(tagDOS)) {
            log.info("开始批量保存{}条标签数据", tagDOS.size());
            tagService.saveBatch(tagDOS);
            log.info("批量保存标签数据完成");
        } else {
            log.info("没有有效的标签数据需要保存");
        }
    }
}
