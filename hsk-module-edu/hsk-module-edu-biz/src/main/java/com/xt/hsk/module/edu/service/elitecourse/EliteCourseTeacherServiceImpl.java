package com.xt.hsk.module.edu.service.elitecourse;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseTeacherPageReqVO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherBasicInfoRespVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseTeacherDO;
import com.xt.hsk.module.edu.dal.dataobject.teacher.TeacherDO;
import com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteCourseTeacherMapper;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;


/**
 * 精品课程讲师关联 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Service
@Validated
public class EliteCourseTeacherServiceImpl extends ServiceImpl<EliteCourseTeacherMapper, EliteCourseTeacherDO> implements EliteCourseTeacherService {

    private static final Logger log = LoggerFactory.getLogger(EliteCourseTeacherServiceImpl.class);

    @Resource
    private EliteCourseTeacherMapper eliteCourseTeacherMapper;

    @Resource
    private EliteCourseService eliteCourseService;

    @Override
    public PageResult<EliteCourseTeacherDO> selectPage(EliteCourseTeacherPageReqVO pageReqVO) {
        return eliteCourseTeacherMapper.selectPage(pageReqVO);
    }

    @Override
    public Map<Long, Integer> countCourseByTeacherIds(List<Long> teacherIds) {
        // 如果讲师ID列表为空，直接返回空映射
        if (CollUtil.isEmpty(teacherIds)) {
            return Collections.emptyMap();
        }

        // 查询指定讲师关联的课程记录
        List<EliteCourseTeacherDO> courseTeachers = lambdaQuery()
            .in(EliteCourseTeacherDO::getTeacherId, teacherIds)
            .list();

        if (CollUtil.isEmpty(courseTeachers)) {
            return Collections.emptyMap();
        }

        // 统计每个讲师关联的课程数量，使用Java Stream分组计数
        return courseTeachers.stream()
            .collect(Collectors.groupingBy(
                EliteCourseTeacherDO::getTeacherId,
                Collectors.collectingAndThen(Collectors.counting(), Long::intValue)
            ));
    }

    @Override
    public Map<Long, Integer> countNotDeletedCourseByTeacherIds(List<Long> teacherIds) {
        // 如果讲师ID列表为空，直接返回空映射
        if (CollUtil.isEmpty(teacherIds)) {
            return Collections.emptyMap();
        }

        // 初始化所有讲师ID的映射为0
        Map<Long, Integer> teacherCourseCountMap = new java.util.HashMap<>(teacherIds.size());
        for (Long teacherId : teacherIds) {
            teacherCourseCountMap.put(teacherId, 0);
        }

        try {
            // 获取所有课程ID和讲师ID的映射关系
            List<EliteCourseTeacherDO> allCourseTeachers = lambdaQuery()
                .in(EliteCourseTeacherDO::getTeacherId, teacherIds)
                .list();

            if (CollUtil.isEmpty(allCourseTeachers)) {
                return teacherCourseCountMap; // 没有任何关联课程，直接返回
            }

            // 提取所有关联的课程ID
            List<Long> allCourseIds = allCourseTeachers.stream()
                .map(EliteCourseTeacherDO::getEliteCourseId)
                .distinct()
                .toList();

            if (CollUtil.isEmpty(allCourseIds)) {
                return teacherCourseCountMap; // 没有任何关联课程，直接返回
            }

            // 使用eliteCourseService查询这些课程中未被删除的课程ID列表
            List<EliteCourseDO> notDeletedCourses = eliteCourseService.lambdaQuery()
                .in(EliteCourseDO::getId, allCourseIds)
                .eq(EliteCourseDO::getDeleted, false)
                .list();

            // 提取未删除课程的ID列表
            List<Long> notDeletedCourseIds = notDeletedCourses.stream()
                .map(EliteCourseDO::getId)
                .toList();

            // 根据未删除的课程ID，计算每个讲师关联的未删除课程数量
            for (EliteCourseTeacherDO courseTeacher : allCourseTeachers) {
                Long teacherId = courseTeacher.getTeacherId();
                Long courseId = courseTeacher.getEliteCourseId();

                if (notDeletedCourseIds.contains(courseId)) {
                    // 增加讲师关联的未删除课程计数
                    int currentCount = teacherCourseCountMap.getOrDefault(teacherId, 0);
                    teacherCourseCountMap.put(teacherId, currentCount + 1);
                }
            }

            return teacherCourseCountMap;
        } catch (Exception e) {
            log.error("Error counting not-deleted courses for teachers: {}", e.getMessage(), e);
            // 发生异常时，保持默认值0
            return teacherCourseCountMap;
        }
    }

    /**
     * 批量保存课程教师
     *
     * @param teacherIds 教师 ID 列表
     * @param courseId   课程 ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<Long> teacherIds, Long courseId) {
        if (CollUtil.isEmpty(teacherIds) || Objects.isNull(courseId)) {
            return;
        }

        // 插入课程教师关联表
        List<EliteCourseTeacherDO> courseTeacherList = teacherIds
                .stream()
                .map(teacherId -> {
                    EliteCourseTeacherDO teacherDO = new EliteCourseTeacherDO();
                    teacherDO.setEliteCourseId(courseId);
                    teacherDO.setTeacherId(teacherId);
                    return teacherDO;
                })
                .toList();

        eliteCourseTeacherMapper.insertBatch(courseTeacherList);
    }

    /**
     * 根据课程ID获取讲师基本信息
     *
     * @param teacherId 课程ID
     * @return 讲师基本信息列表
     */
    @Override
    public List<TeacherBasicInfoRespVO> getBasicInfoByCourseId(Long teacherId) {

        MPJLambdaWrapper<EliteCourseTeacherDO> wrapper = new MPJLambdaWrapperX<EliteCourseTeacherDO>()
                .leftJoin(TeacherDO.class, TeacherDO::getId, EliteCourseTeacherDO::getTeacherId)
                .selectAs(TeacherDO::getId, TeacherBasicInfoRespVO::getTeacherId)
                .select(TeacherDO::getTeacherNameCn, TeacherDO::getTeacherNameEn, TeacherDO::getTeacherNameOt, TeacherDO::getAvatar, TeacherDO::getCountryCode, TeacherDO::getMobile)
                .eq(EliteCourseTeacherDO::getEliteCourseId, teacherId)
                .eq(TeacherDO::getDeleted, false)
                .orderByDesc(EliteCourseTeacherDO::getId);

        return eliteCourseTeacherMapper.selectJoinList(TeacherBasicInfoRespVO.class, wrapper);
    }
}