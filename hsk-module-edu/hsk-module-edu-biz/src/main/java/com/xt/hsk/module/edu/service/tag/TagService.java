package com.xt.hsk.module.edu.service.tag;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.module.edu.dal.dataobject.tag.TagDO;
import com.xt.hsk.framework.common.pojo.PageResult;
import jakarta.validation.Valid;
import com.xt.hsk.module.edu.controller.admin.tag.vo.TagPageReqVO;

import java.util.Collection;

/**
 * 标签 Service 接口
 *
 * <AUTHOR>
 */
public interface TagService extends IService<TagDO> {
    PageResult<TagDO> selectPage(@Valid TagPageReqVO pageReqVO);

    Collection<String> getAllTagName();

}