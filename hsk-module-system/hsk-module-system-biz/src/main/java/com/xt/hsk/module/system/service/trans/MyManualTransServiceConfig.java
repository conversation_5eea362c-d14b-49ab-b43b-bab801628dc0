package com.xt.hsk.module.system.service.trans;

import com.fhs.trans.config.TransServiceConfig;
import com.fhs.trans.service.impl.EnumTransService;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

@Configuration
@Order(Ordered.LOWEST_PRECEDENCE)
@AutoConfigureAfter(TransServiceConfig.class)
public class MyManualTransServiceConfig {

    @Bean
    public EnumTransService myEnumTransService(
            ObjectProvider<EnumTransService> otherTransServices) {
        // 通过ObjectProvider延迟获取依赖
        otherTransServices.ifAvailable((otherTransService) -> {
        }); // 确保其他实例已存在
        return new MyEnumTransService();
    }
}
