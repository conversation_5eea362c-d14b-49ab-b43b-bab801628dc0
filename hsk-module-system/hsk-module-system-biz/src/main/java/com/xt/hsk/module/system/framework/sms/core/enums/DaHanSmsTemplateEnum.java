package com.xt.hsk.module.system.framework.sms.core.enums;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.SMS_TEMPLATE_NOT_EXISTS;

import com.xt.hsk.framework.common.enums.BasicEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 大汉短信模板枚举
 *
 * <AUTHOR>
 * @since 2025/05/29
 */
@Getter
@AllArgsConstructor
public enum DaHanSmsTemplateEnum implements BasicEnum<String> {
    /**
     * 登陆验证码
     */
    LOGIN_SMS_CODE("402885d097c4b0580197ca7b26ae01f6", "PTE小程序验证码登陆", 1,
        DnHanSmsAccountEnum.INDUSTRY),
    ;

    private final String code;
    private final String desc;
    /**
     * 模板变量数量
     */
    private final int variableCount;

    /**
     * 所属账号 行业或者营销
     */
    private final DnHanSmsAccountEnum account;

    /**
     * 根据模板ID取出账号枚举
     */
    public static DnHanSmsAccountEnum getAccountByCode(String code) {
        for (DaHanSmsTemplateEnum smsTemplateEnum : values()) {
            if (smsTemplateEnum.getCode().equals(code)) {
                return smsTemplateEnum.getAccount();
            }
        }
        throw exception(SMS_TEMPLATE_NOT_EXISTS);
    }

    /**
     * 根据模板ID获取枚举
     */
    public static DaHanSmsTemplateEnum getByCode(String code) {
        for (DaHanSmsTemplateEnum smsTemplateEnum : values()) {
            if (smsTemplateEnum.getCode().equals(code)) {
                return smsTemplateEnum;
            }
        }
        throw exception(SMS_TEMPLATE_NOT_EXISTS);
    }
}
