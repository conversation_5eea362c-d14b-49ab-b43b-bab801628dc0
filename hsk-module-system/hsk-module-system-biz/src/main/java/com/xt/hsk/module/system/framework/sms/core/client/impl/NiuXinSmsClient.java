package com.xt.hsk.module.system.framework.sms.core.client.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xt.hsk.framework.common.core.KeyValue;
import com.xt.hsk.framework.common.util.http.HttpUtils;
import com.xt.hsk.module.system.framework.sms.core.client.dto.SmsReceiveRespDTO;
import com.xt.hsk.module.system.framework.sms.core.client.dto.SmsSendRespDTO;
import com.xt.hsk.module.system.framework.sms.core.client.dto.SmsTemplateRespDTO;
import com.xt.hsk.module.system.framework.sms.core.enums.SmsTemplateAuditStatusEnum;
import com.xt.hsk.module.system.framework.sms.core.property.SmsChannelProperties;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 牛信短信客户端
 * 鉴权文档：https://www.nxcloud.com/document/common/interface-contract
 * 发送文档：https://www.nxcloud.com/document/sms/mt-sending
 *
 * <AUTHOR>
 * @since 2025/07/02
 */
@Slf4j
public class NiuXinSmsClient extends AbstractSmsClient{


    private final String BASE_URL = "https://api.nxcloud.com";

    public NiuXinSmsClient(SmsChannelProperties properties) {
        super(properties);
        Assert.notEmpty(properties.getApiKey(), "apiKey 不能为空");
        Assert.notEmpty(properties.getApiSecret(), "apiSecret 不能为空");
        log.info("[NiuXinSmsClient][init] 初始化牛信短信客户端，apiKey={}", properties.getApiKey());
    }

    @Override
    public SmsSendRespDTO sendSms(String countryCode, Long logId, String mobile,
        String apiTemplateId, List<KeyValue<String, Object>> templateParams, String content) throws Throwable {
        // 1. 构建请求参数
        JSONObject bodyObj = new JSONObject();
        // 国家代码 + 手机号
        bodyObj.put("phone", countryCode + mobile);
        // 短信内容，内容最长1000个字符
        bodyObj.put("content", content);
        bodyObj.put("appKey", "0cwecmXp");

        // 2. 构建Header
        Map<String, String> headers = getHeaders();
        
        // 3. 计算签名
        String body = bodyObj.toJSONString();
        String sign = calcSign(headers, body, properties.getApiSecret());
        headers.put("sign", sign);
        
        log.info("[sendSms][牛信云准备发送] mobile={}, content={}, headers={}",
                mobile, content, JSON.toJSONString(headers));
        
        // 4. 发送请求
        String result;
        try {
            result = HttpUtils.post(BASE_URL + "/v1/sms/mt", headers, body);
            log.info("[sendSms][牛信云发送响应] mobile={}, response={}", mobile, result);
        } catch (Exception e) {
            log.error("[sendSms][牛信云发送异常] mobile={}, content={}, error={}",
                    mobile, content, e.getMessage(), e);
            throw e;
        }
        
        // 5. 解析响应
        JSONObject response = JSON.parseObject(result);
        SmsSendRespDTO smsSendRespDTO = new SmsSendRespDTO();
        
        // 判断是否成功
        int code = response.getIntValue("code");
        if (code == 0) {
            smsSendRespDTO.setSuccess(true);
            smsSendRespDTO.setApiRequestId(response.getString("requestId"));
            // 从data中获取序列号
            JSONObject data = response.getJSONObject("data");
            if (data != null) {
                String messageId = data.getString("messageid");
                smsSendRespDTO.setSerialNo(messageId);
                log.info("[sendSms][牛信云发送成功] mobile={}, messageId={}", mobile, messageId);
            }
        } else {
            smsSendRespDTO.setSuccess(false);
            smsSendRespDTO.setApiCode(String.valueOf(code));
            String message = response.getString("message");
            smsSendRespDTO.setApiMsg(message);
            log.error("[sendSms][牛信云发送失败] mobile={}, code={}, message={}", mobile, code, message);
        }
        
        return smsSendRespDTO;
    }

    @Override
    public List<SmsReceiveRespDTO> parseSmsReceiveStatus(String text) throws Throwable {
        log.info("[parseSmsReceiveStatus][开始解析] text={}", text);
        List<SmsReceiveRespDTO> list = new ArrayList<>();
        
        JSONObject response;
        try {
            response = JSON.parseObject(text);
        } catch (Exception e) {
            log.error("[parseSmsReceiveStatus][解析异常] text={}, error={}", text, e.getMessage(), e);
            throw e;
        }
        
        // 判断是否成功
        int code = response.getIntValue("code");
        if (code == 0) {
            JSONArray data = response.getJSONArray("data");
            if (data == null || data.isEmpty()) {
                log.info("[parseSmsReceiveStatus][无数据] text={}", text);
                return list;
            }
            
            // 解析每条短信回执
            for (int i = 0; i < data.size(); i++) {
                JSONObject receiptItem = data.getJSONObject(i);
                SmsReceiveRespDTO respDTO = new SmsReceiveRespDTO();
                
                // 设置是否成功
                String status = receiptItem.getString("status");
                boolean success = "DELIVERED".equals(status);
                respDTO.setSuccess(success);
                
                // 设置手机号
                String mobile = receiptItem.getString("mobile");
                respDTO.setMobile(mobile);
                
                // 设置接收时间
                String receiveTime = receiptItem.getString("receiveTime");
                if (StringUtils.isNotEmpty(receiveTime)) {
                    respDTO.setReceiveTime(DateUtil.parseLocalDateTime(receiveTime));
                }
                
                // 设置序列号
                String messageId = receiptItem.getString("messageId");
                respDTO.setSerialNo(messageId);
                
                // 设置错误码和错误信息
                if (!success) {
                    String errorCode = receiptItem.getString("errorCode");
                    String errorMsg = receiptItem.getString("errorMsg");
                    respDTO.setErrorCode(errorCode);
                    respDTO.setErrorMsg(errorMsg);
                    log.warn("[parseSmsReceiveStatus][回执失败] mobile={}, messageId={}, errorCode={}, errorMsg={}", 
                            mobile, messageId, errorCode, errorMsg);
                } else {
                    log.info("[parseSmsReceiveStatus][回执成功] mobile={}, messageId={}", mobile, messageId);
                }
                
                // 设置日志编号
                String customId = receiptItem.getString("customId");
                if (StringUtils.isNotEmpty(customId)) {
                    respDTO.setLogId(Long.valueOf(customId));
                }
                
                list.add(respDTO);
            }
        } else {
            String message = response.getString("message");
            log.error("[parseSmsReceiveStatus][解析失败] code={}, message={}", code, message);
        }
        
        return list;
    }

    @Override
    public SmsTemplateRespDTO getSmsTemplate(String apiTemplateId) throws Throwable {
        return new SmsTemplateRespDTO()
            .setId(apiTemplateId)
            .setContent("")
            .setAuditStatus(SmsTemplateAuditStatusEnum.SUCCESS.getStatus());
    }

    /**
     * 公共参数 Header
     */
    private Map<String, String> getHeaders() {
        // header参数
        Map<String, String> headers = new HashMap<>(8);
        // 用户身份标识
        headers.put("accessKey", properties.getApiKey());
        //  当前请求的时间戳（单位是毫秒)，牛信服务端允许用户端请求最大时间误差为60000毫秒
        headers.put("ts", String.valueOf(DateUtil.current()));
        //  业务类型 3表示发送短信
        headers.put("bizType", "3");
        // API接口方法
        headers.put("action", "mtsend");
        // 签名哈希算法，可用md5，sha256 默认MD5
        headers.put("algorithm", "sha256");
        return headers;
    }

    /**
     * 计算sign签名
     *
     * @param headers      请求头中的公共参数
     * @param body         body中的json字符串
     * @param accessSecret 秘钥
     * @return  sign 签名
     */
    private String calcSign(Map<String, String> headers, String body, String accessSecret) {
        StringBuilder raw = new StringBuilder();

        // step1: 拼接header参数
        raw.append("accessKey=").append(headers.get("accessKey")).append("&action=").append(headers.get("action"))
            .append("&bizType=").append(headers.get("bizType")).append("&ts=").append(headers.get("ts"));
        log.debug("[calcSign][step1] raw={}", raw);

        // step2: 拼接body参数
        if (StringUtils.isNotEmpty(body)) {
            raw.append("&body=").append(body);
            log.debug("[calcSign][step2] body添加后 raw={}", raw);
        }

        // step3: 拼接accessSecret
        raw.append("&accessSecret=").append(accessSecret);
        // 这里不打印完整的raw内容，因为包含密钥
        log.debug("[calcSign][step3] accessSecret已添加");

        // step4: SHA256算法加密,结果转换成十六进制小写
        String sign = DigestUtils.sha256Hex(raw.toString());
        log.debug("[calcSign][step4] sign={}", sign);

        return sign;
    }
}
