package com.xt.hsk.module.system.framework.sms.core.enums;

import com.dahantc.ctc.utils.MD5;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 短信账号枚举
 * <p>
 * 注意：实际使用时，账号密码应该从 SmsChannelProperties 配置中获取 这里的枚举主要用于兼容旧代码
 *
 * <AUTHOR>
 * @since 2025/05/29
 */
@Getter
@AllArgsConstructor
public enum DnHanSmsAccountEnum {

    /**
     * 行业账号
     */
    INDUSTRY("dh36525", MD5.MD5Encode("mN0heBX1")),
    /**
     * 营销
     */
    MARKETING("dh36526", MD5.MD5Encode("YBk5qKN5"));;

    /**
     * 账号
     */
    private final String account;

    /**
     * 密码
     */
    private final String password;
} 