package com.xt.hsk.module.system.framework.sms.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 大汉云短信错误码枚举
 *
 * <AUTHOR>
 * @since 2025/05/29
 */
@Getter
@AllArgsConstructor
public enum DahanSmsErrorCode {

    /**
     * 成功
     */
    SUCCESS("0", "成功"),
    FAIL("1", "失败"),
    ;

    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误描述
     */
    private final String message;

    /**
     * 判断结果是否成功
     */
    public static boolean isSuccess(String result) {
        return SUCCESS.code.equals(result);
    }
} 