package com.xt.hsk.module.system.framework.sms.core.client.impl;

import static com.xt.hsk.framework.common.util.collection.CollectionUtils.convertList;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.dahantc.sms.api.SmsTemplateClient;
import com.dahantc.sms.entity.param.template.SmsTemplateData;
import com.dahantc.sms.entity.param.template.SmsTemplateSubmitParam;
import com.dahantc.sms.entity.param.template.SmsTemplateVariable;
import com.dahantc.sms.entity.response.template.SmsTemplateSubmitResponse;
import com.xt.hsk.framework.common.core.KeyValue;
import com.xt.hsk.module.system.framework.sms.core.client.dto.SmsReceiveRespDTO;
import com.xt.hsk.module.system.framework.sms.core.client.dto.SmsSendRespDTO;
import com.xt.hsk.module.system.framework.sms.core.client.dto.SmsTemplateRespDTO;
import com.xt.hsk.module.system.framework.sms.core.enums.DaHanSmsTemplateEnum;
import com.xt.hsk.module.system.framework.sms.core.enums.DahanSmsErrorCode;
import com.xt.hsk.module.system.framework.sms.core.enums.DnHanSmsAccountEnum;
import com.xt.hsk.module.system.framework.sms.core.enums.SmsTemplateAuditStatusEnum;
import com.xt.hsk.module.system.framework.sms.core.property.SmsChannelProperties;
import java.util.LinkedList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 大汉云短信实现类 接口文档 <a href="https://help.dahantc.com/docs/oss/oss-1bkic52o2b4ih">...</a>
 *
 * <AUTHOR>
 * @since 2025/05/29
 */
@Slf4j
public class DahanSmsClient extends AbstractSmsClient {

    // 中国大陆手机区号 86
    private static final String CHINA_MAINLAND_PHONE_CODE = "86";

    public DahanSmsClient(SmsChannelProperties properties) {
        super(properties);
        Assert.notEmpty(properties.getApiKey(), "apiKey 不能为空");
        Assert.notEmpty(properties.getApiSecret(), "apiSecret 不能为空");
    }

    @Override
    public SmsSendRespDTO sendSms(String countryCode, Long logId, String mobile,
        String apiTemplateId,
        List<KeyValue<String, Object>> templateParams, String content) throws Throwable {

        log.info(
            "大汉云发送短信 countryCode = {}, logId = {}, mobile = {}, apiTemplateId = {}, templateParams = {}",
            countryCode, logId, mobile, apiTemplateId, templateParams);

        if (StringUtils.isBlank(mobile) || templateParams == null || templateParams.isEmpty()) {
            log.error("发送短信失败：手机号或模板参数为空");
            return new SmsSendRespDTO()
                .setSuccess(false)
                .setApiCode("PARAM_ERROR")
                .setApiMsg("手机号或模板参数为空");
        }

        try {
            // 使用配置中的账号密码发送短信
            SmsTemplateSubmitResponse submitResponse = sendSmsWithTemplate(mobile, apiTemplateId,
                templateParams, countryCode);

            return new SmsSendRespDTO()
                .setSuccess(DahanSmsErrorCode.isSuccess(submitResponse.getResult()))
                .setApiCode(submitResponse.getResult())
                .setApiMsg(submitResponse.getDesc())
                .setApiRequestId(submitResponse.getMsgid());

        } catch (Exception e) {
            log.error("大汉云发送短信异常", e);
            return new SmsSendRespDTO()
                .setSuccess(false)
                .setApiCode("SEND_ERROR")
                .setApiMsg("发送异常: " + e.getMessage());
        }
    }

    /**
     * 使用模板发送短信
     */
    private SmsTemplateSubmitResponse sendSmsWithTemplate(String mobile, String apiTemplateId,
        List<KeyValue<String, Object>> templateParams, String countryCode) {
        log.info("发送短信 mobile = {}, apiTemplateId = {}, templateParams = {}, countryCode = {}",
            mobile, apiTemplateId, templateParams, countryCode);

        if (StringUtils.isBlank(mobile) || StringUtils.isBlank(apiTemplateId)) {
            log.error("发送短信失败：手机号或模板ID为空");
            return new SmsTemplateSubmitResponse();
        }

        // 从配置中获取账号密码
        DnHanSmsAccountEnum smsAccountEnum = DaHanSmsTemplateEnum.getAccountByCode(apiTemplateId);

        // 第三方短信服务商要求，如果不是国内的手机号，则加上国际区号和+号。
        mobile =
            CHINA_MAINLAND_PHONE_CODE.equals(countryCode) ? mobile : "+" + countryCode + mobile;

        SmsTemplateSubmitParam param = new SmsTemplateSubmitParam();
        param.setAccount(smsAccountEnum.getAccount());
        param.setPassword(smsAccountEnum.getPassword());
        param.setPhones(mobile);

        SmsTemplateData smsTemplateData = new SmsTemplateData();
        smsTemplateData.setId(apiTemplateId);

        // 构建模板变量
        LinkedList<SmsTemplateVariable> variables = new LinkedList<>();
        for (int i = 0; i < templateParams.size(); i++) {
            SmsTemplateVariable variable = new SmsTemplateVariable();
            // 大汉云模板变量名从1开始
            variable.setName(String.valueOf(i + 1));
            variable.setValue(templateParams.get(i).getValue().toString());
            variables.add(variable);
        }
        smsTemplateData.setVariables(variables);
        param.setTemplate(smsTemplateData);

        // 创建客户端实例并发送
        SmsTemplateClient client = new SmsTemplateClient();
        SmsTemplateSubmitResponse response = client.templateSubmit(param);
        log.info("大汉云发送短信响应 = {}", JSON.toJSONString(response));
        return response;
    }

    @Override
    public List<SmsReceiveRespDTO> parseSmsReceiveStatus(String text) throws Throwable {
        JSONArray statuses = JSONUtil.parseArray(text);
        return convertList(statuses, status -> {
            cn.hutool.json.JSONObject statusObj = (JSONObject) status;
            return new SmsReceiveRespDTO()
                .setSuccess("0".equals(statusObj.getStr("result"))) // 是否接收成功
                .setErrorCode(statusObj.getStr("result")) // 状态报告编码
                .setErrorMsg(statusObj.getStr("desc")) // 状态报告说明
                .setMobile(statusObj.getStr("failPhones")) // 手机号
                .setLogId(statusObj.getLong("out_id")); // 用户序列号
        });
    }

    @Override
    public SmsTemplateRespDTO getSmsTemplate(String apiTemplateId) throws Throwable {
        if (DaHanSmsTemplateEnum.getByCode(apiTemplateId) != null) {
            return new SmsTemplateRespDTO()
                .setId(apiTemplateId)
                .setContent(DaHanSmsTemplateEnum.getByCode(apiTemplateId).getDesc())
                .setAuditStatus(SmsTemplateAuditStatusEnum.SUCCESS.getStatus());
        }
        return null;
    }
}
