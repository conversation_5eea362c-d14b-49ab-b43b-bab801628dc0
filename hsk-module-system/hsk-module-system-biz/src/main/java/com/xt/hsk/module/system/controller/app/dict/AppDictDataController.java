package com.xt.hsk.module.system.controller.app.dict;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;

import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.system.controller.app.dict.vo.AppDictDataRespVO;
import com.xt.hsk.module.system.service.dict.DictDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "用户 App - 字典数据")
@RestController
@RequestMapping("/system/dict-data")
@Validated
public class AppDictDataController {

    @Resource
    private DictDataService dictDataService;

    @GetMapping("/type")
    @Operation(summary = "根据字典类型查询字典数据信息")
    @Parameter(name = "type", description = "字典类型", required = true, example = "common_status")
    @PermitAll
    public CommonResult<List<AppDictDataRespVO>> getDictDataListByType(@RequestParam("type") String type) {
        throw new ServiceException(INTERNAL_SERVER_ERROR);
    }

}
