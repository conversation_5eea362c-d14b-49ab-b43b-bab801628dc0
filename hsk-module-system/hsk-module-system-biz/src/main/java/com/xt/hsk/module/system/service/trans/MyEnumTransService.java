package com.xt.hsk.module.system.service.trans;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.fhs.trans.service.impl.EnumTransService;
import com.fhs.trans.service.impl.TransService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 简单翻译
 */

public class MyEnumTransService extends EnumTransService {

    public static final Logger LOGGER = LoggerFactory.getLogger(MyEnumTransService.class);
    // 枚举元数据缓存：Class -> (code -> desc)
    private static final ConcurrentMap<Class<?>, Map<Object, String>> ENUM_CACHE = new ConcurrentHashMap<>();

    // 锁对象缓存，每个枚举类一个锁
    private static final ConcurrentMap<Class<?>, Object> CLASS_LOCKS = new ConcurrentHashMap<>();

    public void transOne(VO obj, List<Field> toTransList) {
        Trans tempTrans = null;
        for (Field tempField : toTransList) {
            try {
                tempTrans = tempField.getAnnotation(Trans.class);
                if (tempTrans != null && Objects.equals(tempTrans.type(), TransType.ENUM)) {
                    tempField.setAccessible(true);
                    Object fieldValue = tempField.get(obj);
                    if (fieldValue == null) {
                        continue;
                    }

                    // 获取目标枚举类
                    Class<?> enumClass = tempTrans.target();

                    // 验证是否为枚举类型
                    if (!enumClass.isEnum()) {
                        LOGGER.warn("Trans target is not an enum: {}", enumClass.getName());
                        continue;
                    }

                    // 从缓存获取枚举映射
                    Map<Object, String> enumMap = getEnumMapping(enumClass);

                    // 处理不同类型的code值
                    String descValue = null;
                    if (enumMap.containsKey(fieldValue)) {
                        descValue = enumMap.get(fieldValue);
                    } else {
                        // 尝试将fieldValue转换为字符串形式匹配
                        String stringValue = fieldValue.toString();
                        for (Map.Entry<Object, String> entry : enumMap.entrySet()) {
                            if (stringValue.equals(entry.getKey().toString())) {
                                descValue = entry.getValue();
                                break;
                            }
                        }
                    }

                    // 如果找到匹配的desc值，设置到ref字段
                    if (descValue != null) {
                        // 获取ref字段名
                        String refFieldName = tempTrans.ref();
                        if (!refFieldName.isEmpty()) {
                            setRefField(obj, refFieldName, descValue);
                        }
                    }
                }
            } catch (Exception e) {
                LOGGER.error("trans enum error", e);
            }
        }
    }

    /**
     * 获取枚举映射（线程安全）
     *
     * @param enumClass 枚举类
     * @return code->desc的映射
     */
    private Map<Object, String> getEnumMapping(Class<?> enumClass) {
        // 首先尝试从缓存获取
        Map<Object, String> enumMap = ENUM_CACHE.get(enumClass);
        if (enumMap != null) {
            return enumMap;
        }

        // 为每个枚举类创建专用锁
        Object classLock = CLASS_LOCKS.computeIfAbsent(enumClass, k -> new Object());

        // 使用类锁同步，避免多个线程同时初始化同一个枚举
        synchronized (classLock) {
            // 双重检查
            enumMap = ENUM_CACHE.get(enumClass);
            if (enumMap != null) {
                return enumMap;
            }

            // 创建新的映射
            Map<Object, String> newMap = new ConcurrentHashMap<>();
            Object[] enumConstants = enumClass.getEnumConstants();

            // 遍历枚举获取code和desc
            for (Object enumConstant : enumConstants) {
                try {
                    // 获取枚举的code字段值
                    Field codeField = getDeclaredField(enumClass, "code");
                    codeField.setAccessible(true);
                    Object codeValue = codeField.get(enumConstant);

                    // 获取枚举的desc字段值
                    Field descField = getDeclaredField(enumClass, "desc");
                    descField.setAccessible(true);
                    String descValue = (String) descField.get(enumConstant);

                    // 添加到映射
                    if (codeValue != null) {
                        newMap.put(codeValue, descValue);
                        // 同时存储字符串形式的key，方便匹配
                        newMap.put(String.valueOf(codeValue), descValue);
                    }
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    LOGGER.error("Error accessing enum fields for class: {}", enumClass.getName(), e);
                }
            }

            // 放入缓存
            ENUM_CACHE.put(enumClass, newMap);
            return newMap;
        }
    }

    /**
     * 安全获取字段（包括父类）
     */
    private Field getDeclaredField(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        Class<?> current = clazz;
        while (current != null) {
            try {
                return current.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                current = current.getSuperclass();
            }
        }
        throw new NoSuchFieldException(fieldName + " not found in " + clazz.getName());
    }

    /**
     * 设置引用字段的值
     */
    private void setRefField(VO obj, String fieldName, String value) {
        try {
            // 获取字段（包括父类）
            Field refField = getDeclaredField(obj.getClass(), fieldName);
            refField.setAccessible(true);

            // 设置字段值
            refField.set(obj, value);
        } catch (NoSuchFieldException e) {
            LOGGER.warn("Ref field not found: {}", fieldName);
        } catch (IllegalAccessException e) {
            LOGGER.error("Error setting ref field: {}", fieldName, e);
        }
    }

    @Override
    public void transMore(List<? extends VO> objList, List<Field> toTransList) {
        objList.forEach(obj -> {
            this.transOne(obj, toTransList);
        });
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet();
        TransService.registerTransType(TransType.ENUM, this);
    }


}
