package com.xt.hsk.module.system.service.sms;

import static cn.hutool.core.util.RandomUtil.randomInt;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.framework.common.util.date.DateUtils.isToday;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.SMS_CODE_ERROR;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.SMS_CODE_EXPIRED;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.SMS_CODE_NOT_FOUND;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.SMS_CODE_SEND_TOO_FAST;
import static com.xt.hsk.module.system.enums.ErrorCodeConstants.SMS_CODE_USED;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import com.xt.hsk.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import com.xt.hsk.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import com.xt.hsk.module.system.api.sms.dto.code.SmsCodeValidateReqDTO;
import com.xt.hsk.module.system.dal.dataobject.sms.SmsCodeDO;
import com.xt.hsk.module.system.dal.mysql.sms.SmsCodeMapper;
import com.xt.hsk.module.system.enums.sms.SmsSceneEnum;
import com.xt.hsk.module.system.framework.sms.config.SmsCodeProperties;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 短信验证码 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SmsCodeServiceImpl implements SmsCodeService {

    @Resource
    private SmsCodeProperties smsCodeProperties;

    @Resource
    private SmsCodeMapper smsCodeMapper;

    @Resource
    private SmsSendService smsSendService;

    @Override
    public void sendSmsCode(SmsCodeSendReqDTO reqDTO) {
        SmsSceneEnum sceneEnum = SmsSceneEnum.getCodeBySceneAndCountryCode(reqDTO.getScene(), reqDTO.getCountryCode());
        Assert.notNull(sceneEnum, "验证码场景({}) 查找不到配置", reqDTO.getScene());
        // 创建验证码
        String code = createSmsCode(reqDTO.getCountryCode(), reqDTO.getMobile(), reqDTO.getScene(),
            reqDTO.getCreateIp());
        // 发送验证码
        smsSendService.sendSingleSms(reqDTO.getCountryCode(), reqDTO.getMobile(), null, null,
                sceneEnum.getTemplateCode(), MapUtil.of("code", code));
    }

    private String createSmsCode(String countryCode, String mobile, Integer scene, String ip) {
        // 校验是否可以发送验证码，不用筛选场景
        SmsCodeDO lastSmsCode = smsCodeMapper.selectLastByMobile(countryCode, mobile, null, null);
        if (lastSmsCode != null) {
            if (LocalDateTimeUtil.between(lastSmsCode.getCreateTime(), LocalDateTime.now()).toMillis()
                    < smsCodeProperties.getSendFrequency().toMillis()) { // 发送过于频繁
                throw exception(SMS_CODE_SEND_TOO_FAST);
            }
            // 必须是今天，才能计算超过当天的上限
            if (isToday(lastSmsCode.getCreateTime()) &&
                // 超过当天发送的上限。
                lastSmsCode.getTodayIndex() >= smsCodeProperties.getSendMaximumQuantityPerDay()) {
                throw exception(SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY,
                    "今日验证码发送已达上限，请尝试密码登录");
            }
            Long conutByIp = smsCodeMapper.validateSendConutByIp(ip);
            // 超过ip地址当天发送的上限。
            if (conutByIp >= smsCodeProperties.getIpSendMaximumQuantityPerDay()) {
                throw exception(SMS_CODE_SEND_TOO_FAST);
            }
        }

        // 创建验证码记录 生成6位数验证码
        String code = String.format("%06d", randomInt(100000, 1000000));
        SmsCodeDO newSmsCode = SmsCodeDO.builder()
            .mobile(mobile)
            .code(code)
            .countryCode(countryCode)
            .scene(scene)
            .todayIndex(lastSmsCode != null && isToday(lastSmsCode.getCreateTime()) ?
                lastSmsCode.getTodayIndex() + 1 : 1)
            .createIp(ip)
            .used(false)
            .build();
        smsCodeMapper.insert(newSmsCode);
        return code;
    }

    @Override
    public void useSmsCode(SmsCodeUseReqDTO reqDTO) {
        // 检测验证码是否有效
        SmsCodeDO lastSmsCode = validateSmsCode0(reqDTO.getCountryCode(), reqDTO.getMobile(),
            reqDTO.getCode(), reqDTO.getScene());
        // 使用验证码
        smsCodeMapper.updateById(SmsCodeDO.builder().id(lastSmsCode.getId())
                .used(true).usedTime(LocalDateTime.now()).usedIp(reqDTO.getUsedIp()).build());
    }

    @Override
    public void validateSmsCode(SmsCodeValidateReqDTO reqDTO) {
        validateSmsCode0(reqDTO.getCountryCode(), reqDTO.getMobile(), reqDTO.getCode(),
            reqDTO.getScene());
    }

    private SmsCodeDO validateSmsCode0(String countryCode, String mobile, String code,
        Integer scene) {
        // 校验验证码
        SmsCodeDO lastSmsCode = smsCodeMapper.selectLastByMobile(countryCode, mobile, null, scene);
        // 若验证码不存在，抛出异常
        if (lastSmsCode == null) {
            throw exception(SMS_CODE_NOT_FOUND);
        }
        // 判断验证码是否正确
        if (!lastSmsCode.getCode().equals(code)) {
            throw exception(SMS_CODE_ERROR);
        }

        // 超过时间
        if (LocalDateTimeUtil.between(lastSmsCode.getCreateTime(), LocalDateTime.now()).toMillis()
                >= smsCodeProperties.getExpireTimes().toMillis()) { // 验证码已过期
            throw exception(SMS_CODE_EXPIRED);
        }
        // 判断验证码是否已被使用
        if (Boolean.TRUE.equals(lastSmsCode.getUsed())) {
            throw exception(SMS_CODE_USED);
        }
        return lastSmsCode;
    }

}
