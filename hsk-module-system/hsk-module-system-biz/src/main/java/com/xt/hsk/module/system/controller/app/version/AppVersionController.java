package com.xt.hsk.module.system.controller.app.version;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson.JSON;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.infra.api.config.ConfigApi;
import com.xt.hsk.module.system.controller.app.version.vo.AppVersionReqVo;
import com.xt.hsk.module.system.controller.app.version.vo.AppVersionVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "App-版本相关")
@RestController
@RequestMapping("/system/app/version")
@Validated
public class AppVersionController {
    String iosVersionConfigKey = "sys:version:ios";
    String androidVersionConfigKey = "sys:version:android";

    @Resource
    private ConfigApi configApi;

    /**
     * 获取最新版本
     */
    @SaIgnore
    @RequestMapping("/getLatestVersion")
    public CommonResult<AppVersionVo> getLatestVersion(AppVersionReqVo reqVo) {

        try {
            if (reqVo.getSource() == 1) {
                String value = configApi.getConfigValueByKey(androidVersionConfigKey);
                AppVersionVo versionVo = JSON.parseObject(value, AppVersionVo.class);
                return CommonResult.success(versionVo);
            } else {
                String value = configApi.getConfigValueByKey(iosVersionConfigKey);
                AppVersionVo versionVo = JSON.parseObject(value, AppVersionVo.class);
                return CommonResult.success(versionVo);
            }
        } catch (Exception e) {
            log.error("获取最新版本失败", e, e.getMessage());
            return CommonResult.success(new AppVersionVo());
        }

    }
}
