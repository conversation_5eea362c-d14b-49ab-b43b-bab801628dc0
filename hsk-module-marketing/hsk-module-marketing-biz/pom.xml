<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xt.hsk</groupId>
    <artifactId>hsk-module-marketing</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>hsk-module-marketing-biz</artifactId>

  <properties>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-module-system-api</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-module-marketing-api</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-module-edu-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-common</artifactId>
      <version>${revision}</version>
    </dependency>

    <!-- 业务组件 -->
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-spring-boot-starter-biz-data-permission</artifactId>
    </dependency>

    <!-- Web 相关 -->
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-spring-boot-starter-security</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <!-- DB 相关 -->
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-spring-boot-starter-mybatis</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-spring-boot-starter-redis</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-spring-boot-starter-protection</artifactId>
    </dependency>

    <!-- 定时任务 -->
    <dependency>
      <groupId>com.xt.hsk</groupId>
      <artifactId>hsk-spring-boot-starter-job</artifactId>
    </dependency>

  </dependencies>
</project>