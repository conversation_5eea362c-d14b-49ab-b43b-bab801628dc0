package com.xt.hsk.module.marketing.controller.admin.banner;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;
import static com.xt.hsk.module.marketing.enums.banner.LogRecordConstants.BANNER_CREATE_SUB_TYPE;
import static com.xt.hsk.module.marketing.enums.banner.LogRecordConstants.BANNER_CREATE_SUCCESS;
import static com.xt.hsk.module.marketing.enums.banner.LogRecordConstants.BANNER_DELETE_SUB_TYPE;
import static com.xt.hsk.module.marketing.enums.banner.LogRecordConstants.BANNER_DELETE_SUCCESS;
import static com.xt.hsk.module.marketing.enums.banner.LogRecordConstants.BANNER_DISPLAY_SUB_TYPE;
import static com.xt.hsk.module.marketing.enums.banner.LogRecordConstants.BANNER_DISPLAY_SUCCESS;
import static com.xt.hsk.module.marketing.enums.banner.LogRecordConstants.BANNER_UPDATE_SUB_TYPE;
import static com.xt.hsk.module.marketing.enums.banner.LogRecordConstants.BANNER_UPDATE_SUCCESS;

import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerCreateReqVO;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerPageReqVO;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerRespVO;
import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerUpdateReqVO;
import com.xt.hsk.module.marketing.convert.banner.BannerConvert;
import com.xt.hsk.module.marketing.dal.dataobject.banner.BannerDO;
import com.xt.hsk.module.marketing.manager.banner.BannerAdminManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理后台 - Banner 管理
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@RestController
@RequestMapping("/marketing/banner")
@Validated
public class BannerAdminController {

    @Resource
    private BannerAdminManager bannerAdminManager;

    /**
     * 创建Banner
     *
     * @param createReqVO 创建Banner请求VO
     * @return 创建的Banner编号
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('marketing:banner:create')")
    @LogRecord(type = LogRecordType.BANNER_TYPE, subType = BANNER_CREATE_SUB_TYPE, bizNo = "{{#banner.id}}", success = BANNER_CREATE_SUCCESS)
    public CommonResult<Long> createBanner(@Valid @RequestBody BannerCreateReqVO createReqVO) {
        return success(bannerAdminManager.createBanner(createReqVO));
    }

    /**
     * 更新Banner
     *
     * @param updateReqVO 更新Banner请求VO
     * @return 更新结果
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('marketing:banner:update')")
    @LogRecord(type = LogRecordType.BANNER_TYPE, subType = BANNER_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}", success = BANNER_UPDATE_SUCCESS)
    public CommonResult<Boolean> updateBanner(@Valid @RequestBody BannerUpdateReqVO updateReqVO) {
        bannerAdminManager.updateBanner(updateReqVO);
        return success(true);
    }

    /**
     * 删除Banner
     *
     * @param id Banner编号
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('marketing:banner:delete')")
    @LogRecord(type = LogRecordType.BANNER_TYPE, subType = BANNER_DELETE_SUB_TYPE, bizNo = "{{#id}}", success = BANNER_DELETE_SUCCESS)
    public CommonResult<Boolean> deleteBanner(@RequestParam("id") Long id) {
        bannerAdminManager.deleteBanner(id);
        return success(true);
    }

    /**
     * 获得Banner
     *
     * @param id Banner编号
     * @return Banner
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('marketing:banner:query')")
    @TransMethodResult
    public CommonResult<BannerRespVO> getBanner(@RequestParam("id") Long id) {
        BannerDO banner = bannerAdminManager.getBanner(id);
        return success(BannerConvert.INSTANCE.convert(banner));
    }

    /**
     * 获得Banner分页
     *
     * @param pageReqVO 分页查询
     * @return Banner分页结果
     */
    @PostMapping("/page")
    @PreAuthorize("@ss.hasPermission('marketing:banner:query')")
    @TransMethodResult
    public CommonResult<PageResult<BannerRespVO>> getBannerPage(@Valid @RequestBody BannerPageReqVO pageReqVO) {
        return success(bannerAdminManager.getBannerPage(pageReqVO));
    }

    /**
     * 更新Banner状态
     *
     * @param id Banner编号
     * @param display 是否展示，true-展示，false-隐藏
     * @return 更新结果
     */
    @PutMapping("/update-display")
    @PreAuthorize("@ss.hasPermission('marketing:banner:update')")
    @LogRecord(type = LogRecordType.BANNER_TYPE, subType = BANNER_DISPLAY_SUB_TYPE, bizNo = "{{#id}}", success = BANNER_DISPLAY_SUCCESS)
    public CommonResult<Boolean> updateBannerDisplay(@RequestParam("id") Long id, @RequestParam("display") Boolean display) {
        bannerAdminManager.updateBannerDisplay(id, display);
        return success(true);
    }
    
    /**
     * 更新Banner排序
     *
     * @param id Banner编号
     * @param sort 新的排序值
     * @return 更新结果
     */
    @PutMapping("/update-sort")
    @PreAuthorize("@ss.hasPermission('marketing:banner:update')")
    @LogRecord(type = LogRecordType.BANNER_TYPE, subType = BANNER_UPDATE_SUB_TYPE, bizNo = "{{#id}}", success = BANNER_UPDATE_SUCCESS)
    public CommonResult<Boolean> updateBannerSort(@RequestParam("id") Long id, @RequestParam("sort") Integer sort) {
        // 获取当前Banner
        BannerDO banner = bannerAdminManager.getBanner(id);
        if (banner == null) {
            return success(false);
        }
        
        // 创建更新请求，只更新排序字段
        BannerUpdateReqVO updateReqVO = new BannerUpdateReqVO();
        updateReqVO.setId(id);
        updateReqVO.setSort(sort);
        updateReqVO.setBannerName(banner.getBannerName());
        updateReqVO.setPage(banner.getPage());
        updateReqVO.setPosition(banner.getPosition());
        updateReqVO.setImageUrl(banner.getImageUrl());
        
        // 调用更新方法
        bannerAdminManager.updateBanner(updateReqVO);
        return success(true);
    }
} 