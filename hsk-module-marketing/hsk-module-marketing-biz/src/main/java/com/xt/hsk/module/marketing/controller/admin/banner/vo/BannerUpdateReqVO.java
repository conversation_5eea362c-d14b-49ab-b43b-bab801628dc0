package com.xt.hsk.module.marketing.controller.admin.banner.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Banner 更新请求 VO
 *
 * <AUTHOR>
 * @since 2025/06/18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BannerUpdateReqVO extends BannerBaseVO {

    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;
} 