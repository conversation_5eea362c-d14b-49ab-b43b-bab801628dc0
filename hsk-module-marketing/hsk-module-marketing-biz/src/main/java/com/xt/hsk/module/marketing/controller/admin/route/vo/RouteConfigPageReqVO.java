package com.xt.hsk.module.marketing.controller.admin.route.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 路由配置分页查询请求VO
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RouteConfigPageReqVO extends PageParam {

    /**
     * 页面名称，模糊匹配
     */
    private String pathName;

    /**
     * 是否显示
     */
    private Integer isShow;


    /**
     * iOS页面路径
     */
    private String iosPath;

    /**
     * Android页面路径
     */
    private String androidPath;

    /**
     * H5页面路径
     */
    private String h5Path;
} 