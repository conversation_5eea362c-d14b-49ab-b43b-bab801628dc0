package com.xt.hsk.module.marketing.controller.admin.route.vo;

import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 路由配置响应VO，用于返回给前端的数据
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RouteConfigRespVO extends RouteConfigBaseVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 