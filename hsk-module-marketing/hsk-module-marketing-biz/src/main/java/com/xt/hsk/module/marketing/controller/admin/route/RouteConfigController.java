package com.xt.hsk.module.marketing.controller.admin.route;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.route.vo.RouteConfigCreateReqVO;
import com.xt.hsk.module.marketing.controller.admin.route.vo.RouteConfigPageReqVO;
import com.xt.hsk.module.marketing.controller.admin.route.vo.RouteConfigRespVO;
import com.xt.hsk.module.marketing.controller.admin.route.vo.RouteConfigUpdateReqVO;
import com.xt.hsk.module.marketing.convert.route.RouteConfigConvert;
import com.xt.hsk.module.marketing.dal.dataobject.banner.RouteConfigDO;
import com.xt.hsk.module.marketing.manager.banner.RouteConfigAdminManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 后台管理 - 路由配置接口
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@RestController
@RequestMapping("/marketing/route-config")
@Validated
public class RouteConfigController {

    @Resource
    private RouteConfigAdminManager routeConfigAdminManager;

    /**
     * 创建路由配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('marketing:route-config:create')")
    public CommonResult<Long> createRouteConfig(
        @Valid @RequestBody RouteConfigCreateReqVO createReqVO) {
        Long id = routeConfigAdminManager.createRouteConfig(createReqVO);
        return success(id);
    }

    /**
     * 更新路由配置
     *
     * @param updateReqVO 更新信息
     * @return 是否成功
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('marketing:route-config:update')")
    public CommonResult<Boolean> updateRouteConfig(
        @Valid @RequestBody RouteConfigUpdateReqVO updateReqVO) {
        routeConfigAdminManager.updateRouteConfig(updateReqVO);
        return success(true);
    }

    /**
     * 删除路由配置
     *
     * @param id 编号
     * @return 是否成功
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('marketing:route-config:delete')")
    public CommonResult<Boolean> deleteRouteConfig(@RequestParam("id") Long id) {
        routeConfigAdminManager.deleteRouteConfig(id);
        return success(true);
    }

    /**
     * 获得路由配置详情
     *
     * @param id 编号
     * @return 路由配置详情
     */
    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('marketing:route-config:query')")
    public CommonResult<RouteConfigRespVO> getRouteConfig(@RequestParam("id") Long id) {
        RouteConfigDO routeConfig = routeConfigAdminManager.getRouteConfig(id);
        return success(RouteConfigConvert.INSTANCE.convert(routeConfig));
    }

    /**
     * 获得路由配置分页
     *
     * @param pageVO 分页查询
     * @return 路由配置分页
     */
    @PostMapping("/page")
    @PreAuthorize("@ss.hasPermission('marketing:route-config:query')")
    public CommonResult<PageResult<RouteConfigRespVO>> getRouteConfigPage(
        @Valid @RequestBody RouteConfigPageReqVO pageVO) {
        PageResult<RouteConfigDO> pageResult = routeConfigAdminManager.getRouteConfigPage(pageVO);
        return success(RouteConfigConvert.INSTANCE.convertPage(pageResult));
    }
} 