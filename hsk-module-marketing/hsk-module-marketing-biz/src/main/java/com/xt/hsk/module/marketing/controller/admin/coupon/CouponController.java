package com.xt.hsk.module.marketing.controller.admin.coupon;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理后台 - 优惠券
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 优惠券")
@RestController
@RequestMapping("/marketing/coupon")
@Validated
@Slf4j
public class CouponController {

    // API实现将在后续添加
} 