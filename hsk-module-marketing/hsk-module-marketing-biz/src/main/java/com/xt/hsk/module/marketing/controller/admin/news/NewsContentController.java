package com.xt.hsk.module.marketing.controller.admin.news;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;
import static com.xt.hsk.module.marketing.enums.LogRecordConstants.NEWS_CONTENT_DELETE_SUB_TYPE;
import static com.xt.hsk.module.marketing.enums.LogRecordConstants.NEWS_CONTENT_DELETE_SUCCESS;
import static com.xt.hsk.module.marketing.enums.LogRecordConstants.NEWS_CONTENT_TYPE;
import static com.xt.hsk.module.marketing.enums.LogRecordConstants.NEWS_CONTENT_UPDATE_SUB_TYPE;
import static com.xt.hsk.module.marketing.enums.LogRecordConstants.NEWS_CONTENT_UPDATE_SUCCESS;

import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsContentPageReqVO;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsContentPageRespVO;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsContentSaveOrUpdateVO;
import com.xt.hsk.module.marketing.manager.news.NewsContentManager;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理后台 - 资讯内容
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@RestController
@RequestMapping("/marketing/news/content")
@Validated
public class NewsContentController {

    @Resource
    private NewsContentManager newsContentManager;

    /**
     * 分页查询资讯内容
     */
    @PostMapping("/getContentPage")
    @PreAuthorize("@ss.hasPermission('marketing:news:query')")
    public CommonResult<PageResult<NewsContentPageRespVO>> getContentPage(@Validated
    @RequestBody NewsContentPageReqVO reqVO) {
        PageResult<NewsContentPageRespVO> pageResult = newsContentManager.getNewsContentPage(reqVO);
        return success(pageResult);
    }

    /**
     * 删除资讯内容
     */
    @LogRecord(type = NEWS_CONTENT_TYPE, subType = NEWS_CONTENT_DELETE_SUB_TYPE, bizNo = "{{#news.id}}",success = NEWS_CONTENT_DELETE_SUCCESS)
    @PostMapping("/deleteContent")
    @PreAuthorize("@ss.hasPermission('marketing:news:update')")
    public CommonResult<Boolean> deleteContent(@RequestBody Long id) {
        newsContentManager.deleteContent(id);
        return success(true);
    }

    /**
     * 创建或更新资讯内容
     */
    @LogRecord(type = NEWS_CONTENT_TYPE, subType = NEWS_CONTENT_UPDATE_SUB_TYPE, bizNo = "{{#news.id}}", success = NEWS_CONTENT_UPDATE_SUCCESS)
    @PostMapping("/createOrUpdateContent")
    @PreAuthorize("@ss.hasPermission('marketing:news:update')")
    public CommonResult<Boolean> createOrUpdateContent(@Validated @RequestBody
    NewsContentSaveOrUpdateVO updateReqVO, Long newsId) {
        newsContentManager.createOrUpdateContent(updateReqVO, newsId);
        return success(true);
    }
}
