package com.xt.hsk.module.marketing.controller.admin.news;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;
import static com.xt.hsk.module.marketing.enums.LogRecordConstants.NEWS_CREATE_SUB_TYPE;
import static com.xt.hsk.module.marketing.enums.LogRecordConstants.NEWS_CREATE_SUCCESS;
import static com.xt.hsk.module.marketing.enums.LogRecordConstants.NEWS_DELETE_SUB_TYPE;
import static com.xt.hsk.module.marketing.enums.LogRecordConstants.NEWS_DELETE_SUCCESS;
import static com.xt.hsk.module.marketing.enums.LogRecordConstants.NEWS_TYPE;
import static com.xt.hsk.module.marketing.enums.LogRecordConstants.NEWS_UPDATE_SUB_TYPE;
import static com.xt.hsk.module.marketing.enums.LogRecordConstants.NEWS_UPDATE_SUCCESS;

import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsPageReqVO;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsRespVO;
import com.xt.hsk.module.marketing.controller.admin.news.vo.NewsSaveReqVO;
import com.xt.hsk.module.marketing.convert.news.NewsConvert;
import com.xt.hsk.module.marketing.dal.dataobject.news.NewsDO;
import com.xt.hsk.module.marketing.manager.news.NewsManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理后台 - 资讯
 *
 * <AUTHOR>
 * @since 2025/06/27
 */
@RestController
@RequestMapping("/marketing/news")
@Validated
public class NewsAdminController {

    @Resource
    private NewsManager newsManager;

    /**
     * 创建资讯
     * @param createReqVO 创建参数
     * @return 资讯ID
     */
    @LogRecord(type = NEWS_TYPE, subType = NEWS_CREATE_SUB_TYPE, bizNo = "{{#news.id}}",success = NEWS_CREATE_SUCCESS)
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('marketing:news:create')")
    public CommonResult<Long> createNews(@Valid @RequestBody NewsSaveReqVO createReqVO) {
        return success(newsManager.createNews(createReqVO));
    }

    /**
     * 更新资讯
     * @param updateReqVO 更新参数
     * @return 更新结果
     */
    @LogRecord(type = NEWS_TYPE, subType = NEWS_UPDATE_SUB_TYPE, bizNo = "{{#news.id}}",success = NEWS_UPDATE_SUCCESS)
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('marketing:news:update')")
    public CommonResult<Boolean> updateNews(@Valid @RequestBody NewsSaveReqVO updateReqVO) {
        newsManager.updateNews(updateReqVO);
        return success(true);
    }

    /**
     * 删除资讯
     * @param id 资讯ID
     * @return 删除结果
     */
    @LogRecord(type = NEWS_TYPE, subType = NEWS_DELETE_SUB_TYPE, bizNo = "{{#id}}",success = NEWS_DELETE_SUCCESS)
    @PostMapping("/delete")
    @PreAuthorize("@ss.hasPermission('marketing:news:delete')")
    public CommonResult<Boolean> deleteNews(@RequestParam("id") Long id) {
        newsManager.deleteNews(id);
        return success(true);
    }

    /**
     * 获得资讯
     * @param id 资讯ID
     * @return 资讯
     */
    @PostMapping("/get")
    @PreAuthorize("@ss.hasPermission('marketing:news:query')")
    public CommonResult<NewsRespVO> getNews(@RequestParam("id") Long id) {
        NewsDO news = newsManager.getNews(id);
        return success(NewsConvert.INSTANCE.convert(news));
    }

    /**
     * 获得资讯分页
     * @param pageReqVO 分页参数
     * @return 资讯分页
     */
    @PostMapping("/page")
    @PreAuthorize("@ss.hasPermission('marketing:news:query')")
    public CommonResult<PageResult<NewsRespVO>> getNewsPage(@Valid
    @RequestBody NewsPageReqVO pageReqVO) {
        PageResult<NewsRespVO> pageResult = newsManager.getNewsPage(pageReqVO);
        return success(pageResult);
    }


    /**
     * 查询当前精选资讯数量
     */
    @PostMapping("/getFeaturedNewsCount")
    @PreAuthorize("@ss.hasPermission('marketing:news:query')")
    public CommonResult<Integer> getFeaturedNewsCount() {
        return success(newsManager.getFeaturedNewsList().size());
    }

}
