package com.xt.hsk.module.marketing.controller.admin.banner.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.xt.hsk.module.marketing.dal.dataobject.banner.RouteConfigDO;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Banner 响应 VO，用于返回给前端的数据
 *
 * <AUTHOR>
 * @since 2025/07/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BannerRespVO extends BannerBaseVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 最近更新人名称
     */
    private String updaterName;

    /**
     * 创建者id
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "creatorName")
    private String creator;

    /**
     * 更新者id
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "updaterName")
    private String updater;

    /**
     * 关联的页面路由ID {}
     */
    @Trans(type = TransType.SIMPLE, target = RouteConfigDO.class, fields = {"pathName","iosPath","androidPath","h5Path"},refs = {"pathName","iosPath","androidPath","h5Path"})
    private Integer jumpRouteId;

    /**
     * 页面名称
     */
    private String pathName;

    /**
     * iOS页面路径
     */
    private String iosPath;

    /**
     * Android页面路径
     */
    private String androidPath;

    /**
     * H5页面路径
     */
    private String h5Path;

    /**
     * 轮播图地址
     */
    private String imageUrl;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 点击显示图片地址
     */
    private String jumpImageUrl;

    /**
     * 外部链接地址
     */
    private String jumpLinkUrl;

    /**
     * 路由参数值，例如：{"course_id": 12345}
     */
    private Object jumpRouteParams;

    /**
     * 弹窗内容
     */
    private String jumpPopup;

    /**
     * 展示状态：1-立即展示, 2-定时展示, 3-隐藏
     */
    private Integer displayStatus;

    /**
     * 展示时间 display_status = 2必填
     */
    private LocalDateTime displayTime;
}