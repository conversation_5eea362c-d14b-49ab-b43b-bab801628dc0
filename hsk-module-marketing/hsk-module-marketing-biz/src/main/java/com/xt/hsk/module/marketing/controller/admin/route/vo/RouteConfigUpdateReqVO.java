package com.xt.hsk.module.marketing.controller.admin.route.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 路由配置更新请求VO
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RouteConfigUpdateReqVO extends RouteConfigBaseVO {

    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;
} 