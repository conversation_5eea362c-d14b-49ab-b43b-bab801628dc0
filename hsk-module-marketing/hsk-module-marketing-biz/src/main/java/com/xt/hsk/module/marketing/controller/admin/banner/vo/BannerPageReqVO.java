package com.xt.hsk.module.marketing.controller.admin.banner.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Banner分页查询请求VO
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BannerPageReqVO extends PageParam {

    /**
     * Banner名称
     */
    private String bannerName;

    /**
     * 页面 - 见BannerPageEnum枚举 1-练习 2-题型练习列表页 3-课程 4-我的
     */
    private Integer page;

    /**
     * 位置 - 见BannerPositionEnum枚举 1-顶部 2-中部 3-底部
     */
    private Integer position;

    /**
     * 展示状态 - 见BannerDisplayStatusEnum枚举 1-立即展示 2-定时展示 3-隐藏
     */
    private Integer displayStatus;
} 