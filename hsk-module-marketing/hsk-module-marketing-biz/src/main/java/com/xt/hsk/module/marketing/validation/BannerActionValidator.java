package com.xt.hsk.module.marketing.validation;

import com.xt.hsk.module.marketing.controller.admin.banner.vo.BannerBaseVO;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

/**
 * Banner动作校验器 校验点击显示图片、跳转外部链接、跳转APP内页面、打开弹窗这四项只能填写一项
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
public class BannerActionValidator implements
    ConstraintValidator<BannerActionConstraint, BannerBaseVO> {

    @Override
    public void initialize(BannerActionConstraint constraintAnnotation) {
        // 初始化，不需要做任何事
    }

    @Override
    public boolean isValid(BannerBaseVO banner, ConstraintValidatorContext context) {
        if (banner == null) {
            return true;
        }

        // 计算填写项的数量
        int filledCount = 0;

        // 检查jumpImageUrl是否填写
        if (StringUtils.isNotBlank(banner.getJumpImageUrl())) {
            filledCount++;
        }

        // 检查jumpLinkUrl是否填写
        if (StringUtils.isNotBlank(banner.getJumpLinkUrl())) {
            filledCount++;
        }

        // 检查jumpRouteId是否填写
        if (banner.getJumpRouteId() != null) {
            filledCount++;
        }

        // 检查jumpPopup是否填写
        if (StringUtils.isNotBlank(banner.getJumpPopup())) {
            filledCount++;
        }

        // 如果填写项超过1个，则不通过校验
        if (filledCount > 1) {
            setCustomMessage(context,
                "点击显示图片、跳转外部链接、跳转APP内页面、打开弹窗这四项只能填写一项");
            return false;
        }

        // 如果jumpRouteParams存在但没有jumpRouteId，则不通过校验
        if (banner.getJumpRouteParams() != null && banner.getJumpRouteId() == null) {
            setCustomMessage(context, "如果填写了路由参数，则必须填写页面路由ID");
            return false;
        }

        return true;
    }

    /**
     * 设置自定义错误信息
     */
    private void setCustomMessage(ConstraintValidatorContext context, String message) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message)
            .addConstraintViolation();
    }
} 