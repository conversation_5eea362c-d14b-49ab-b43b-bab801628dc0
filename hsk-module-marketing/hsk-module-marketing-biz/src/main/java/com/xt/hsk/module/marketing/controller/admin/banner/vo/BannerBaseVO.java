package com.xt.hsk.module.marketing.controller.admin.banner.vo;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.marketing.enums.banner.BannerPageEnum;
import com.xt.hsk.module.marketing.enums.banner.BannerPositionEnum;
import com.xt.hsk.module.marketing.validation.BannerActionConstraint;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * Banner 基础 VO，提供给添加、修改、详情共用
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Data
@BannerActionConstraint
public class BannerBaseVO implements VO {

    /**
     * Banner名称
     */
    @NotBlank(message = "Banner名称不能为空")
    @Size(max = 64, message = "Banner名称长度不能超过64")
    private String bannerName;

    /**
     * 页面 - 见BannerPageEnum枚举 1-练习 2-题型练习列表页 3-课程 4-我的
     * @see BannerPageEnum
     */
    @InEnum(BannerPageEnum.class)
    @NotNull(message = "页面不能为空")
    private Integer page;

    /**
     * 所属位置 - 见BannerPositionEnum枚举 1-顶部 2-中部 3-底部
     */
    @InEnum(BannerPositionEnum.class)
    @NotNull(message = "所属位置不能为空")
    private Integer position;

    /**
     * 轮播图地址
     */
    @NotBlank(message = "轮播图地址不能为空")
    private String imageUrl;

    /**
     * 排序值，默认为1，不可为负数或0
     */
    @Min(value = 1, message = "排序值不能小于1")
    private Integer sort = 1;

    /**
     * 点击显示图片地址
     */
    private String jumpImageUrl;

    /**
     * 外部链接地址
     */
    private String jumpLinkUrl;

    /**
     * 关联的页面路由ID
     */
    private Integer jumpRouteId;

    /**
     * 路由参数值 JSON格式，例如：{"course_id": 12345} 当actionType=4时选填
     * - examId 模考ID
     * - gameType 游戏类型 1单词连连看 2笔画书写 3连词成句 4卡拉ok 5汉越词
     * - subject 科目 1 听力 2 阅读 4 书写
     * - hskLevel hsk等级
     * - courseId 精品课ID 或者直播课ID
     */
    private Object jumpRouteParams;

    /**
     * 弹窗内容
     */
    private String jumpPopup;

    /**
     * 展示状态 - 见BannerDisplayStatusEnum枚举 1-立即展示 2-定时展示 3-隐藏
     */
    @NotNull(message = "展示状态不能为空")
    private Integer displayStatus;

    /**
     * 展示时间 当displayStatus=2时必填
     */
    private LocalDateTime displayTime;
} 