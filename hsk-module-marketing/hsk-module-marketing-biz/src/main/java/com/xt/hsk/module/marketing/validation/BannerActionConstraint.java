package com.xt.hsk.module.marketing.validation;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * Banner动作约束注解，验证点击动作相关字段 - 点击显示的图片 - 点击跳转外部链接 - 点击跳转APP内页面 - 点击打开弹窗 上述四项只能填写一项，不能同时填写多项
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Target({TYPE})
@Retention(RUNTIME)
@Constraint(validatedBy = BannerActionValidator.class)
@Documented
public @interface BannerActionConstraint {

    String message() default "点击显示图片、跳转外部链接、跳转APP内页面、打开弹窗这四项只能填写一项";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
} 