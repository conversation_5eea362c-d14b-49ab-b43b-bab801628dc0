package com.xt.hsk.module.marketing.controller.admin.route.vo;

import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.validation.InEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 路由配置基础VO，提供给添加、修改、详情共用
 *
 * <AUTHOR>
 * @since 2025/06/17
 */
@Data
public class RouteConfigBaseVO {

    /**
     * 页面名称
     */
    @NotBlank(message = "页面名称不能为空")
    private String pathName;

    /**
     * iOS页面路径
     */
    private String iosPath;

    /**
     * Android页面路径
     */
    private String androidPath;

    /**
     * H5页面路径
     */
    private String h5Path;

    /**
     * 是否显示：0-隐藏 1-显示 见 RouteShowEnum 枚举
     */
    @InEnum(IsShowEnum.class)
    @NotNull(message = "是否显示不能为空")
    private Integer isShow;
} 